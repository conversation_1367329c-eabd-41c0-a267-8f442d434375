# Image Upload Progress Implementation

## Problem Solved

After implementing deferred image upload for faster contact creation, users lost visual feedback about when their images were being uploaded in the background. The trajectory summary screen no longer showed upload progress, leaving users unsure if their images were being processed.

## Solution Implemented

### 1. Enhanced Monitoring Sync Service

**Added Progress Callback System:**
```typescript
class MonitoringSyncService {
  private progressCallback: ((progress: number, message: string) => void) | null = null;

  // Set progress callback for UI updates
  setProgressCallback(callback: ((progress: number, message: string) => void) | null) {
    this.progressCallback = callback;
  }

  // Report progress to UI
  private reportProgress(progress: number, message: string) {
    if (this.progressCallback) {
      this.progressCallback(progress, message);
    }
  }
}
```

**Progress Reporting During Sync:**
- **0-5%**: Initial sync setup
- **5-10%**: Syncing sessions and GPS points  
- **10-70%**: Image upload progress (dynamic based on number of contacts/images)
- **70-100%**: Final sync completion
- **100%**: "Sincroniza<PERSON> concluída!" message

### 2. Smart Image Detection

**Added hasImagesToSync() method:**
```typescript
private async hasImagesToSync(): Promise<boolean> {
  // Check both technician and gestor contact events
  // Only show progress if there are actually images to upload
}
```

**Benefits:**
- Progress modal only appears when there are images to sync
- Avoids unnecessary UI disruption for trajectories without images
- Checks both technician and gestor contact storage

### 3. Dynamic Progress Messages

**Real-time Progress Updates:**
- `"A iniciar sincronização de fotos..."`
- `"A sincronizar sessões..."`
- `"A sincronizar pontos GPS..."`
- `"A carregar fotos do contacto 1..."`
- `"A carregar foto 2/3 do contacto 1..."`
- `"Sincronização concluída!"`

### 4. UI Integration

**Connected to Both Monitoring Components:**
- `ActiveMonitoring.tsx` (Technician monitoring)
- `GestorActiveMonitoring.tsx` (Hunt manager monitoring)

**Progress Modal Features:**
- Shows when background sync has images to upload
- Real-time progress bar (0-100%)
- Dynamic status messages
- Automatic dismissal when complete
- Non-blocking (users can still navigate)

## Technical Implementation

### Files Modified

1. **`services/monitoringSyncService.ts`**
   - Added progress callback system
   - Enhanced image upload with progress reporting
   - Added smart image detection

2. **`components/ActiveMonitoring.tsx`**
   - Connected to sync service progress callbacks
   - Enhanced progress modal with dynamic messages
   - Added upload message state

3. **`components/GestorActiveMonitoring.tsx`**
   - Same enhancements as ActiveMonitoring
   - Consistent progress reporting across user types

### Progress Calculation Logic

```typescript
// Base progress allocation:
// 0-10%: Initial sync (sessions, GPS)
// 10-70%: Image uploads (60% total)
// 70-100%: Completion

// Per contact image progress:
const imageProgress = 10 + (contactIndex / totalContacts) * 60 + 
                     (imageIndex / imagesInContact) * (60 / totalContacts);
```

## User Experience Improvements

### Before (Issue)
- ❌ No feedback when images were uploading in background
- ❌ Users unsure if images were being processed
- ❌ No indication of sync progress
- ❌ Users might think images were lost

### After (Solution)
- ✅ Clear progress indication when images are syncing
- ✅ Real-time status messages showing current activity
- ✅ Progress bar showing completion percentage
- ✅ Automatic dismissal when sync is complete
- ✅ Only appears when there are actually images to sync

## Testing Scenarios

### Test Case 1: Trajectory with Images
1. Create trajectory with contacts containing images
2. Complete trajectory (shows summary)
3. When internet available, progress modal should appear
4. Progress should show: 0% → 10% → [image upload progress] → 100%
5. Messages should update dynamically
6. Modal should auto-dismiss when complete

### Test Case 2: Trajectory without Images
1. Create trajectory with contacts but no images
2. Complete trajectory
3. Progress modal should NOT appear
4. Background sync should still occur silently

### Test Case 3: Multiple Contacts with Images
1. Create trajectory with multiple contacts, each with images
2. Progress should show individual contact processing
3. Messages should indicate which contact/image is being processed

## Benefits

1. **User Confidence**: Users know their images are being processed
2. **Transparency**: Clear indication of what's happening in background
3. **Performance**: Non-blocking progress indication
4. **Smart**: Only shows when necessary (images present)
5. **Detailed**: Specific messages about current activity
6. **Consistent**: Same experience across technician and gestor users

## Future Enhancements

1. **Retry Mechanism**: Show retry options if upload fails
2. **Offline Queue**: Show queued images waiting for internet
3. **Upload Statistics**: Show total images uploaded/remaining
4. **Background Notifications**: System notifications for long uploads 