# Weather Functionality Setup Guide

This guide explains how to set up weather data collection for the ColaboradorReport feature with enhanced bird-watching specific parameters.

## Overview

The weather functionality automatically fetches comprehensive weather conditions based on the user's GPS location when creating a report. This includes standard meteorological data plus bird-watching specific information:

### Standard Weather Data
- Temperature and "feels like" temperature
- Weather description (e.g., "clear sky", "light rain")
- Humidity and atmospheric pressure
- Wind speed, direction, and gusts
- Visibility and cloud coverage

### Bird-Watching Specific Data
- **UV Index** - Important for outdoor observation comfort and safety
- **Sunrise/Sunset times** - Critical for planning bird watching sessions
- **Moon phase** - Affects nocturnal bird behavior and migration patterns
- **Barometric pressure trends** - Birds are sensitive to pressure changes
- **Wind conditions assessment** - Categorized for bird watching impact
- **Overall bird watching conditions** - AI assessment of weather suitability

## Setup Instructions

### 1. Get OpenWeatherMap API Key

1. Visit [OpenWeatherMap API](https://openweathermap.org/api)
2. Sign up for a free account
3. Go to your dashboard and copy your API key
4. The free tier includes:
   - 1,000 API calls per day
   - Current weather data
   - One Call API 3.0 (enhanced features)
   - 5-day weather forecast
   - Weather maps

### 2. Configure the API Key

1. Open `config/weather.ts`
2. Replace the empty `API_KEY` string with your actual API key:

```typescript
export const WEATHER_CONFIG = {
  // Add your OpenWeatherMap API key here
  API_KEY: 'your_actual_api_key_here', // Replace with your key
  
  // ... rest of config
};
```

3. Save the file and restart the app

### 3. Test the Functionality

1. Open the app and navigate to the report screen
2. The weather section should appear automatically
3. If you see "A obter dados meteorológicos..." (Fetching weather data), the system is working
4. You should see comprehensive weather data including:
   - Temperature with "feels like"
   - Bird watching conditions assessment
   - UV index and moon phase
   - Sunrise/sunset times
   - Wind conditions for bird watching

## Enhanced Features for Bird Watching

### Bird Watching Conditions Assessment

The app automatically evaluates weather conditions for bird watching and provides assessments like:
- "Temperatura ideal, Vento calmo, Excelente visibilidade"
- "Vento forte, Pressão a descer (tempo a piorar)"
- "Condições normais"

### Wind Condition Analysis

Wind conditions are specifically categorized for bird watching:
- **Calmo (0-1 m/s)**: Ideal for observation
- **Brisa leve (1-3 m/s)**: Good for observation
- **Brisa moderada (3-6 m/s)**: Normal conditions
- **Vento moderado (6-10 m/s)**: May affect small birds
- **Vento forte (10-15 m/s)**: Birds may seek shelter
- **Vento muito forte (>15 m/s)**: Reduced bird activity

### UV Index Information

UV levels are provided with descriptions:
- **0-2**: Baixo (Low)
- **3-5**: Moderado (Moderate)
- **6-7**: Alto (High)
- **8-10**: Muito Alto (Very High)
- **11+**: Extremo (Extreme)

### Moon Phase Tracking

Moon phases are calculated and displayed in Portuguese:
- Lua Nova (New Moon)
- Lua Crescente (Waxing Crescent)
- Quarto Crescente (First Quarter)
- Lua Cheia (Full Moon)
- Quarto Minguante (Last Quarter)
- Lua Minguante (Waning Crescent)

## API Usage

### Enhanced API (One Call 3.0)

The app first attempts to use OpenWeatherMap's One Call API 3.0 for comprehensive data including:
- UV index
- Detailed atmospheric conditions
- Enhanced wind data including gusts
- Dew point temperature

### Fallback API (Current Weather)

If the One Call API is unavailable, the app falls back to the basic Current Weather API, which still provides:
- Basic weather conditions
- Temperature and humidity
- Wind speed and direction
- Pressure and visibility
- Calculated moon phase and bird watching assessment

## Data Storage and Display

### In Reports
Weather data is stored with each report and includes all collected parameters. When viewing reports, you'll see:
- Main weather summary with temperature and conditions
- Barometric pressure trend
- Enhanced wind information including gusts
- UV index and moon phase (when available)

### Historical Value
Storing comprehensive weather data with bird sightings helps build a database of:
- Optimal weather conditions for specific bird species
- Seasonal patterns and weather correlations
- Migration timing related to atmospheric conditions

## Troubleshooting

### "OpenWeatherMap API key not configured"
- Check that you've added your API key to `config/weather.ts`
- Ensure the key is a valid string (not empty)

### Limited weather data displayed
- If you only see basic weather info, the One Call API may not be available
- Check your API key has access to One Call API 3.0
- The app will still function with basic weather data

### "Permissão de localização necessária"
- Grant location permissions to the app
- Ensure GPS is enabled on the device

### "Erro ao obter dados meteorológicos"
- Check internet connection
- Verify API key is correct and active
- Check if you've exceeded the daily API limit (1,000 calls for free tier)

## Privacy and Data Usage

- Weather data is fetched based on GPS coordinates only
- No personal location data is sent to OpenWeatherMap
- Weather data is stored with reports for scientific and historical reference
- Enhanced weather data helps improve bird watching success rates

## Scientific Value

The comprehensive weather data collection serves multiple purposes:
1. **Immediate**: Helps observers understand current conditions affecting bird behavior
2. **Historical**: Builds a database of weather conditions during bird sightings
3. **Research**: Contributes to understanding weather patterns and bird activity correlations
4. **Conservation**: Helps identify optimal conditions for different species observations

This enhanced weather functionality makes the app particularly valuable for serious bird watchers and ornithological research.

## Features

### Automatic Weather Fetching

- Weather data is fetched automatically when the report screen loads
- Uses the device's current GPS location
- Displays a loading indicator while fetching
- Shows error message with retry option if fetch fails

### Weather Display

The weather section shows:
- **Main info**: Temperature and description with weather icon
- **Details**: Humidity, pressure, wind speed/direction, visibility, cloudiness

### Data Storage

Weather data is stored with each report and includes:
- All weather parameters
- Timestamp of when the data was fetched
- Can be viewed later in the reports list

## API Limits

The free OpenWeatherMap tier includes:
- **1,000 calls per day** - sufficient for most reporting needs
- **60 calls per minute** - rate limiting
- **Current weather data** - what we use for reports

If you need more calls, consider upgrading to a paid plan.

## Privacy Notes

- Weather data is fetched based on GPS coordinates
- No personal location data is sent to OpenWeatherMap
- Weather data is stored with reports for historical reference
- Users can see weather conditions when viewing past reports

## Technical Details

### Files Modified

- `services/weatherService.ts` - Weather API service
- `config/weather.ts` - Configuration file
- `types/reports.ts` - Added weather data types
- `components/reports/ColaboradorReport.tsx` - Added weather UI
- `app/report.tsx` - Updated to include weather in submissions
- `app/(tabs)/reports.tsx` - Added weather display in reports list

### API Endpoint

The service uses OpenWeatherMap's Current Weather API:
```
https://api.openweathermap.org/data/2.5/weather
```

### Parameters Used

- `lat`, `lon` - GPS coordinates
- `appid` - Your API key
- `units=metric` - Celsius temperature
- `lang=pt` - Portuguese language for descriptions

## Support

If you encounter issues:

1. Check the console for error messages
2. Verify your API key is active on OpenWeatherMap dashboard
3. Ensure location permissions are granted
4. Test with a simple API call to verify your key works

For OpenWeatherMap API support, visit their [documentation](https://openweathermap.org/api/one-call-api) or [FAQ](https://openweathermap.org/faq). 