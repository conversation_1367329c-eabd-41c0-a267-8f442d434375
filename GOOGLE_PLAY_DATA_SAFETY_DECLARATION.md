# Google Play Data Safety Declaration for ProROLA

## Overview
This document provides the exact data safety declaration needed for Google Play Console to fix the "Invalid Data safety form" rejection. Google detected that your app collects/transmits user data that wasn't properly declared.

## Required Data Safety Form Responses

### 1. Data Collection and Security Section

**"Does your app collect or share any of the required user data types?"**
**Answer**: YES

**"Is all of the user data collected by your app encrypted in transit?"**
**Answer**: YES

**"Do you provide a way for users to request that their data is deleted?"**
**Answer**: YES

### 2. Data Types Your App Collects

#### PERSONAL INFO
✅ **Name**
- **Purpose**: Account management, User identification in scientific reports
- **Collection**: Required for account creation
- **Sharing**: Shared with ICNF for research purposes

✅ **Email address** 
- **Purpose**: Account management, Authentication, Communication
- **Collection**: Required for account creation and login
- **Sharing**: Not shared with third parties

#### LOCATION
✅ **Approximate location**
- **Purpose**: Wildlife monitoring, Scientific research, Map functionality
- **Collection**: Required for core app functionality
- **Sharing**: Shared with ICNF for research purposes

✅ **Precise location**
- **Purpose**: GPS trajectory recording, Wildlife observation positioning, Scientific data collection
- **Collection**: Required for core app functionality
- **Sharing**: Shared with ICNF for research purposes

#### PHOTOS AND VIDEOS
✅ **Photos**
- **Purpose**: Wildlife documentation, Scientific verification, Research evidence
- **Collection**: Optional - user can choose to take photos
- **Sharing**: Shared with ICNF for research purposes

#### FILES AND DOCS
✅ **Files and docs**
- **Purpose**: Offline data storage, Report attachments, Scientific documentation
- **Collection**: Required for offline functionality
- **Sharing**: Shared with ICNF for research purposes

#### APP ACTIVITY
✅ **App interactions**
- **Purpose**: Analytics, Performance monitoring, User experience improvement
- **Collection**: Required for app functionality
- **Sharing**: Not shared with third parties

#### APP INFO AND PERFORMANCE
✅ **Crash logs**
- **Purpose**: App stability, Bug fixing, Performance improvement
- **Collection**: Automatic when crashes occur
- **Sharing**: Not shared with third parties

✅ **Diagnostics**
- **Purpose**: App performance monitoring, Technical support
- **Collection**: Automatic during app usage
- **Sharing**: Not shared with third parties

#### DEVICE OR OTHER IDs
✅ **Device or other IDs**
- **Purpose**: User authentication, Session management, Data synchronization
- **Collection**: Required for account management
- **Sharing**: Not shared with third parties

### 3. Data Usage Purposes

For each data type, select these purposes:

#### Account Management
- Name
- Email address
- Device or other IDs

#### Analytics
- App interactions
- Crash logs
- Diagnostics

#### App functionality
- Precise location
- Approximate location
- Photos
- Files and docs
- App interactions

#### Developer communications
- Email address

#### Fraud prevention, security, and compliance
- Device or other IDs
- Crash logs

#### Personalization
- App interactions

### 4. Data Sharing

**"Do you share user data with third parties?"**
**Answer**: YES

**Third parties you share data with:**
- **ICNF - Instituto da Conservação da Natureza e das Florestas**
  - **Purpose**: Scientific research and wildlife conservation
  - **Data shared**: Location data, Photos, Wildlife observations, User identification
  - **Data handling**: Follows GDPR and Portuguese privacy laws

### 5. Third-Party SDKs Data Collection

#### Firebase SDK
✅ **Collects**: 
- Email addresses (Authentication)
- Device IDs (User management)
- App interactions (Analytics)
- Crash logs (Crashlytics)

✅ **Shares with**: Google (Firebase services)

#### Google Maps SDK
✅ **Collects**:
- Precise location (Mapping services)
- App interactions (Map usage)

✅ **Shares with**: Google (Maps services)

#### Expo SDK
✅ **Collects**:
- Device information (Platform compatibility)
- App interactions (Framework functionality)

✅ **Shares with**: Expo (Development platform)

### 6. Data Security Measures

**"Is all of the user data collected by your app encrypted in transit?"**
**Answer**: YES
- All data transmission uses HTTPS/TLS encryption
- Firebase handles secure data transmission
- No unencrypted data transmission

**"Do you provide a way for users to request that their data is deleted?"**
**Answer**: YES
- Users can request data <NAME_EMAIL>
- Account deletion removes all personal data
- Compliance with GDPR right to erasure

### 7. Sensitive Permissions

#### Location Access
- **Permission**: ACCESS_FINE_LOCATION, ACCESS_COARSE_LOCATION
- **Purpose**: Core wildlife monitoring functionality
- **User control**: Users must grant permission explicitly

#### Camera Access
- **Permission**: CAMERA
- **Purpose**: Wildlife photo documentation
- **User control**: Users can choose when to take photos

#### Storage Access
- **Permission**: READ_EXTERNAL_STORAGE, WRITE_EXTERNAL_STORAGE
- **Purpose**: Offline data storage and photo management
- **User control**: Required for offline functionality

## Step-by-Step Fix Instructions

### Go to Google Play Console

1. **Navigate to**: App content → Data safety
2. **Click**: "Manage" next to Data safety

### Update Data Collection Section

1. **Question**: "Does your app collect or share any of the required user data types?"
   - **Select**: "Yes"

2. **Question**: "Is all of the user data collected by your app encrypted in transit?"
   - **Select**: "Yes"

3. **Question**: "Do you provide a way for users to request that their data is deleted?"
   - **Select**: "Yes"

### Add All Data Types

Go through each category and add the data types listed above:

#### Personal info
- ✅ Name
- ✅ Email address

#### Location
- ✅ Approximate location
- ✅ Precise location

#### Photos and videos
- ✅ Photos

#### Files and docs
- ✅ Files and docs

#### App activity
- ✅ App interactions

#### App info and performance
- ✅ Crash logs
- ✅ Diagnostics

#### Device or other IDs
- ✅ Device or other IDs

### For Each Data Type, Specify:

1. **Is this data collected, shared, or both?**
   - Most: "Collected and shared"
   - Some (like crash logs): "Collected only"

2. **Is this data processed ephemerally?**
   - Select "No" for all (data is stored for research)

3. **Is this data required or optional?**
   - Location, Name, Email: "Required"
   - Photos: "Optional"
   - Others: "Required"

4. **Why is this data collected?**
   - Select appropriate purposes from the list above

### Add Third-Party Data Sharing

1. **Add ICNF as data recipient**:
   - Name: "ICNF - Instituto da Conservação da Natureza e das Florestas"
   - Purpose: "Scientific research and wildlife conservation"

2. **Add SDK partners**:
   - Google (Firebase, Maps)
   - Expo (Development platform)

### Save and Submit

1. **Review all entries** for accuracy
2. **Save changes**
3. **Submit for review**

## Important Notes

- **Be Complete**: Declare ALL data collection, even if minimal
- **Match Reality**: Ensure declarations match your actual app behavior
- **Include SDKs**: Third-party libraries often collect data automatically
- **Scientific Purpose**: Emphasize research and conservation purposes
- **GDPR Compliance**: Highlight privacy law compliance

This comprehensive declaration should resolve the Google Play rejection by accurately reflecting all data collection and sharing in your ProROLA app.

---

**Last Updated**: January 2025  
**For**: ProROLA App Google Play Submission 