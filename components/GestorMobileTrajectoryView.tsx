import React, { useState, useEffect, useMemo } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  ActivityIndicator,
  TouchableOpacity,
  Alert,
  Dimensions,
  Modal,
  BackHandler,
  Image,
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StatusBar } from 'expo-status-bar';
import { getFirestore, doc, getDoc, collection, query, where, getDocs } from 'firebase/firestore';
import TrajectoryMapView from './TrajectoryMapView';

const { width } = Dimensions.get('window');

interface GestorMobileTrajectoryData {
  id: string;
  zoneName: string;
  zoneId: string;
  totalDistance: number;
  createdAt: any;
  startTime: string;
  endTime: string;
  pathCoordinates: Array<{
    latitude: number;
    longitude: number;
  }>;
  observersCount: number;
  contactsCount: number;
  sessionId: string;
  weather?: {
    description?: string;
    temperature?: number;
    humidity?: number;
    windSpeed?: number;
    pressure?: number;
    [key: string]: any;
  };
  sessionDuration: number;
  userEmail: string;
  userName: string;
  [key: string]: any;
}

interface ContactData {
  id: string;
  sessionId: string;
  contactNumber: number;
  timestamp: any;
  distance: number;
  bearing: number;
  contactLocation: {
    latitude: number;
    longitude: number;
  };
  observerLocation: {
    latitude: number;
    longitude: number;
  };
  circumstances: {
    rolaEmVoo?: boolean;
    rolaAdultaCantando?: boolean;
    adultoPousado?: boolean;
    adultoEmDisplay?: boolean;
    adultoAIncubar?: boolean;
    ovos?: boolean;
    crias?: boolean;
    juvenile?: boolean;
    nichoOcupado?: boolean;
    ninhoVazio?: boolean;
    outraQual?: boolean;
    outraQualText?: string;
    [key: string]: any;
  };
  contactLocationDetails: {
    pontoDeAgua?: boolean;
    arvore?: boolean;
    arbusto?: boolean;
    clareira?: boolean;
    parcelaAgricola?: boolean;
    outraQual?: boolean;
    outraQualText?: string;
    latitude?: number;
    longitude?: number;
    [key: string]: any;
  };
  images: string[];
  protocol: string;
  userId: string;
  userName: string;
}

interface GestorMobileTrajectoryViewProps {
  trajectoryId: string;
  onClose: () => void;
}

const GestorMobileTrajectoryView: React.FC<GestorMobileTrajectoryViewProps> = ({ trajectoryId, onClose }) => {
  const [trajectory, setTrajectory] = useState<GestorMobileTrajectoryData | null>(null);
  const [contacts, setContacts] = useState<ContactData[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadingContacts, setLoadingContacts] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isMapFullscreen, setIsMapFullscreen] = useState(false);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isImageViewerVisible, setIsImageViewerVisible] = useState(false);

  useEffect(() => {
    fetchTrajectoryData();
  }, [trajectoryId]);

  // Handle Android back button
  useEffect(() => {
    const backAction = () => {
      onClose();
      return true; // Prevent default behavior
    };

    const backHandler = BackHandler.addEventListener('hardwareBackPress', backAction);

    return () => backHandler.remove();
  }, [onClose]);

  const fetchTrajectoryData = async () => {
    try {
      console.log('🔄 Fetching GESTOR MOBILE trajectory data for ID:', trajectoryId);
      const db = getFirestore();
      const docRef = doc(db, 'gestorMobile_trajetos', trajectoryId);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        const data = { id: docSnap.id, ...docSnap.data() } as GestorMobileTrajectoryData;
        console.log('✅ Gestor mobile trajectory data loaded:', data);
        setTrajectory(data);
        
        // Fetch contacts if sessionId exists
        if (data.sessionId) {
          await fetchContacts(data.sessionId);
        }
      } else {
        console.error('❌ Gestor mobile trajectory document not found');
        setError('Trajeto GPS não encontrado');
      }
    } catch (err) {
      console.error('💥 Error fetching gestor mobile trajectory:', err);
      setError('Erro ao carregar trajeto GPS');
    } finally {
      setLoading(false);
    }
  };

  const fetchContacts = async (sessionId: string) => {
    try {
      console.log('🔄 Fetching contacts for session:', sessionId);
      setLoadingContacts(true);
      
      const db = getFirestore();
      const contactsQuery = query(
        collection(db, 'gestorMobile_contacts'),
        where('sessionId', '==', sessionId)
      );
      
      const querySnapshot = await getDocs(contactsQuery);
      const contactsData: ContactData[] = [];
      
      querySnapshot.forEach((doc) => {
        const contactData = { id: doc.id, ...doc.data() } as ContactData;
        contactsData.push(contactData);
      });
      
      // Sort contacts by contact number
      contactsData.sort((a, b) => a.contactNumber - b.contactNumber);
      
      console.log('✅ Loaded contacts:', contactsData.length);
      setContacts(contactsData);
      
    } catch (err) {
      console.error('💥 Error fetching contacts:', err);
      // Don't set error state for contacts, just log it
    } finally {
      setLoadingContacts(false);
    }
  };

  const formatDate = (timestamp: any) => {
    if (!timestamp) return '—';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString('pt-PT');
  };

  const formatTime = (timeString: string) => {
    if (!timeString) return '--:--';
    const date = new Date(timeString);
    return date.toLocaleTimeString('pt-PT', { hour: '2-digit', minute: '2-digit' });
  };

  const formatDistance = (distanceInMeters: number) => {
    if (!distanceInMeters) return '—';
    return `${(distanceInMeters / 1000).toFixed(2)} km`;
  };

  const formatDuration = (durationInSeconds: number) => {
    if (!durationInSeconds) return '—';
    const minutes = Math.floor(durationInSeconds / 60);
    const seconds = durationInSeconds % 60;
    return `${minutes}m ${seconds}s`;
  };

  // Utility function to normalize bearing values
  const normalizeBearing = (bearing: any): string => {
    if (bearing === undefined || bearing === null) return 'N/A';
    
    let normalizedBearing = Number(bearing);
    if (isNaN(normalizedBearing)) return 'N/A';
    
    // Handle very large numbers that might be precision errors
    if (Math.abs(normalizedBearing) > 100000) {
      // Likely a precision error, try to extract meaningful digits
      normalizedBearing = normalizedBearing % 360;
    }
    
    // If bearing seems to be in radians (> 2π or < -2π), convert to degrees
    if (Math.abs(normalizedBearing) > 6.28) {
      normalizedBearing = (normalizedBearing * 180 / Math.PI);
    }
    
    // Normalize to 0-360 range
    normalizedBearing = ((normalizedBearing % 360) + 360) % 360;
    
    return `${Math.round(normalizedBearing)}°`;
  };

  const getWeatherIcon = (description?: string) => {
    if (!description) return '🌤️';
    const desc = description.toLowerCase();
    if (desc.includes('limpo') || desc.includes('clear')) return '☀️';
    if (desc.includes('nublado') || desc.includes('cloud')) return '☁️';
    if (desc.includes('chuva') || desc.includes('rain')) return '🌧️';
    if (desc.includes('vento') || desc.includes('wind')) return '💨';
    return '🌤️';
  };

  // Convert mobile trajectory coordinates to TrajectoryMapView format
  const getMapCoordinates = () => {
    if (!trajectory?.pathCoordinates) return [];
    return trajectory.pathCoordinates.map(coord => ({
      lat: coord.latitude,
      lng: coord.longitude
    }));
  };

  const handleImagePress = (images: string[], index: number) => {
    setSelectedImages(images);
    setCurrentImageIndex(index);
    setIsImageViewerVisible(true);
  };

  const closeImageViewer = () => {
    setIsImageViewerVisible(false);
    setSelectedImages([]);
    setCurrentImageIndex(0);
  };

  const navigateImage = (direction: 'prev' | 'next') => {
    if (direction === 'prev') {
      setCurrentImageIndex(prev => prev > 0 ? prev - 1 : selectedImages.length - 1);
    } else {
      setCurrentImageIndex(prev => prev < selectedImages.length - 1 ? prev + 1 : 0);
    }
  };

  // Convert contacts to TrajectoryMapView format - memoized to prevent flickering
  const mapContacts = useMemo(() => {
    if (!contacts || contacts.length === 0) return [];
    return contacts.map(contact => {
      // Convert circumstances object to readable string
      const circumstancesArray: string[] = [];
      if (contact.circumstances) {
        if (contact.circumstances.rolaEmVoo) circumstancesArray.push('Rola em voo');
        if (contact.circumstances.rolaAdultaCantando) circumstancesArray.push('Rola adulta cantando');
        if (contact.circumstances.adultoPousado) circumstancesArray.push('Adulto pousado');
        if (contact.circumstances.adultoEmDisplay) circumstancesArray.push('Adulto em display');
        if (contact.circumstances.adultoAIncubar) circumstancesArray.push('Adulto a incubar');
        if (contact.circumstances.ovos) circumstancesArray.push('Ovos');
        if (contact.circumstances.crias) circumstancesArray.push('Crias');
        if (contact.circumstances.juvenile) circumstancesArray.push('Juvenil');
        if (contact.circumstances.nichoOcupado) circumstancesArray.push('Nicho ocupado');
        if (contact.circumstances.ninhoVazio) circumstancesArray.push('Ninho vazio');
        if (contact.circumstances.outraQual && contact.circumstances.outraQualText) {
          circumstancesArray.push(contact.circumstances.outraQualText);
        }
      }

      // Convert location details object to readable string
      const locationArray: string[] = [];
      if (contact.contactLocationDetails) {
        if (contact.contactLocationDetails.pontoDeAgua) locationArray.push('Ponto de água');
        if (contact.contactLocationDetails.arvore) locationArray.push('Árvore');
        if (contact.contactLocationDetails.arbusto) locationArray.push('Arbusto');
        if (contact.contactLocationDetails.clareira) locationArray.push('Clareira');
        if (contact.contactLocationDetails.parcelaAgricola) locationArray.push('Parcela agrícola');
        if (contact.contactLocationDetails.outraQual && contact.contactLocationDetails.outraQualText) {
          locationArray.push(contact.contactLocationDetails.outraQualText);
        }
      }

      return {
        id: contact.id,
        lat: contact.contactLocation?.latitude || 0,
        lng: contact.contactLocation?.longitude || 0,
        type: 'contact',
        circumstances: circumstancesArray.join(', '),
        location: locationArray.join(', '),
        timestamp: contact.timestamp ? new Date(contact.timestamp.toDate ? contact.timestamp.toDate() : contact.timestamp).toISOString() : undefined
      };
    });
  }, [contacts]);

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0996a8" />
        <Text style={styles.loadingText}>A carregar trajeto GPS...</Text>
      </View>
    );
  }

  if (error || !trajectory) {
    return (
      <View style={styles.errorContainer}>
        <FontAwesome name="exclamation-triangle" size={50} color="#FF3B30" />
        <Text style={styles.errorText}>{error || 'Trajeto GPS não encontrado'}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={onClose}>
          <Text style={styles.retryButtonText}>Voltar</Text>
        </TouchableOpacity>
      </View>
    );
  }

  const mapCoordinates = getMapCoordinates();

  return (
    <>
      <StatusBar style="dark" />
      <ScrollView 
        style={styles.container}
        showsVerticalScrollIndicator={false}
      >
        {/* Back Button */}
        <View style={styles.backButtonContainer}>
          <TouchableOpacity style={styles.backButton} onPress={onClose}>
            <FontAwesome name="arrow-left" size={20} color="#0996a8" />
            <Text style={styles.backButtonText}>Voltar</Text>
          </TouchableOpacity>
        </View>

        {/* Content Cards */}
        <View style={styles.cardsContainer}>
          {/* Trajectory Name Card */}
          <View style={styles.trajectoryNameCard}>
            <View style={styles.mobileIndicator}>
              <FontAwesome name="mobile" size={16} color="#FFFFFF" />
              <Text style={styles.mobileIndicatorText}>TRAJETO GPS</Text>
            </View>
            <Text style={styles.trajectoryName}>{trajectory.zoneName}</Text>
          </View>

          {/* Basic Info Card */}
          <View style={styles.infoCard}>
            <View style={styles.cardHeader}>
              <FontAwesome name="info-circle" size={16} color="#0996a8" />
              <Text style={styles.cardTitle}>Informações</Text>
            </View>
            <View style={styles.cardContent}>
              <View style={styles.infoRowCompact}>
                <View style={styles.infoItemCompact}>
                  <FontAwesome name="road" size={14} color="#0996a8" />
                  <Text style={styles.infoText}>{formatDistance(trajectory.totalDistance)}</Text>
                </View>
                <View style={styles.infoItemCompact}>
                  <FontAwesome name="clock-o" size={14} color="#0996a8" />
                  <Text style={styles.infoText}>{formatDuration(trajectory.sessionDuration)}</Text>
                </View>
              </View>
              <View style={styles.infoRowSingle}>
                <FontAwesome name="calendar" size={14} color="#0996a8" />
                <Text style={styles.infoText}>{formatDate(trajectory.createdAt)}</Text>
              </View>
            </View>
          </View>

          {/* Timeline Card */}
          <View style={styles.infoCard}>
            <View style={styles.cardHeader}>
              <FontAwesome name="history" size={16} color="#0996a8" />
              <Text style={styles.cardTitle}>Timeline</Text>
            </View>
            <View style={styles.cardContent}>
              <View style={styles.timelineContainer}>
                <View style={styles.timelineRowCompact}>
                  <View style={styles.timelineItemCompact}>
                    <View style={[styles.timelineDot, styles.timelineDotStart]} />
                    <Text style={styles.timelineLabel}>INÍCIO</Text>
                    <Text style={styles.timelineTime}>{formatTime(trajectory.startTime)}</Text>
                  </View>
                  <View style={styles.timelineItemCompact}>
                    <View style={[styles.timelineDot, styles.timelineDotEnd]} />
                    <Text style={styles.timelineLabel}>FIM</Text>
                    <Text style={styles.timelineTime}>{formatTime(trajectory.endTime)}</Text>
                  </View>
                </View>
              </View>
            </View>
          </View>

          {/* Weather Card */}
          {trajectory.weather?.description && (
            <View style={styles.infoCard}>
              <View style={styles.cardHeader}>
                <FontAwesome name="cloud" size={16} color="#0996a8" />
                <Text style={styles.cardTitle}>Condições Meteorológicas</Text>
              </View>
              <View style={styles.cardContent}>
                <View style={styles.weatherContainer}>
                  <Text style={styles.weatherIcon}>{getWeatherIcon(trajectory.weather.description)}</Text>
                  <View style={styles.weatherInfo}>
                    <Text style={styles.weatherText}>{trajectory.weather.description}</Text>
                    <View style={styles.weatherDetailsInline}>
                      {trajectory.weather.temperature && (
                        <Text style={styles.weatherDetail}>
                          Temperatura: {trajectory.weather.temperature}°C
                        </Text>
                      )}
                      {trajectory.weather.humidity && (
                        <Text style={styles.weatherDetail}>
                          Humidade: {trajectory.weather.humidity}%
                        </Text>
                      )}
                    </View>
                  </View>
                </View>
                
                {/* Weather Details Grid */}
                <View style={styles.weatherDetailsGrid}>
                  {trajectory.weather.windSpeed !== undefined && (
                    <View style={styles.weatherDetailItem}>
                      <FontAwesome name="flag" size={12} color="#0996a8" />
                      <Text style={styles.weatherDetailLabel}>Vento</Text>
                      <Text style={styles.weatherDetailValue}>
                        {trajectory.weather.windSpeed} m/s
                      </Text>
                    </View>
                  )}
                  
                  {trajectory.weather.pressure !== undefined && (
                    <View style={styles.weatherDetailItem}>
                      <FontAwesome name="tachometer" size={12} color="#0996a8" />
                      <Text style={styles.weatherDetailLabel}>Pressão</Text>
                      <Text style={styles.weatherDetailValue}>
                        {trajectory.weather.pressure} hPa
                      </Text>
                    </View>
                  )}
                  
                  {trajectory.weather.visibility !== undefined && (
                    <View style={styles.weatherDetailItem}>
                      <FontAwesome name="eye" size={12} color="#0996a8" />
                      <Text style={styles.weatherDetailLabel}>Visibilidade</Text>
                      <Text style={styles.weatherDetailValue}>
                        {trajectory.weather.visibility} km
                      </Text>
                    </View>
                  )}
                  
                  {trajectory.weather.cloudiness !== undefined && (
                    <View style={styles.weatherDetailItem}>
                      <FontAwesome name="cloud" size={12} color="#0996a8" />
                      <Text style={styles.weatherDetailLabel}>Nebulosidade</Text>
                      <Text style={styles.weatherDetailValue}>
                        {trajectory.weather.cloudiness}%
                      </Text>
                    </View>
                  )}
                  
                  {trajectory.weather.uvIndex !== undefined && (
                    <View style={styles.weatherDetailItem}>
                      <FontAwesome name="sun-o" size={12} color="#0996a8" />
                      <Text style={styles.weatherDetailLabel}>UV</Text>
                      <Text style={styles.weatherDetailValue}>
                        {trajectory.weather.uvIndex}
                      </Text>
                    </View>
                  )}
                  
                  {trajectory.weather.feelsLike !== undefined && trajectory.weather.feelsLike !== trajectory.weather.temperature && (
                    <View style={styles.weatherDetailItem}>
                      <FontAwesome name="thermometer-half" size={12} color="#0996a8" />
                      <Text style={styles.weatherDetailLabel}>Sensação</Text>
                      <Text style={styles.weatherDetailValue}>
                        {trajectory.weather.feelsLike}°C
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            </View>
          )}

          {/* Statistics Card */}
          <View style={styles.infoCard}>
            <View style={styles.cardHeader}>
              <FontAwesome name="bar-chart" size={16} color="#0996a8" />
              <Text style={styles.cardTitle}>Estatísticas</Text>
            </View>
            <View style={styles.cardContent}>
              <View style={styles.statsGrid}>
                <View style={styles.statItem}>
                  <View style={styles.statLabel}>
                    <FontAwesome name="users" size={12} color="#0996a8" />
                    <Text style={styles.statLabelText}>Observadores</Text>
                  </View>
                  <Text style={styles.statValue}>{trajectory.observersCount || 0}</Text>
                </View>
                <View style={styles.statItem}>
                  <View style={styles.statLabel}>
                    <Image 
                      source={require('@/assets/images/icons/dove-icon.png')} 
                      style={styles.doveIconSmall}
                      resizeMode="contain"
                    />
                    <Text style={styles.statLabelText}>Contactos</Text>
                  </View>
                  <Text style={styles.statValue}>{trajectory.contactsCount || 0}</Text>
                </View>
              </View>
            </View>
          </View>

          {/* Contacts Section */}
          {contacts.length > 0 && (
            <View style={styles.infoCard}>
              <View style={styles.cardHeader}>
                <Image 
                  source={require('@/assets/images/icons/dove-icon.png')} 
                  style={styles.doveIcon}
                  resizeMode="contain"
                />
                <Text style={styles.cardTitle}>Contactos ({contacts.length})</Text>
                {loadingContacts && <ActivityIndicator size="small" color="#0996a8" />}
              </View>
              <View style={styles.cardContent}>
                {contacts.map((contact, index) => (
                  <View key={contact.id} style={[styles.contactItem, index < contacts.length - 1 && styles.contactItemBorder]}>
                    <View style={styles.contactHeader}>
                      <Text style={styles.contactNumber}>Contacto {contact.contactNumber}</Text>
                      <Text style={styles.contactTime}>
                        {contact.timestamp ? new Date(contact.timestamp.toDate ? contact.timestamp.toDate() : contact.timestamp).toLocaleTimeString('pt-PT', { hour: '2-digit', minute: '2-digit' }) : '--:--'}
                      </Text>
                    </View>
                    
                    <View style={styles.contactDetails}>
                      <View style={styles.contactDetailRow}>
                        <FontAwesome name="road" size={12} color="#6b7280" />
                        <Text style={styles.contactDetailText}>
                          {contact.distance ? `${contact.distance}m` : 'N/A'}
                        </Text>
                      </View>
                      
                      {contact.bearing !== undefined && contact.bearing !== null && (
                        <View style={styles.contactDetailRow}>
                          <FontAwesome name="compass" size={12} color="#6b7280" />
                          <Text style={styles.contactDetailText}>
                            {normalizeBearing(contact.bearing)}
                          </Text>
                        </View>
                      )}
                    </View>

                    {contact.circumstances && Object.keys(contact.circumstances).length > 0 && (
                      <View style={styles.contactCircumstances}>
                        <Text style={styles.contactCircumstancesLabel}>Circunstâncias:</Text>
                        <Text style={styles.contactCircumstancesText}>
                          {(() => {
                            const circumstancesArray: string[] = [];
                            if (contact.circumstances.rolaEmVoo) circumstancesArray.push('Rola em voo');
                            if (contact.circumstances.rolaAdultaCantando) circumstancesArray.push('Rola adulta cantando');
                            if (contact.circumstances.adultoPousado) circumstancesArray.push('Adulto pousado');
                            if (contact.circumstances.adultoEmDisplay) circumstancesArray.push('Adulto em display');
                            if (contact.circumstances.adultoAIncubar) circumstancesArray.push('Adulto a incubar');
                            if (contact.circumstances.ovos) circumstancesArray.push('Ovos');
                            if (contact.circumstances.crias) circumstancesArray.push('Crias');
                            if (contact.circumstances.juvenile) circumstancesArray.push('Juvenil');
                            if (contact.circumstances.nichoOcupado) circumstancesArray.push('Nicho ocupado');
                            if (contact.circumstances.ninhoVazio) circumstancesArray.push('Ninho vazio');
                            if (contact.circumstances.outraQual && contact.circumstances.outraQualText) {
                              circumstancesArray.push(contact.circumstances.outraQualText);
                            }
                            return circumstancesArray.join(', ');
                          })()}
                        </Text>
                      </View>
                    )}

                    {contact.contactLocationDetails && Object.keys(contact.contactLocationDetails).length > 0 && (
                      <View style={styles.contactLocation}>
                        <Text style={styles.contactLocationLabel}>Local:</Text>
                        <Text style={styles.contactLocationText}>
                          {(() => {
                            const locationArray: string[] = [];
                            if (contact.contactLocationDetails.pontoDeAgua) locationArray.push('Ponto de água');
                            if (contact.contactLocationDetails.arvore) locationArray.push('Árvore');
                            if (contact.contactLocationDetails.arbusto) locationArray.push('Arbusto');
                            if (contact.contactLocationDetails.clareira) locationArray.push('Clareira');
                            if (contact.contactLocationDetails.parcelaAgricola) locationArray.push('Parcela agrícola');
                            if (contact.contactLocationDetails.outraQual && contact.contactLocationDetails.outraQualText) {
                              locationArray.push(contact.contactLocationDetails.outraQualText);
                            }
                            return locationArray.join(', ');
                          })()}
                        </Text>
                      </View>
                    )}

                    {contact.images && contact.images.length > 0 && (
                      <View style={styles.contactImages}>
                        <Text style={styles.contactImagesLabel}>
                          <FontAwesome name="camera" size={12} color="#6b7280" />
                          {` Fotos (${contact.images.length})`}
                        </Text>
                        <View style={styles.imageThumbnails}>
                          {contact.images.map((imageUrl, imageIndex) => (
                            <TouchableOpacity
                              key={imageIndex}
                              style={styles.thumbnailContainer}
                              onPress={() => handleImagePress(contact.images, imageIndex)}
                            >
                              <Image
                                source={{ uri: imageUrl }}
                                style={styles.thumbnail}
                                resizeMode="cover"
                              />
                            </TouchableOpacity>
                          ))}
                        </View>
                      </View>
                    )}
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* Map Card */}
          <View style={styles.mapCard}>
            <View style={styles.cardHeader}>
              <FontAwesome name="map" size={16} color="#0996a8" />
              <Text style={styles.cardTitle}>Mapa do Trajeto GPS</Text>
              <View style={styles.mapStats}>
                <FontAwesome name="map-pin" size={12} color="#0996a8" />
                <Text style={styles.mapStatsText}>{mapCoordinates.length} pontos</Text>
              </View>
            </View>
            <View style={styles.mapContainer}>
              {mapCoordinates.length > 0 ? (
                <TrajectoryMapView
                  coordinates={mapCoordinates}
                  contacts={mapContacts}
                  onMapReady={() => console.log('✅ Gestor mobile trajectory map ready')}
                  onFullscreenToggle={(fullscreen) => setIsMapFullscreen(fullscreen)}
                />
              ) : (
                <View style={styles.mapPlaceholder}>
                  <FontAwesome name="map" size={40} color="#94a3b8" />
                  <Text style={styles.mapPlaceholderText}>
                    Nenhum trajeto encontrado para exibir
                  </Text>
                </View>
              )}
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Fullscreen Map Modal */}
      <Modal
        visible={isMapFullscreen}
        animationType="slide"
        presentationStyle="fullScreen"
        statusBarTranslucent={true}
      >
        <View style={styles.fullscreenContainer}>
          {mapCoordinates.length > 0 && (
            <TrajectoryMapView
              coordinates={mapCoordinates}
              contacts={mapContacts}
              onMapReady={() => console.log('✅ Fullscreen gestor mobile map ready')}
              onFullscreenToggle={(fullscreen) => setIsMapFullscreen(fullscreen)}
            />
          )}
        </View>
      </Modal>

      {/* Image Viewer Modal */}
      <Modal
        visible={isImageViewerVisible}
        animationType="fade"
        presentationStyle="fullScreen"
        statusBarTranslucent={true}
      >
        <View style={styles.imageViewerContainer}>
          <View style={styles.imageViewerHeader}>
            <TouchableOpacity style={styles.imageViewerClose} onPress={closeImageViewer}>
              <FontAwesome name="times" size={24} color="#FFFFFF" />
            </TouchableOpacity>
            <Text style={styles.imageCounter}>
              {currentImageIndex + 1} / {selectedImages.length}
            </Text>
          </View>
          
          {selectedImages.length > 0 && (
            <View style={styles.imageViewerContent}>
              <Image
                source={{ uri: selectedImages[currentImageIndex] }}
                style={styles.fullImage}
                resizeMode="contain"
              />
              
              {selectedImages.length > 1 && (
                <>
                  <TouchableOpacity
                    style={[styles.imageNavButton, styles.imageNavLeft]}
                    onPress={() => navigateImage('prev')}
                  >
                    <FontAwesome name="chevron-left" size={24} color="#FFFFFF" />
                  </TouchableOpacity>
                  
                  <TouchableOpacity
                    style={[styles.imageNavButton, styles.imageNavRight]}
                    onPress={() => navigateImage('next')}
                  >
                    <FontAwesome name="chevron-right" size={24} color="#FFFFFF" />
                  </TouchableOpacity>
                </>
              )}
            </View>
          )}
        </View>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
    paddingTop: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#64748b',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
    padding: 20,
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 16,
    backgroundColor: '#0996a8',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  backButtonContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  backButtonText: {
    fontSize: 16,
    color: '#0996a8',
    fontWeight: '500',
  },
  cardsContainer: {
    padding: 16,
    paddingTop: 0,
  },
  trajectoryNameCard: {
    backgroundColor: '#f0f9ff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#0996a8',
  },
  mobileIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#16a34a',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginBottom: 8,
    gap: 4,
  },
  mobileIndicatorText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#FFFFFF',
    textTransform: 'uppercase',
  },
  trajectoryName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
    textAlign: 'center',
    marginBottom: 4,
  },
  trajectoryDate: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
  },
  infoCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
    gap: 8,
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
  },
  cardContent: {
    gap: 8,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  infoRowCompact: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
    marginBottom: 8,
  },
  infoItemCompact: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    flex: 1,
    justifyContent: 'center',
  },
  infoRowSingle: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#6b7280',
  },
  timelineContainer: {
    gap: 16,
  },
  timelineRowCompact: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 8,
    marginBottom: 8,
  },
  timelineItemCompact: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f9fafb',
    borderRadius: 8,
    flex: 1,
    justifyContent: 'center',
  },
  timelineItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  timelineDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  timelineDotStart: {
    backgroundColor: '#22c55e',
  },
  timelineDotEnd: {
    backgroundColor: '#dc2626',
  },
  timelineContent: {
    flex: 1,
  },
  timelineLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#9ca3af',
    marginBottom: 2,
  },
  timelineTime: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '500',
  },
  weatherContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  weatherIcon: {
    fontSize: 32,
  },
  weatherInfo: {
    flex: 1,
  },
  weatherText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 4,
    textTransform: 'capitalize',
  },
  weatherDetail: {
    fontSize: 12,
    color: '#6b7280',
  },
  weatherDetailsInline: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 4,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
    gap: 8,
  },
  statLabel: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statLabelText: {
    fontSize: 12,
    color: '#6b7280',
    fontWeight: '500',
  },
  statValue: {
    fontSize: 16,
    color: '#374151',
    fontWeight: '600',
  },
  mapCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  mapStats: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginLeft: 'auto',
  },
  mapStatsText: {
    fontSize: 12,
    color: '#6b7280',
  },
  mapContainer: {
    height: 300,
    marginTop: 12,
    borderRadius: 8,
    overflow: 'hidden',
  },
  mapPlaceholder: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
    backgroundColor: '#f8fafc',
    borderRadius: 8,
    marginTop: 12,
  },
  mapPlaceholderText: {
    fontSize: 14,
    color: '#94a3b8',
    marginTop: 8,
    textAlign: 'center',
  },
  fullscreenContainer: {
    flex: 1,
    backgroundColor: '#000',
  },
  contactItem: {
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  contactItemBorder: {
    borderBottomWidth: 1,
  },
  contactHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  contactNumber: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1e293b',
  },
  contactTime: {
    fontSize: 14,
    color: '#6b7280',
  },
  contactDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  contactDetailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  contactDetailText: {
    fontSize: 14,
    color: '#6b7280',
  },
  contactCircumstances: {
    marginTop: 4,
  },
  contactCircumstancesLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 2,
  },
  contactCircumstancesText: {
    fontSize: 14,
    color: '#6b7280',
  },
  contactLocation: {
    marginTop: 4,
  },
  contactLocationLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 2,
  },
  contactLocationText: {
    fontSize: 14,
    color: '#6b7280',
  },
  contactImages: {
    marginTop: 4,
  },
  contactImagesLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#1e293b',
    marginBottom: 2,
  },
  weatherDetailsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 12,
  },
  weatherDetailItem: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: '#f8fafc',
    padding: 8,
    borderRadius: 6,
    alignItems: 'center',
    gap: 4,
  },
  weatherDetailLabel: {
    fontSize: 10,
    color: '#6b7280',
    fontWeight: '500',
    textAlign: 'center',
  },
  weatherDetailValue: {
    fontSize: 12,
    color: '#374151',
    fontWeight: '600',
    textAlign: 'center',
  },
  doveIcon: {
    width: 16,
    height: 16,
    tintColor: '#0996a8',
  },
  doveIconSmall: {
    width: 12,
    height: 12,
    tintColor: '#0996a8',
  },
  imageThumbnails: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 8,
  },
  thumbnailContainer: {
    width: 60,
    height: 60,
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: '#f3f4f6',
  },
  thumbnail: {
    width: '100%',
    height: '100%',
  },
  imageViewerContainer: {
    flex: 1,
    backgroundColor: '#000000',
  },
  imageViewerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 50,
    paddingBottom: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  imageViewerClose: {
    padding: 10,
  },
  imageCounter: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
  imageViewerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullImage: {
    width: '100%',
    height: '100%',
  },
  imageNavButton: {
    position: 'absolute',
    top: '50%',
    transform: [{ translateY: -25 }],
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageNavLeft: {
    left: 20,
  },
  imageNavRight: {
    right: 20,
  },
});

export default GestorMobileTrajectoryView; 