import React from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';

interface HelpSupportModalProps {
  visible: boolean;
  onClose: () => void;
}

const HelpSupportModal: React.FC<HelpSupportModalProps> = ({
  visible,
  onClose,
}) => {
  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.iconContainer}>
            <FontAwesome name="info-circle" size={50} color="#0996a8" />
          </View>
          
          <Text style={styles.title}>Precisa de Ajuda?</Text>
          
          <ScrollView 
            style={styles.scrollContainer}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={false}
          >
            <Text style={styles.mainMessage}>
              Se tiver dúvidas ou problemas, contacte o nosso suporte técnico:
            </Text>
            
            <View style={styles.emailContainer}>
              <Text style={styles.emailText}><EMAIL></Text>
            </View>
            
            <Text style={styles.subTitle}>A nossa equipa está disponível para o ajudar com:</Text>
            
            <View style={styles.helpList}>
              <View style={styles.helpItem}>
                <FontAwesome name="mobile" size={14} color="#0996a8" style={styles.helpIcon} />
                <Text style={styles.helpText}>Questões técnicas da aplicação</Text>
              </View>
              <View style={styles.helpItem}>
                <FontAwesome name="file-text" size={14} color="#0996a8" style={styles.helpIcon} />
                <Text style={styles.helpText}>Problemas com relatórios</Text>
              </View>
              <View style={styles.helpItem}>
                <FontAwesome name="user-plus" size={14} color="#0996a8" style={styles.helpIcon} />
                <Text style={styles.helpText}>Dificuldades com o registo</Text>
              </View>
              <View style={styles.helpItem}>
                <FontAwesome name="question-circle" size={14} color="#0996a8" style={styles.helpIcon} />
                <Text style={styles.helpText}>Outras questões relacionadas com o ProROLA</Text>
              </View>
            </View>
          </ScrollView>

          <TouchableOpacity
            style={styles.button}
            onPress={onClose}
          >
            <FontAwesome name="check" size={16} color="#FFFFFF" style={styles.buttonIcon} />
            <Text style={styles.buttonText}>OK</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    width: '85%',
    maxWidth: 400,
    maxHeight: '80%',
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0996a8',
    textAlign: 'center',
    marginBottom: 16,
  },
  scrollContainer: {
    maxHeight: 300,
    width: '100%',
  },
  scrollContent: {
    alignItems: 'center',
  },
  mainMessage: {
    fontSize: 16,
    color: '#333333',
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 22,
  },
  emailContainer: {
    backgroundColor: '#f0f9ff',
    borderRadius: 8,
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: '#0996a8',
  },
  emailText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#0996a8',
    textAlign: 'center',
  },
  subTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666666',
    textAlign: 'center',
    marginBottom: 12,
  },
  helpList: {
    width: '100%',
    paddingHorizontal: 8,
  },
  helpItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
    paddingVertical: 4,
  },
  helpIcon: {
    marginRight: 10,
    marginTop: 2,
  },
  helpText: {
    fontSize: 13,
    color: '#666666',
    flex: 1,
    lineHeight: 18,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 32,
    borderRadius: 8,
    backgroundColor: '#0996a8',
    marginTop: 20,
    minWidth: 100,
  },
  buttonIcon: {
    marginRight: 8,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default HelpSupportModal; 