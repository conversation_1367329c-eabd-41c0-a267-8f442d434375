import React from 'react';
import { TouchableOpacity, Text, StyleSheet, ActivityIndicator, View } from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { useNetwork } from '@/contexts/NetworkContext';

interface NetworkAwareButtonProps {
  onPress: () => void;
  title: string;
  operation: string; // Description of the operation for the warning message
  style?: any;
  textStyle?: any;
  disabled?: boolean;
  loading?: boolean;
  icon?: string;
  variant?: 'primary' | 'secondary' | 'danger';
  requiresNetwork?: boolean; // Set to false for operations that don't need network
}

export default function NetworkAwareButton({
  onPress,
  title,
  operation,
  style,
  textStyle,
  disabled = false,
  loading = false,
  icon,
  variant = 'primary',
  requiresNetwork = true,
}: NetworkAwareButtonProps) {
  const { checkNetworkAndWarn } = useNetwork();

  const handlePress = () => {
    if (disabled || loading) return;
    
    if (requiresNetwork) {
      checkNetworkAndWarn(operation, onPress);
    } else {
      onPress();
    }
  };

  const getButtonStyle = () => {
    const baseStyle = [styles.button];
    
    switch (variant) {
      case 'primary':
        baseStyle.push(styles.primaryButton);
        break;
      case 'secondary':
        baseStyle.push(styles.secondaryButton);
        break;
      case 'danger':
        baseStyle.push(styles.dangerButton);
        break;
    }
    
    if (disabled || loading) {
      baseStyle.push(styles.disabledButton);
    }
    
    return baseStyle;
  };

  const getTextStyle = () => {
    const baseStyle = [styles.buttonText];
    
    switch (variant) {
      case 'primary':
        baseStyle.push(styles.primaryButtonText);
        break;
      case 'secondary':
        baseStyle.push(styles.secondaryButtonText);
        break;
      case 'danger':
        baseStyle.push(styles.dangerButtonText);
        break;
    }
    
    if (disabled || loading) {
      baseStyle.push(styles.disabledButtonText);
    }
    
    return baseStyle;
  };

  return (
    <TouchableOpacity
      style={[getButtonStyle(), style].flat()}
      onPress={handlePress}
      disabled={disabled || loading}
    >
      <View style={styles.buttonContent}>
        {loading ? (
          <ActivityIndicator 
            color={variant === 'secondary' ? '#0996a8' : '#FFFFFF'} 
            style={styles.loadingIndicator}
          />
        ) : (
          icon && (
            <FontAwesome
              name={icon as any}
              size={16}
              color={variant === 'secondary' ? '#0996a8' : '#FFFFFF'}
              style={styles.buttonIcon}
            />
          )
        )}
        <Text style={[getTextStyle(), textStyle].flat()}>
          {title}
        </Text>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 48,
  },
  primaryButton: {
    backgroundColor: '#0996a8',
  },
  secondaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#0996a8',
  },
  dangerButton: {
    backgroundColor: '#dc3545',
  },
  disabledButton: {
    backgroundColor: '#cccccc',
    borderColor: '#cccccc',
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  primaryButtonText: {
    color: '#FFFFFF',
  },
  secondaryButtonText: {
    color: '#0996a8',
  },
  dangerButtonText: {
    color: '#FFFFFF',
  },
  disabledButtonText: {
    color: '#999999',
  },
  buttonIcon: {
    marginRight: 8,
  },
  loadingIndicator: {
    marginRight: 8,
  },
}); 