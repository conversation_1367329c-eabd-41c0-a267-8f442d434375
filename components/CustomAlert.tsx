import React from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Dimensions,
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { t } from '../config/translations';

interface CustomAlertProps {
  visible: boolean;
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  title?: string;
  onClose: () => void;
  onConfirm?: () => void;
}

const CustomAlert: React.FC<CustomAlertProps> = ({
  visible,
  type,
  message,
  title,
  onClose,
  onConfirm,
}) => {
  const getIcon = () => {
    // Special case for offline/no internet alerts
    if (title && (title.toLowerCase().includes('offline') || title.toLowerCase().includes('modo offline'))) {
      return <FontAwesome name="exclamation-triangle" size={50} color="#FF9500" />;
    }
    
    switch (type) {
      case 'success':
        return <FontAwesome name="check-circle" size={50} color="#0996a8" />;
      case 'error':
        return <FontAwesome name="times-circle" size={50} color="#FF3B30" />;
      case 'warning':
        return <FontAwesome name="exclamation-triangle" size={50} color="#FF3B30" />;
      case 'info':
        return <FontAwesome name="info-circle" size={50} color="#0996a8" />;
      default:
        return null;
    }
  };

  const getColor = () => {
    // Special case for offline/no internet alerts
    if (title && (title.toLowerCase().includes('offline') || title.toLowerCase().includes('modo offline'))) {
      return '#FF9500';
    }
    
    switch (type) {
      case 'success':
        return '#0996a8';
      case 'error':
        return '#FF3B30';
      case 'warning':
        return '#FF3B30';
      case 'info':
        return '#0996a8';
      default:
        return '#0996a8';
    }
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.iconContainer}>
            {getIcon()}
          </View>
          {title && <Text style={[styles.title, { color: getColor() }]}>{title}</Text>}
          <Text style={styles.message}>{message}</Text>
          <View style={styles.buttonContainer}>
            {(type === 'warning' || type === 'info') && onConfirm ? (
              <>
                <TouchableOpacity
                  style={[styles.button, styles.cancelButton]}
                  onPress={onClose}
                >
                  <FontAwesome name="times" size={16} color="#FFFFFF" style={styles.buttonIcon} />
                  <Text style={styles.buttonText}>{t('alerts.buttons.cancel')}</Text>
                </TouchableOpacity>
                <TouchableOpacity
                  style={[styles.button, { backgroundColor: getColor() }]}
                  onPress={async () => {
                    console.log('🔄 ALERT_DEBUG: Confirm button pressed in CustomAlert');
                    console.log('🔄 ALERT_DEBUG: onConfirm exists:', !!onConfirm);
                    if (onConfirm) {
                      console.log('🔄 ALERT_DEBUG: Calling onConfirm...');
                      await onConfirm();
                      console.log('🔄 ALERT_DEBUG: onConfirm completed');
                    }
                    console.log('🔄 ALERT_DEBUG: Calling onClose...');
                    onClose();
                    console.log('🔄 ALERT_DEBUG: onClose completed');
                  }}
                >
                  <FontAwesome name="check" size={16} color="#FFFFFF" style={styles.buttonIcon} />
                  <Text style={styles.buttonText}>{type === 'info' ? t('alerts.buttons.confirm') : t('alerts.buttons.ok')}</Text>
                </TouchableOpacity>
              </>
            ) :
              <TouchableOpacity
                style={[styles.button, { backgroundColor: getColor(), flex: 0 }]}
                onPress={async () => {
                  console.log('🔄 ALERT_DEBUG: Single confirm button pressed in CustomAlert');
                  console.log('🔄 ALERT_DEBUG: onConfirm exists:', !!onConfirm);
                  if (onConfirm) {
                    console.log('🔄 ALERT_DEBUG: Calling onConfirm...');
                    await onConfirm();
                    console.log('🔄 ALERT_DEBUG: onConfirm completed');
                  }
                  console.log('🔄 ALERT_DEBUG: Calling onClose...');
                  onClose();
                  console.log('🔄 ALERT_DEBUG: onClose completed');
                }}
              >
                <FontAwesome name="check" size={16} color="#FFFFFF" style={styles.buttonIcon} />
                <Text style={styles.buttonText}>{t('alerts.buttons.ok')}</Text>
              </TouchableOpacity>
            }
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    width: '80%',
    maxWidth: 400,
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: 16,
  },
  message: {
    fontSize: 16,
    color: '#333333',
    textAlign: 'center',
    marginBottom: 24,
    paddingHorizontal: 10,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    alignSelf: 'stretch',
    justifyContent: 'center',
  },
  button: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  buttonIcon: {
    marginRight: 8,
  },
  cancelButton: {
    backgroundColor: '#757575',
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default CustomAlert; 