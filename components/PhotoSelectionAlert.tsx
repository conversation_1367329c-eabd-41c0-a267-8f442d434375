import React from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';

interface PhotoSelectionAlertProps {
  visible: boolean;
  onClose: () => void;
  onTakePhoto: () => void;
  onChoosePhoto: () => void;
}

const PhotoSelectionAlert: React.FC<PhotoSelectionAlertProps> = ({
  visible,
  onClose,
  onTakePhoto,
  onChoosePhoto,
}) => {
  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.iconContainer}>
            <View style={styles.iconBackground}>
              <FontAwesome name="camera" size={32} color="#0996a8" />
            </View>
          </View>
          <Text style={styles.title}>Adicionar Foto</Text>
          <Text style={styles.subtitle}>Escolha uma opção abaixo</Text>
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.actionButton]}
              onPress={onTakePhoto}
            >
              <View style={styles.buttonIconContainer}>
                <FontAwesome name="camera" size={20} color="#FFFFFF" />
              </View>
              <View style={styles.buttonTextContainer}>
                <Text style={styles.buttonText}>Tirar Foto</Text>
                <Text style={styles.buttonSubtext}>Usar a câmera do dispositivo</Text>
              </View>
              <FontAwesome name="angle-right" size={20} color="#FFFFFF" style={styles.buttonArrow} />
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.actionButton]}
              onPress={onChoosePhoto}
            >
              <View style={styles.buttonIconContainer}>
                <FontAwesome name="image" size={20} color="#FFFFFF" />
              </View>
              <View style={styles.buttonTextContainer}>
                <Text style={styles.buttonText}>Galeria</Text>
                <Text style={styles.buttonSubtext}>Escolher foto existente</Text>
              </View>
              <FontAwesome name="angle-right" size={20} color="#FFFFFF" style={styles.buttonArrow} />
            </TouchableOpacity>
          </View>
          <TouchableOpacity
            style={styles.cancelButton}
            onPress={onClose}
          >
            <Text style={styles.cancelText}>Cancelar</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 24,
    width: '90%',
    maxWidth: 400,
    alignItems: 'center',
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  iconContainer: {
    marginBottom: 20,
  },
  iconBackground: {
    width: 72,
    height: 72,
    borderRadius: 36,
    backgroundColor: '#e8f5f3',
    justifyContent: 'center',
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 24,
  },
  buttonContainer: {
    width: '100%',
    gap: 12,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    width: '100%',
  },
  buttonIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonTextContainer: {
    flex: 1,
    marginLeft: 16,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  buttonSubtext: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
    marginTop: 2,
  },
  buttonArrow: {
    marginLeft: 8,
  },
  actionButton: {
    backgroundColor: '#0996a8',
  },
  cancelButton: {
    marginTop: 16,
    paddingVertical: 12,
  },
  cancelText: {
    color: '#666666',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default PhotoSelectionAlert; 