import React, { useEffect, useRef, useState } from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { FontAwesome6 } from '@expo/vector-icons';
import { useNetwork } from '@/contexts/NetworkContext';
import { t } from '@/config/translations';
import { monitoringSyncService } from '@/services/monitoringSyncService';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface NetworkStatusIndicatorProps {
  style?: any;
}

export default function NetworkStatusIndicator({ 
  style 
}: NetworkStatusIndicatorProps) {
  const { hasInternetConnection, isLoading } = useNetwork();
  const flashAnim = useRef(new Animated.Value(1)).current;
  const [offlineDataCount, setOfflineDataCount] = useState(0);

  // Check for offline data periodically
  useEffect(() => {
    const checkOfflineData = async () => {
      try {
        const [pendingReports, monitoringData] = await Promise.all([
          AsyncStorage.getItem('pendingReports'),
          monitoringSyncService.getOfflineDataSummary()
        ]);
        
        const reportCount = pendingReports ? JSON.parse(pendingReports).length : 0;
        
        // Check if monitoring data has valid userIds before counting it
        let validMonitoringData = 0;
        try {
          const offlineContactEvents = await AsyncStorage.getItem('offlineContactEvents');
          if (offlineContactEvents) {
            const events = JSON.parse(offlineContactEvents);
            const hasValidData = events.some((event: any) => 
              event.userId && event.userId !== 'undefined' && event.userId !== 'unknown_user'
            );
            if (hasValidData) {
              validMonitoringData = monitoringData.sessions + 
                                  monitoringData.gpsPoints + 
                                  monitoringData.contactEvents;
            }
          }
        } catch (error) {
          // If we can't check, assume monitoring data is valid
          validMonitoringData = monitoringData.sessions + 
                               monitoringData.gpsPoints + 
                               monitoringData.contactEvents;
        }
        
        const totalOfflineData = reportCount + validMonitoringData;
        setOfflineDataCount(totalOfflineData);
      } catch (error) {
        console.error('Error checking offline data:', error);
      }
    };

    checkOfflineData();
    
    // Check every 5 seconds for faster sync status updates
    const interval = setInterval(checkOfflineData, 5000);
    return () => clearInterval(interval);
  }, []);

  // Start flashing animation when component mounts
  useEffect(() => {
    const startFlashing = () => {
      Animated.loop(
        Animated.sequence([
          Animated.timing(flashAnim, {
            toValue: 0.3,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(flashAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    };

    startFlashing();
  }, [flashAnim]);

  if (isLoading) {
    return null;
  }

  const isConnected = hasInternetConnection();

  // Show different states based on connection and offline data
  if (isConnected && offlineDataCount === 0) {
    return null; // Connected and no offline data - don't show anything
  }

  if (isConnected && offlineDataCount > 0) {
    // Connected with offline data - show sync indicator
    return (
      <View style={[styles.container, styles.syncContainer, style]}>
        <FontAwesome6
          name="cloud-arrow-up"
          size={12}
          color="#FFFFFF"
          style={styles.icon}
        />
        <Text style={styles.text}>
          Sincronizando...
        </Text>
      </View>
    );
  }

  // Not connected - show offline indicator with data count if any
  return (
    <View style={[styles.container, style]}>
      <FontAwesome6
        name="wifi"
        size={12}
        color="#FFFFFF"
        style={styles.icon}
      />
      <Animated.Text style={[styles.text, { opacity: flashAnim }]}>
        {offlineDataCount > 0 
          ? `Offline (${offlineDataCount} pendentes)`
          : t('network.disconnected.noInternet')
        }
      </Animated.Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 5,
    paddingVertical: 0,
    borderRadius: 3,
    backgroundColor: '#FF9500',
    alignSelf: 'center',
  },
  syncContainer: {
    backgroundColor: '#007BFF',
  },
  icon: {
    marginRight: 4,
    opacity: 0.6, // Make the wifi icon appear dimmed to indicate no connection
  },
  text: {
    fontSize: 12,
    fontWeight: '500',
    color: '#FFFFFF',
  },
}); 