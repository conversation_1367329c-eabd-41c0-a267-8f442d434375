import React from 'react';
import {
  Modal,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Linking,
} from 'react-native';
import { FontAwesome } from '@expo/vector-icons';

interface HuntingZoneRegistrationAlertProps {
  visible: boolean;
  onClose: () => void;
}

const HuntingZoneRegistrationAlert: React.FC<HuntingZoneRegistrationAlertProps> = ({
  visible,
  onClose,
}) => {
  const handleConfirm = () => {
    console.log('Opening hunting zone web registration');
    Linking.openURL('https://prorola.app/gestores');
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.iconContainer}>
            <FontAwesome name="binoculars" size={50} color="#0996a8" />
          </View>
          
          <Text style={styles.title}>Registo de Zona de Caça</Text>
          
          <Text style={styles.question}>
            Para se registar como Gestor{'\n'}de Zona de Caça, deve primeiro{'\n'}registar a sua zona
          </Text>

          <View style={styles.infoCard}>
            <View style={styles.infoHeader}>
              <FontAwesome name="globe" size={16} color="#0996a8" />
              <Text style={styles.infoTitle}>Registo Web Necessário</Text>
            </View>
            <Text style={styles.infoMessage}>
              Deve primeiro registar a sua zona no sistema web. Após o registo web, poderá iniciar sessão nesta aplicação.
            </Text>
          </View>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton]}
              onPress={onClose}
            >
              <FontAwesome name="times" size={16} color="#FFFFFF" style={styles.buttonIcon} />
              <Text style={styles.buttonText}>Cancelar</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.button, styles.confirmButton]}
              onPress={handleConfirm}
            >
              <FontAwesome name="external-link" size={16} color="#FFFFFF" style={styles.buttonIcon} />
              <Text style={styles.buttonText}>Registar</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 24,
    width: '85%',
    maxWidth: 400,
    alignItems: 'center',
  },
  iconContainer: {
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#0996a8',
    textAlign: 'center',
    marginBottom: 16,
  },
  question: {
    fontSize: 16,
    color: '#333333',
    textAlign: 'center',
    marginBottom: 20,
  },
  infoCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 15,
    marginBottom: 24,
    width: '100%',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    borderWidth: 1,
    borderColor: '#f0f0f0',
  },
  infoHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  infoTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#0996a8',
    marginLeft: 6,
  },
  infoMessage: {
    fontSize: 13,
    color: '#666666',
    lineHeight: 18,
    textAlign: 'justify',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    alignSelf: 'stretch',
    justifyContent: 'center',
  },
  button: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  buttonIcon: {
    marginRight: 10,
  },
  cancelButton: {
    backgroundColor: '#757575',
  },
  confirmButton: {
    backgroundColor: '#0996a8',
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default HuntingZoneRegistrationAlert; 