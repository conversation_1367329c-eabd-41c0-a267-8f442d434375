import React, { useState, useRef, useEffect } from 'react';
import { View, Text, TouchableOpacity, TextInput, ScrollView, StyleSheet, Modal, Platform, Image, ActivityIndicator, Alert } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { FontAwesome } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import LoadingImage from '@/components/LoadingImage';
import PhotoSelectionAlert from '@/components/PhotoSelectionAlert';
import { ReportCircumstances, ReportContactLocation } from '@/types/reports';

interface ContactDetailsModalProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (data: ContactDetailsData) => void;
  distance: number;
  bearing?: number;
  showAlert?: (config: { type: 'success' | 'error' | 'warning' | 'info'; message: string; onConfirm?: () => void }) => void;
}

interface ContactDetailsData {
  circumstances: ReportCircumstances;
  contactLocation: ReportContactLocation;
  images: string[];
}

// Portuguese translations
const pt = {
  contactDetails: 'Detalhes do Contacto',
  reportContactCircumstances: 'Indique em que circunstância se deu o contacto',
  reportContactLocation: 'Indique onde se deu contacto',
  rolaAdultaCantando: 'Rola adulta a cantar',
  rolaEmVoo: 'Rola em voo',
  adultoPousado: 'Adulto pousado',
  adultoEmDisplay: 'Adulto em display',
  ninhoVazio: 'Ninho vazio',
  nichoOcupado: 'Nicho ocupado',
  ovos: 'Ovos',
  adultoAIncubar: 'Adulto a incubar',
  crias: 'Crias',
  juvenile: 'Juvenil',
  outraQual: 'Outra. Qual?',
  arvore: 'Árvore',
  arbusto: 'Arbusto',
  pontoDeAgua: 'Ponto de Água',
  clareira: 'Clareira',
  parcelaAgricola: 'Parcela Agrícola',
  addImages: 'Anexar fotos',
  selectAtLeastOne: 'Por favor, selecione pelo menos uma opção de cada secção',
  maxImagesReached: 'Máximo de 6 fotos atingido',
  confirmDeletePhoto: 'Tem certeza que deseja eliminar esta foto?',
  cancel: 'Cancelar',
  save: 'Adicionar',
  distance: 'Distância',
  bearing: 'Direção',
  uploadingImages: 'A carregar fotos...',
  imagesWillSync: 'Fotos serão enviadas em segundo plano quando houver internet',
};

const ContactDetailsModal: React.FC<ContactDetailsModalProps> = ({
  visible,
  onClose,
  onSubmit,
  distance,
  bearing,
  showAlert
}) => {
  const scrollViewRef = useRef<ScrollView>(null);
  const [showPhotoAlert, setShowPhotoAlert] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // State for circumstances
  const [circumstances, setCircumstances] = useState<ReportCircumstances>({
    rolaAdultaCantando: false,
    rolaEmVoo: false,
    adultoPousado: false,
    adultoEmDisplay: false,
    ninhoVazio: false,
    nichoOcupado: false,
    ovos: false,
    adultoAIncubar: false,
    crias: false,
    juvenile: false,
    outraQual: false,
    outraQualText: '',
  });

  // State for contact location
  const [contactLocation, setContactLocation] = useState<ReportContactLocation>({
    arvore: false,
    arbusto: false,
    pontoDeAgua: false,
    clareira: false,
    parcelaAgricola: false,
    outraQual: false,
    outraQualText: '',
  });

  // State for images
  const [images, setImages] = useState<string[]>([]);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (visible) {
      // Reset when opening
      setCircumstances({
        rolaAdultaCantando: false,
        rolaEmVoo: false,
        adultoPousado: false,
        adultoEmDisplay: false,
        ninhoVazio: false,
        nichoOcupado: false,
        ovos: false,
        adultoAIncubar: false,
        crias: false,
        juvenile: false,
        outraQual: false,
        outraQualText: '',
      });
      setContactLocation({
        arvore: false,
        arbusto: false,
        pontoDeAgua: false,
        clareira: false,
        parcelaAgricola: false,
        outraQual: false,
        outraQualText: '',
      });
      setImages([]);
      setIsSubmitting(false);
    } else {
      // Cleanup when closing to free memory
      setImages([]);
      setCircumstances({
        rolaAdultaCantando: false,
        rolaEmVoo: false,
        adultoPousado: false,
        adultoEmDisplay: false,
        ninhoVazio: false,
        nichoOcupado: false,
        ovos: false,
        adultoAIncubar: false,
        crias: false,
        juvenile: false,
        outraQual: false,
        outraQualText: '',
      });
      setContactLocation({
        arvore: false,
        arbusto: false,
        pontoDeAgua: false,
        clareira: false,
        parcelaAgricola: false,
        outraQual: false,
        outraQualText: '',
      });
      setIsSubmitting(false);
    }
  }, [visible]);

  // Add effect to track component mounting
  useEffect(() => {
    return () => {
    };
  }, []);

  // Add effect to track isSubmitting changes
  useEffect(() => {
  }, [isSubmitting]);

  const formatDistance = (meters: number): string => {
    if (meters < 1000) {
      return `${Math.round(meters)}m`;
    } else {
      return `${(meters / 1000).toFixed(2)}km`;
    }
  };

  const getBearingText = (degrees: number): string => {
    const directions = [
      'N', 'NNE', 'NE', 'ENE',
      'E', 'ESE', 'SE', 'SSE',
      'S', 'SSO', 'SO', 'OSO',
      'O', 'ONO', 'NO', 'NNO'
    ];
    
    const index = Math.round(degrees / 22.5) % 16;
    return directions[index];
  };

  const handleCircumstanceChange = (key: keyof ReportCircumstances, value: boolean | string) => {
    if (key === 'outraQualText') {
      // Handle text input separately
      setCircumstances(prev => ({
        ...prev,
        [key]: value as string
      }));
    } else {
      // Reset all options to false, then set the selected one if value is true
      if (value === true) {
        const resetCircumstances: ReportCircumstances = {
          rolaAdultaCantando: false,
          rolaEmVoo: false,
          adultoPousado: false,
          adultoEmDisplay: false,
          ninhoVazio: false,
          nichoOcupado: false,
          ovos: false,
          adultoAIncubar: false,
          crias: false,
          juvenile: false,
          outraQual: false,
          outraQualText: '',
        };
        
        setCircumstances({
          ...resetCircumstances,
          [key]: true,
          outraQualText: key === 'outraQual' ? circumstances.outraQualText : '',
        });
      } else {
        // Deselecting - reset everything to false
        const resetCircumstances: ReportCircumstances = {
          rolaAdultaCantando: false,
          rolaEmVoo: false,
          adultoPousado: false,
          adultoEmDisplay: false,
          ninhoVazio: false,
          nichoOcupado: false,
          ovos: false,
          adultoAIncubar: false,
          crias: false,
          juvenile: false,
          outraQual: false,
          outraQualText: '',
        };
        
        setCircumstances(resetCircumstances);
      }
    }
  };

  const handleContactLocationChange = (key: keyof ReportContactLocation, value: boolean | string) => {
    if (key === 'outraQualText') {
      // Handle text input separately
      setContactLocation(prev => ({
        ...prev,
        [key]: value as string
      }));
    } else {
      // Reset all options to false, then set the selected one if value is true
      if (value === true) {
        const resetContactLocation: ReportContactLocation = {
          arvore: false,
          arbusto: false,
          pontoDeAgua: false,
          clareira: false,
          parcelaAgricola: false,
          outraQual: false,
          outraQualText: '',
        };
        
        setContactLocation({
          ...resetContactLocation,
          [key]: true,
          outraQualText: key === 'outraQual' ? contactLocation.outraQualText : '',
        });
      } else {
        // Deselecting - reset everything to false
        const resetContactLocation: ReportContactLocation = {
          arvore: false,
          arbusto: false,
          pontoDeAgua: false,
          clareira: false,
          parcelaAgricola: false,
          outraQual: false,
          outraQualText: '',
        };
        
        setContactLocation(resetContactLocation);
      }
    }
  };

  const validateForm = (): boolean => {
    // Check if at least one circumstance is selected
    const hasCircumstance = Object.entries(circumstances).some(([key, value]) => 
      key !== 'outraQualText' && value === true
    );

    // Check if at least one contact location is selected
    const hasContactLocation = Object.entries(contactLocation).some(([key, value]) => 
      key !== 'outraQualText' && value === true
    );

    // Require at least one selection from both sections
    const isValid = hasCircumstance && hasContactLocation;
    return isValid;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      if (showAlert) {
        showAlert({
          type: 'warning',
          message: pt.selectAtLeastOne
        });
      }
      return;
    }

    // Quick validation and immediate submission - no image processing delays
    console.log('📝 Contact details: Submitting immediately with local image paths');
    
      onSubmit({
        circumstances,
        contactLocation,
      images // Pass local image paths - processing will happen during sync
      });
  };

  const handleClose = () => {
    onClose();
  };

  const handleAddImages = () => {
    if (images.length >= 6) {
      if (showAlert) {
        showAlert({
          type: 'warning',
          message: pt.maxImagesReached
        });
      }
      return;
    }
    setShowPhotoAlert(true);
  };

  const pickImage = async (source: 'camera' | 'gallery') => {
    try {
      let result;
      
      if (source === 'camera') {
        const { status } = await ImagePicker.requestCameraPermissionsAsync();
        if (status !== 'granted') {
          if (showAlert) {
            showAlert({
              type: 'error',
              message: 'Desculpe, precisamos de permissões da câmara para isto funcionar!'
            });
          }
          return;
        }
        
        result = await ImagePicker.launchCameraAsync({
          allowsEditing: true,
          aspect: [4, 3],
          quality: 0.7,
        });
      } else {
        const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
        if (status !== 'granted') {
          if (showAlert) {
            showAlert({
              type: 'error',
              message: 'Desculpe, precisamos de permissões da galeria para isto funcionar!'
            });
          }
          return;
        }
        
        result = await ImagePicker.launchImageLibraryAsync({
          allowsEditing: true,
          aspect: [4, 3],
          quality: 0.7,
        });
      }

      if (!result.canceled && result.assets[0]) {
        setImages(prev => [...prev, result.assets[0].uri]);
        
        // Auto-scroll to bottom after adding image so user can see the "Adicionar" button
        setTimeout(() => {
          scrollViewRef.current?.scrollToEnd({ animated: true });
        }, 100);
      }
    } catch (error) {
      console.error('Erro ao selecionar imagem:', error);
    }
  };

  const handleDeletePhoto = (index: number) => {
    if (showAlert) {
      showAlert({
        type: 'warning',
        message: pt.confirmDeletePhoto,
        onConfirm: () => {
          setImages(prev => prev.filter((_, i) => i !== index));
        }
      });
    } else {
      // Fallback to Alert.alert if showAlert is not provided
      Alert.alert(
        'Eliminar foto',
        pt.confirmDeletePhoto,
        [
          {
            text: pt.cancel,
            onPress: () => {},
            style: 'cancel',
          },
          {
            text: 'Eliminar',
            onPress: () => {
              setImages(prev => prev.filter((_, i) => i !== index));
            },
            style: 'destructive',
          },
        ],
        { cancelable: false }
      );
    }
  };

  const renderCheckboxItem = (
    key: string,
    label: string,
    checked: boolean,
    onToggle: () => void,
    showTextInput: boolean = false,
    textValue: string = '',
    onTextChange?: (text: string) => void
  ) => (
    <View key={key} style={styles.checkboxContainer}>
      <TouchableOpacity
        style={[styles.checkboxRow, checked && styles.checkboxRowSelected]}
        onPress={onToggle}
        activeOpacity={0.7}
      >
        <View style={[styles.checkbox, checked && styles.checkboxChecked]}>
          {checked && <FontAwesome name="check" size={14} color="#fff" />}
        </View>
        <Text style={[styles.checkboxLabel, checked && styles.checkboxLabelSelected]}>
          {label}
        </Text>
      </TouchableOpacity>
      
      {showTextInput && checked && (
        <View style={styles.textInputContainer}>
          <TextInput
            style={styles.checkboxTextInput}
            placeholder="Especifique..."
            placeholderTextColor="#999"
            value={textValue}
            onChangeText={onTextChange}
            multiline
            numberOfLines={3}
          />
        </View>
      )}
    </View>
  );

  // Helper functions to get selected options
  const getSelectedCircumstance = () => {
    const selected = Object.entries(circumstances).find(([key, value]) => 
      key !== 'outraQualText' && value === true
    )?.[0] as keyof ReportCircumstances | undefined;
    
    return selected;
  };

  const getSelectedContactLocation = () => {
    const selected = Object.entries(contactLocation).find(([key, value]) => 
      key !== 'outraQualText' && value === true
    )?.[0] as keyof ReportContactLocation | undefined;
    
    return selected;
  };

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="fullScreen"
      onRequestClose={onClose}
      statusBarTranslucent={true}
    >
      <View style={styles.container}>
        <StatusBar style="light" />
        {/* Header */}
        <SafeAreaView edges={['top']} style={styles.headerSafeArea}>
          <View style={styles.header}>
            <FontAwesome name="file-text-o" size={20} color="#fff" style={styles.headerIcon} />
            <Text style={styles.headerTitle}>{pt.contactDetails}</Text>
          </View>
        </SafeAreaView>

        {/* Distance and bearing info */}
        <View style={styles.distanceContainer}>
          <View style={styles.distanceRow}>
            <FontAwesome name="arrows-h" size={16} color="#666" />
            <Text style={styles.distanceText}>
              {pt.distance}: {formatDistance(distance)}
            </Text>
            {bearing !== undefined && (
              <>
                <FontAwesome name="compass" size={16} color="#666" style={styles.compassIcon} />
                <Text style={styles.distanceText}>
                  {pt.bearing}: {Math.round(bearing)}° {getBearingText(bearing)}
                </Text>
              </>
            )}
          </View>
        </View>

        {/* Scrollable content */}
        <ScrollView 
          ref={scrollViewRef}
          style={styles.scrollContent}
          contentContainerStyle={styles.scrollContentContainer}
          showsVerticalScrollIndicator={true}
          bounces={true}
          keyboardShouldPersistTaps="handled"
          nestedScrollEnabled={true}
        >
          {/* Circumstances Section */}
          <View style={styles.checkboxSection}>
            <Text style={styles.sectionTitle}>{pt.reportContactCircumstances}</Text>
            
            <View style={styles.checkboxSection}>
              {(() => {
                const selectedCircumstance = getSelectedCircumstance();
                
                if (selectedCircumstance) {
                  // Show only the selected option
                  return renderCheckboxItem(
                    selectedCircumstance,
                    pt[selectedCircumstance as keyof typeof pt],
                    true,
                    () => handleCircumstanceChange(selectedCircumstance, false),
                    selectedCircumstance === 'outraQual',
                    circumstances.outraQualText,
                    (text) => handleCircumstanceChange('outraQualText', text)
                  );
                }
                
                // Show all options when none are selected
                return (
                  <>
                    {renderCheckboxItem(
                      'rolaAdultaCantando',
                      pt.rolaAdultaCantando,
                      circumstances.rolaAdultaCantando,
                      () => handleCircumstanceChange('rolaAdultaCantando', !circumstances.rolaAdultaCantando)
                    )}
                    
                    {renderCheckboxItem(
                      'rolaEmVoo',
                      pt.rolaEmVoo,
                      circumstances.rolaEmVoo,
                      () => handleCircumstanceChange('rolaEmVoo', !circumstances.rolaEmVoo)
                    )}
                    
                    {renderCheckboxItem(
                      'adultoPousado',
                      pt.adultoPousado,
                      circumstances.adultoPousado,
                      () => handleCircumstanceChange('adultoPousado', !circumstances.adultoPousado)
                    )}
                    
                    {renderCheckboxItem(
                      'adultoEmDisplay',
                      pt.adultoEmDisplay,
                      circumstances.adultoEmDisplay,
                      () => handleCircumstanceChange('adultoEmDisplay', !circumstances.adultoEmDisplay)
                    )}
                    
                    {renderCheckboxItem(
                      'ninhoVazio',
                      pt.ninhoVazio,
                      circumstances.ninhoVazio,
                      () => handleCircumstanceChange('ninhoVazio', !circumstances.ninhoVazio)
                    )}
                    
                    {renderCheckboxItem(
                      'nichoOcupado',
                      pt.nichoOcupado,
                      circumstances.nichoOcupado,
                      () => handleCircumstanceChange('nichoOcupado', !circumstances.nichoOcupado)
                    )}
                    
                    {renderCheckboxItem(
                      'ovos',
                      pt.ovos,
                      circumstances.ovos,
                      () => handleCircumstanceChange('ovos', !circumstances.ovos)
                    )}
                    
                    {renderCheckboxItem(
                      'adultoAIncubar',
                      pt.adultoAIncubar,
                      circumstances.adultoAIncubar,
                      () => handleCircumstanceChange('adultoAIncubar', !circumstances.adultoAIncubar)
                    )}
                    
                    {renderCheckboxItem(
                      'crias',
                      pt.crias,
                      circumstances.crias,
                      () => handleCircumstanceChange('crias', !circumstances.crias)
                    )}
                    
                    {renderCheckboxItem(
                      'juvenile',
                      pt.juvenile,
                      circumstances.juvenile,
                      () => handleCircumstanceChange('juvenile', !circumstances.juvenile)
                    )}
                    
                    {renderCheckboxItem(
                      'outraQual',
                      pt.outraQual,
                      circumstances.outraQual,
                      () => handleCircumstanceChange('outraQual', !circumstances.outraQual),
                      true,
                      circumstances.outraQualText,
                      (text) => handleCircumstanceChange('outraQualText', text)
                    )}
                  </>
                );
              })()}
            </View>
          </View>

          {/* Contact Location Section */}
          <View style={styles.checkboxSection}>
            <Text style={styles.sectionTitle}>{pt.reportContactLocation}</Text>
            
            <View style={styles.checkboxSection}>
              {(() => {
                const selectedContactLocation = getSelectedContactLocation();
                
                if (selectedContactLocation) {
                  // Show only the selected option
                  return renderCheckboxItem(
                    selectedContactLocation,
                    pt[selectedContactLocation as keyof typeof pt],
                    true,
                    () => handleContactLocationChange(selectedContactLocation, false),
                    selectedContactLocation === 'outraQual',
                    contactLocation.outraQualText,
                    (text) => handleContactLocationChange('outraQualText', text)
                  );
                }
                
                // Show all options when none are selected
                return (
                  <>
                    {renderCheckboxItem(
                      'arvore',
                      pt.arvore,
                      contactLocation.arvore,
                      () => handleContactLocationChange('arvore', !contactLocation.arvore)
                    )}
                    
                    {renderCheckboxItem(
                      'arbusto',
                      pt.arbusto,
                      contactLocation.arbusto,
                      () => handleContactLocationChange('arbusto', !contactLocation.arbusto)
                    )}
                    
                    {renderCheckboxItem(
                      'pontoDeAgua',
                      pt.pontoDeAgua,
                      contactLocation.pontoDeAgua,
                      () => handleContactLocationChange('pontoDeAgua', !contactLocation.pontoDeAgua)
                    )}
                    
                    {renderCheckboxItem(
                      'clareira',
                      pt.clareira,
                      contactLocation.clareira,
                      () => handleContactLocationChange('clareira', !contactLocation.clareira)
                    )}
                    
                    {renderCheckboxItem(
                      'parcelaAgricola',
                      pt.parcelaAgricola,
                      contactLocation.parcelaAgricola,
                      () => handleContactLocationChange('parcelaAgricola', !contactLocation.parcelaAgricola)
                    )}
                    
                    {renderCheckboxItem(
                      'outraQual',
                      pt.outraQual,
                      contactLocation.outraQual,
                      () => handleContactLocationChange('outraQual', !contactLocation.outraQual),
                      true,
                      contactLocation.outraQualText,
                      (text) => handleContactLocationChange('outraQualText', text)
                    )}
                  </>
                );
              })()}
            </View>
          </View>

          {/* Images Section */}
          <View style={styles.imagesSectionContainer}>
            <Text style={styles.sectionTitle}>{pt.addImages}</Text>
            
            <View style={styles.imagesContainer}>
              {images.map((imageUri, index) => (
                <View key={index} style={styles.imagePreviewContainer}>
                  <LoadingImage
                    source={{ uri: imageUri }}
                    style={styles.imagePreview}
                  />
                  <View style={styles.imageDarkOverlay} />
                  <TouchableOpacity
                    style={styles.removeImageButton}
                    onPress={() => handleDeletePhoto(index)}
                  >
                    <FontAwesome name="times" size={12} color="white" />
                  </TouchableOpacity>
                  {/* Sync indicator */}
                  <View style={styles.syncIndicator}>
                    <FontAwesome name="clock-o" size={8} color="#fff" />
                  </View>
                </View>
              ))}
              
              {images.length < 6 && (
                <TouchableOpacity
                  style={styles.addImageButton}
                  onPress={handleAddImages}
                >
                  <FontAwesome name="camera" size={24} color="#0996a8" />
                  <Text style={styles.addImageText}>Adicionar</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>
        </ScrollView>

        {/* Info message about image sync */}
        {images.length > 0 && (
          <View style={styles.infoMessage}>
            <FontAwesome name="info-circle" size={12} color="#666" style={{ marginRight: 6 }} />
            <Text style={styles.infoText}>{pt.imagesWillSync}</Text>
          </View>
        )}

        {/* Bottom buttons - ALWAYS VISIBLE regardless of form state */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.cancelButton} onPress={handleClose}>
            <FontAwesome name="times" size={16} color="#fff" style={styles.buttonIcon} />
            <Text style={styles.cancelButtonText}>{pt.cancel}</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={styles.saveButton} 
            onPress={handleSubmit}
          >
            <FontAwesome name="check" size={16} color="#fff" style={styles.buttonIcon} />
            <Text style={styles.saveButtonText}>{pt.save}</Text>
          </TouchableOpacity>
        </View>
      </View>

      <PhotoSelectionAlert
        visible={showPhotoAlert}
        onClose={() => setShowPhotoAlert(false)}
        onTakePhoto={() => {
          setShowPhotoAlert(false);
          pickImage('camera');
        }}
        onChoosePhoto={() => {
          setShowPhotoAlert(false);
          pickImage('gallery');
        }}
      />
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  headerSafeArea: {
    backgroundColor: '#0996a8',
  },
  header: {
    backgroundColor: '#0996a8',
    paddingBottom: 15,
    paddingHorizontal: 20,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  headerIcon: {
    marginRight: 10,
  },
  distanceContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 10,
    backgroundColor: '#e8f4f8',
    borderBottomWidth: 1,
    borderBottomColor: '#d0e6ed',
  },
  distanceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 2,
  },
  distanceText: {
    fontSize: 14,
    color: '#0996a8',
    fontWeight: '500',
    marginLeft: 8,
  },
  compassIcon: {
    marginLeft: 8,
  },
  contentWrapper: {
    flex: 1,
    backgroundColor: '#fff',
  },
  mainContentContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContent: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContentContainer: {
    paddingTop: 15,
    paddingBottom: 100,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginBottom: 15,
    marginTop: 5,
  },
  checkboxSection: {
    marginBottom: 5,
    paddingHorizontal: 20,
  },
  checkboxContainer: {
    marginBottom: 12,
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderRadius: 10,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  checkboxRowSelected: {
    backgroundColor: '#f0f8ff',
    borderColor: '#0996a8',
    shadowOpacity: 0.1,
    elevation: 2,
  },
  checkbox: {
    width: 22,
    height: 22,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    backgroundColor: '#fff',
  },
  checkboxChecked: {
    backgroundColor: '#0996a8',
    borderColor: '#0996a8',
  },
  checkboxLabel: {
    flex: 1,
    fontSize: 16,
    color: '#666',
    lineHeight: 20,
  },
  checkboxLabelSelected: {
    fontWeight: '600',
    color: '#0996a8',
  },
  textInputContainer: {
    marginTop: 0,
  },
  checkboxTextInput: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 10,
    padding: 12,
    minHeight: 80,
    textAlignVertical: 'top',
    backgroundColor: '#fff',
    color: '#666',
    fontSize: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  imagesSectionContainer: {
    marginTop: 5,
    marginBottom: 0,
    padding: 15,
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
    marginTop: 10,
    justifyContent: 'center',
  },
  imagePreviewContainer: {
    position: 'relative',
    width: 100,
    height: 100,
    borderRadius: 8,
    overflow: 'hidden',
  },
  imagePreview: {
    width: '100%',
    height: '100%',
  },
  imageDarkOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '30%',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: 5,
    right: 8,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'rgba(255, 59, 48, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  addImageButton: {
    width: 100,
    height: 100,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#0996a8',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5fcfd',
    gap: 4,
  },
  addImageText: {
    fontSize: 12,
    color: '#0996a8',
    fontWeight: '500',
  },
  buttonContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 20,
    paddingBottom: Platform.OS === 'ios' ? 35 : 20,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    gap: 15,
  },
  cancelButton: {
    flex: 1,
    backgroundColor: '#6B7280',
    borderRadius: 10,
    paddingVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  saveButton: {
    flex: 1,
    backgroundColor: '#0996a8',
    borderRadius: 10,
    paddingVertical: 15,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonIcon: {
    marginRight: 8,
  },
  cancelButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  syncIndicator: {
    position: 'absolute',
    bottom: 4,
    right: 24,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 8,
    padding: 2,
  },
  infoMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 8,
    backgroundColor: '#f8f9fa',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  infoText: {
    fontSize: 12,
    color: '#666',
    flex: 1,
  },
});

export default ContactDetailsModal; 