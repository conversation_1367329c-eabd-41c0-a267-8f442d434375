import React, { useState, useCallback, forwardRef, useImperativeHandle, useRef, useEffect } from 'react';
import { View, Text, TouchableOpacity, TextInput, ScrollView, StyleSheet, ActivityIndicator, Animated, Image } from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import * as ImagePicker from 'expo-image-picker';
import * as Location from 'expo-location';
import LoadingImage from '../LoadingImage';
import PhotoSelectionAlert from '../PhotoSelectionAlert';
import { ReportRef } from './ReportModal';
import { ReportCircumstances, ReportContactLocation, ColaboradorReportData, WeatherConditions } from '@/types/reports';
import { weatherService, WeatherData, WeatherService } from '@/services/weatherService';

// Portuguese translations
const pt = {
  reportContactCircumstances: 'Indique em que circunstância se deu o contacto com rola-brava',
  reportContactLocation: 'Indique onde se deu contacto',
  rolaAdultaCantando: 'Rola adulta a cantar',
  rolaEmVoo: 'Rola em voo',
  adultoPousado: 'Adulto pousado',
  adultoEmDisplay: 'Adulto em display',
  ninhoVazio: 'Ninho vazio',
  nichoOcupado: 'Nicho ocupado',
  ovos: 'Ovos',
  adultoAIncubar: 'Adulto a incubar',
  crias: 'Crias',
  juvenile: 'Juvenil',
  outraQual: 'Outra. Qual?',
  arvore: 'Árvore',
  arbusto: 'Arbusto',
  pontoDeAgua: 'Ponto de Água',
  clareira: 'Clareira',
  parcelaAgricola: 'Parcela Agrícola',
  selectAtLeastOne: 'Por favor, selecione pelo menos uma opção',
  maxImagesReached: 'Máximo de 6 fotos atingido',
  confirmDeletePhoto: 'Tem certeza que deseja eliminar esta foto?',
};

const styles = StyleSheet.create({
  scrollContent: {
    flex: 1,
  },
  scrollContentContainer: {
    paddingTop: 30,
    paddingBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginBottom: 10,
    marginTop: 0,
  },
  checkboxSection: {
    marginBottom: 0,
  },
  checkboxContainer: {
    marginBottom: 12,
  },
  checkboxRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderRadius: 10,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  checkboxRowSelected: {
    backgroundColor: '#f0f8ff',
    borderColor: '#0996a8',
    shadowOpacity: 0.1,
    elevation: 2,
  },
  checkbox: {
    width: 22,
    height: 22,
    borderWidth: 2,
    borderColor: '#D1D5DB',
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
    backgroundColor: '#fff',
  },
  checkboxChecked: {
    backgroundColor: '#0996a8',
    borderColor: '#0996a8',
  },
  checkboxLabel: {
    flex: 1,
    fontSize: 16,
    color: '#666',
    lineHeight: 20,
  },
  checkboxLabelSelected: {
    fontWeight: '600',
    color: '#0996a8',
  },
  textInputContainer: {
    marginTop: 8,
  },
  checkboxTextInput: {
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 10,
    padding: 12,
    minHeight: 80,
    textAlignVertical: 'top',
    backgroundColor: '#fff',
    color: '#666',
    fontSize: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  imagesSectionContainer: {
    marginTop: 20,
    marginBottom: 20,
    padding: 15,
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
    marginTop: 10,
    justifyContent: 'center',
  },
  imagePreviewContainer: {
    position: 'relative',
    width: 100,
    height: 100,
    borderRadius: 8,
    overflow: 'hidden',
  },
  imagePreview: {
    width: '100%',
    height: '100%',
  },
  imageDarkOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '30%',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: 5,
    right: 8,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'rgba(255, 59, 48, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  addImageButton: {
    width: 100,
    height: 100,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#0996a8',
    borderStyle: 'dashed',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5fcfd',
    gap: 4,
  },
  addImageText: {
    fontSize: 12,
    color: '#0996a8',
    fontWeight: '500',
  },
});

// Memoized CheckboxItem component outside the main component
const CheckboxItem = React.memo(({ 
  label, 
  checked, 
  onPress, 
  showTextInput = false, 
  textValue = '', 
  onTextChange 
}: {
  label: string;
  checked: boolean;
  onPress: () => void;
  showTextInput?: boolean;
  textValue?: string;
  onTextChange?: (text: string) => void;
}) => (
  <View style={styles.checkboxContainer}>
    <TouchableOpacity 
      style={[styles.checkboxRow, checked && styles.checkboxRowSelected]} 
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={[styles.checkbox, checked && styles.checkboxChecked]}>
        {checked && <FontAwesome name="check" size={14} color="#fff" />}
      </View>
      <Text style={[styles.checkboxLabel, checked && styles.checkboxLabelSelected]}>{label}</Text>
    </TouchableOpacity>
    {showTextInput && checked && (
      <View style={styles.textInputContainer}>
        <TextInput
          style={styles.checkboxTextInput}
          placeholder="Especifique..."
          placeholderTextColor="#999"
          value={textValue}
          onChangeText={onTextChange}
          multiline
          autoCorrect={false}
          blurOnSubmit={false}
          returnKeyType="done"
          keyboardType="default"
        />
      </View>
    )}
  </View>
));

interface ColaboradorReportProps {
  onSubmit: (data: ColaboradorReportData) => void;
  isLoading: boolean;
  isUploadingImages: boolean;
  uploadProgress: number;
  weatherData?: WeatherData | null;
  showAlert: (config: { type: 'success' | 'error' | 'warning' | 'info'; message: string; onConfirm?: () => void }) => void;
}

const ColaboradorReport = forwardRef<ReportRef, ColaboradorReportProps>(({ onSubmit, isLoading, isUploadingImages, uploadProgress, weatherData, showAlert }, ref) => {
  const scrollViewRef = useRef<ScrollView>(null);
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [isPhotoSelectionVisible, setPhotoSelectionVisible] = useState(false);

  const [circumstances, setCircumstances] = useState<ReportCircumstances>({
    rolaAdultaCantando: false,
    rolaEmVoo: false,
    adultoPousado: false,
    adultoEmDisplay: false,
    ninhoVazio: false,
    nichoOcupado: false,
    ovos: false,
    adultoAIncubar: false,
    crias: false,
    juvenile: false,
    outraQual: false,
    outraQualText: '',
  });

  const [contactLocation, setContactLocation] = useState<ReportContactLocation>({
    arvore: false,
    arbusto: false,
    pontoDeAgua: false,
    clareira: false,
    parcelaAgricola: false,
    outraQual: false,
    outraQualText: '',
  });

  // Animated values for smooth transitions
  const circumstancesAnimation = useRef(new Animated.Value(1)).current;
  const contactLocationAnimation = useRef(new Animated.Value(1)).current;

  // Helper functions to determine which options to show
  const getSelectedCircumstance = () => {
    return Object.entries(circumstances).find(([key, value]) => 
      key !== 'outraQualText' && value === true
    )?.[0] as keyof ReportCircumstances | undefined;
  };

  const getSelectedContactLocation = () => {
    return Object.entries(contactLocation).find(([key, value]) => 
      key !== 'outraQualText' && value === true
    )?.[0] as keyof ReportContactLocation | undefined;
  };

  // Function to handle checkbox changes for circumstances
  const handleCircumstanceChange = (key: keyof ReportCircumstances, value: boolean | string) => {
    if (key === 'outraQualText') {
      // Handle text input separately
      setCircumstances(prev => ({
        ...prev,
        [key]: value as string
      }));
    } else {
      const wasNothingSelected = !getSelectedCircumstance();
      const willBeSelected = value === true;
      
      if (wasNothingSelected && willBeSelected) {
        // Animate options disappearing and scroll to top
        Animated.timing(circumstancesAnimation, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }).start(() => {
          // Update state after animation
          const resetCircumstances: ReportCircumstances = {
            rolaAdultaCantando: false,
            rolaEmVoo: false,
            adultoPousado: false,
            adultoEmDisplay: false,
            ninhoVazio: false,
            nichoOcupado: false,
            ovos: false,
            adultoAIncubar: false,
            crias: false,
            juvenile: false,
            outraQual: false,
            outraQualText: '',
          };
          
          setCircumstances({
            ...resetCircumstances,
            [key]: value as boolean,
            outraQualText: key === 'outraQual' && value ? circumstances.outraQualText : '',
          });
          
          // Animate options appearing
          Animated.timing(circumstancesAnimation, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }).start();
          
          // Scroll to top
          scrollViewRef.current?.scrollTo({ y: 0, animated: true });
        });
      } else {
        // Direct state update for deselection or when already selected
        if (!willBeSelected) {
          // Animate options appearing when deselecting
          Animated.timing(circumstancesAnimation, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true,
          }).start(() => {
            const resetCircumstances: ReportCircumstances = {
              rolaAdultaCantando: false,
              rolaEmVoo: false,
              adultoPousado: false,
              adultoEmDisplay: false,
              ninhoVazio: false,
              nichoOcupado: false,
              ovos: false,
              adultoAIncubar: false,
              crias: false,
              juvenile: false,
              outraQual: false,
              outraQualText: '',
            };
            
            setCircumstances(resetCircumstances);
            
            Animated.timing(circumstancesAnimation, {
              toValue: 1,
              duration: 300,
              useNativeDriver: true,
            }).start();
          });
        } else {
          // Reset all options to false, then set the selected one
          const resetCircumstances: ReportCircumstances = {
            rolaAdultaCantando: false,
            rolaEmVoo: false,
            adultoPousado: false,
            adultoEmDisplay: false,
            ninhoVazio: false,
            nichoOcupado: false,
            ovos: false,
            adultoAIncubar: false,
            crias: false,
            juvenile: false,
            outraQual: false,
            outraQualText: '',
          };
          
          setCircumstances({
            ...resetCircumstances,
            [key]: value as boolean,
            outraQualText: key === 'outraQual' && value ? circumstances.outraQualText : '',
          });
        }
      }
    }
  };

  // Function to handle checkbox changes for contact location
  const handleContactLocationChange = (key: keyof ReportContactLocation, value: boolean | string) => {
    if (key === 'outraQualText') {
      // Handle text input separately
      setContactLocation(prev => ({
        ...prev,
        [key]: value as string
      }));
    } else {
      const wasNothingSelected = !getSelectedContactLocation();
      const willBeSelected = value === true;
      
      if (wasNothingSelected && willBeSelected) {
        // Animate options disappearing
        Animated.timing(contactLocationAnimation, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }).start(() => {
          // Update state after animation
          const resetContactLocation: ReportContactLocation = {
            arvore: false,
            arbusto: false,
            pontoDeAgua: false,
            clareira: false,
            parcelaAgricola: false,
            outraQual: false,
            outraQualText: '',
          };
          
          setContactLocation({
            ...resetContactLocation,
            [key]: value as boolean,
            outraQualText: key === 'outraQual' && value ? contactLocation.outraQualText : '',
          });
          
          // Animate options appearing
          Animated.timing(contactLocationAnimation, {
            toValue: 1,
            duration: 300,
            useNativeDriver: true,
          }).start();
        });
      } else {
        // Direct state update for deselection or when already selected
        if (!willBeSelected) {
          // Animate options appearing when deselecting
          Animated.timing(contactLocationAnimation, {
            toValue: 0,
            duration: 200,
            useNativeDriver: true,
          }).start(() => {
            const resetContactLocation: ReportContactLocation = {
              arvore: false,
              arbusto: false,
              pontoDeAgua: false,
              clareira: false,
              parcelaAgricola: false,
              outraQual: false,
              outraQualText: '',
            };
            
            setContactLocation(resetContactLocation);
            
            Animated.timing(contactLocationAnimation, {
              toValue: 1,
              duration: 300,
              useNativeDriver: true,
            }).start();
          });
        } else {
          // Reset all options to false, then set the selected one
          const resetContactLocation: ReportContactLocation = {
            arvore: false,
            arbusto: false,
            pontoDeAgua: false,
            clareira: false,
            parcelaAgricola: false,
            outraQual: false,
            outraQualText: '',
          };
          
          setContactLocation({
            ...resetContactLocation,
            [key]: value as boolean,
            outraQualText: key === 'outraQual' && value ? contactLocation.outraQualText : '',
          });
        }
      }
    }
  };

  // Function to validate colaborador report
  const validateReport = (): boolean => {
    const hasCircumstance = Object.entries(circumstances).some(([key, value]) => 
      key !== 'outraQualText' && value === true
    );
    const hasContactLocation = Object.entries(contactLocation).some(([key, value]) => 
      key !== 'outraQualText' && value === true
    );
    
    return hasCircumstance && hasContactLocation;
  };

  const handleSubmit = () => {
    if (!validateReport()) {
      showAlert({
        type: 'error',
        message: pt.selectAtLeastOne,
      });
      return;
    }

    const reportData: ColaboradorReportData = {
      circumstances,
      contactLocation,
      images: selectedImages,
    };

    // Only include weather data if it exists and is valid
    if (weatherData) {
      const sanitizedWeather = WeatherService.sanitizeWeatherForFirestore(weatherData);
      if (sanitizedWeather) {
        reportData.weather = sanitizedWeather;
      }
    }

    onSubmit(reportData);
  };

  // Expose submit function to parent
  useImperativeHandle(ref, () => ({
    submit: handleSubmit
  }));

  const handleAddImages = () => {
    if (selectedImages.length >= 6) {
      showAlert({
        type: 'error',
        message: pt.maxImagesReached
      });
      return;
    }

    setPhotoSelectionVisible(true);
  };

  const pickImage = async (source: 'camera' | 'gallery') => {
    try {
      let permissionResult;
      if (source === 'camera') {
        permissionResult = await ImagePicker.requestCameraPermissionsAsync();
      } else {
        permissionResult = await ImagePicker.requestMediaLibraryPermissionsAsync();
      }

      if (!permissionResult.granted) {
        showAlert({
          type: 'error',
          message: 'Permissão negada para acessar a câmera ou galeria.',
        });
        return;
      }

      const result = await (source === 'camera'
        ? ImagePicker.launchCameraAsync({
            allowsEditing: true,
            aspect: [4, 3],
            quality: 0.5,
          })
        : ImagePicker.launchImageLibraryAsync({
            allowsEditing: true,
            aspect: [4, 3],
            quality: 0.5,
          }));

      if (!result.canceled && result.assets[0].uri) {
        setSelectedImages(prev => [...prev, result.assets[0].uri]);
        
        // Auto-scroll to bottom after adding image so user can see the "Adicionar" button
        setTimeout(() => {
          scrollViewRef.current?.scrollToEnd({ animated: true });
        }, 100);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      showAlert({
        type: 'error',
        message: 'Erro ao selecionar imagem.',
      });
    }
  };

  const handleDeletePhoto = (index: number) => {
    showAlert({
      type: 'warning',
      message: pt.confirmDeletePhoto,
      onConfirm: () => {
        const newImages = [...selectedImages];
        newImages.splice(index, 1);
        setSelectedImages(newImages);
      }
    });
  };

  return (
    <ScrollView 
      contentContainerStyle={styles.scrollContentContainer}
      showsVerticalScrollIndicator={false}
      bounces={true}
      keyboardShouldPersistTaps="handled"
      nestedScrollEnabled={true}
      ref={scrollViewRef}
    >
      <Text style={styles.sectionTitle}>{pt.reportContactCircumstances}</Text>
      <Animated.View 
        style={[
          styles.checkboxSection,
          {
            opacity: circumstancesAnimation,
            transform: [{
              scale: circumstancesAnimation.interpolate({
                inputRange: [0, 1],
                outputRange: [0.95, 1],
              })
            }]
          }
        ]}
      >
        {(() => {
          const selectedCircumstance = getSelectedCircumstance();
          
          if (selectedCircumstance) {
            // Show only the selected option
            return (
              <CheckboxItem
                label={pt[selectedCircumstance as keyof typeof pt]}
                checked={true}
                onPress={() => handleCircumstanceChange(selectedCircumstance, false)}
                showTextInput={selectedCircumstance === 'outraQual'}
                textValue={circumstances.outraQualText}
                onTextChange={(text) => handleCircumstanceChange('outraQualText', text)}
              />
            );
          }
          
          // Show all options when none are selected
          return (
            <>
              <CheckboxItem
                label={pt.rolaAdultaCantando}
                checked={circumstances.rolaAdultaCantando}
                onPress={() => handleCircumstanceChange('rolaAdultaCantando', !circumstances.rolaAdultaCantando)}
              />
              <CheckboxItem
                label={pt.rolaEmVoo}
                checked={circumstances.rolaEmVoo}
                onPress={() => handleCircumstanceChange('rolaEmVoo', !circumstances.rolaEmVoo)}
              />
              <CheckboxItem
                label={pt.adultoPousado}
                checked={circumstances.adultoPousado}
                onPress={() => handleCircumstanceChange('adultoPousado', !circumstances.adultoPousado)}
              />
              <CheckboxItem
                label={pt.adultoEmDisplay}
                checked={circumstances.adultoEmDisplay}
                onPress={() => handleCircumstanceChange('adultoEmDisplay', !circumstances.adultoEmDisplay)}
              />
              <CheckboxItem
                label={pt.ninhoVazio}
                checked={circumstances.ninhoVazio}
                onPress={() => handleCircumstanceChange('ninhoVazio', !circumstances.ninhoVazio)}
              />
              <CheckboxItem
                label={pt.nichoOcupado}
                checked={circumstances.nichoOcupado}
                onPress={() => handleCircumstanceChange('nichoOcupado', !circumstances.nichoOcupado)}
              />
              <CheckboxItem
                label={pt.ovos}
                checked={circumstances.ovos}
                onPress={() => handleCircumstanceChange('ovos', !circumstances.ovos)}
              />
              <CheckboxItem
                label={pt.adultoAIncubar}
                checked={circumstances.adultoAIncubar}
                onPress={() => handleCircumstanceChange('adultoAIncubar', !circumstances.adultoAIncubar)}
              />
              <CheckboxItem
                label={pt.crias}
                checked={circumstances.crias}
                onPress={() => handleCircumstanceChange('crias', !circumstances.crias)}
              />
              <CheckboxItem
                label={pt.juvenile}
                checked={circumstances.juvenile}
                onPress={() => handleCircumstanceChange('juvenile', !circumstances.juvenile)}
              />
              <CheckboxItem
                label={pt.outraQual}
                checked={circumstances.outraQual}
                onPress={() => handleCircumstanceChange('outraQual', !circumstances.outraQual)}
                showTextInput={true}
                textValue={circumstances.outraQualText}
                onTextChange={(text) => handleCircumstanceChange('outraQualText', text)}
              />
            </>
          );
        })()}
      </Animated.View>

      <Text style={styles.sectionTitle}>{pt.reportContactLocation}</Text>
      <Animated.View 
        style={[
          styles.checkboxSection,
          {
            opacity: contactLocationAnimation,
            transform: [{
              scale: contactLocationAnimation.interpolate({
                inputRange: [0, 1],
                outputRange: [0.95, 1],
              })
            }]
          }
        ]}
      >
        {(() => {
          const selectedContactLocation = getSelectedContactLocation();
          
          if (selectedContactLocation) {
            // Show only the selected option
            return (
              <CheckboxItem
                label={pt[selectedContactLocation as keyof typeof pt]}
                checked={true}
                onPress={() => handleContactLocationChange(selectedContactLocation, false)}
                showTextInput={selectedContactLocation === 'outraQual'}
                textValue={contactLocation.outraQualText}
                onTextChange={(text) => handleContactLocationChange('outraQualText', text)}
              />
            );
          }
          
          // Show all options when none are selected
          return (
            <>
              <CheckboxItem
                label={pt.arvore}
                checked={contactLocation.arvore}
                onPress={() => handleContactLocationChange('arvore', !contactLocation.arvore)}
              />
              <CheckboxItem
                label={pt.arbusto}
                checked={contactLocation.arbusto}
                onPress={() => handleContactLocationChange('arbusto', !contactLocation.arbusto)}
              />
              <CheckboxItem
                label={pt.pontoDeAgua}
                checked={contactLocation.pontoDeAgua}
                onPress={() => handleContactLocationChange('pontoDeAgua', !contactLocation.pontoDeAgua)}
              />
              <CheckboxItem
                label={pt.clareira}
                checked={contactLocation.clareira}
                onPress={() => handleContactLocationChange('clareira', !contactLocation.clareira)}
              />
              <CheckboxItem
                label={pt.parcelaAgricola}
                checked={contactLocation.parcelaAgricola}
                onPress={() => handleContactLocationChange('parcelaAgricola', !contactLocation.parcelaAgricola)}
              />
              <CheckboxItem
                label={pt.outraQual}
                checked={contactLocation.outraQual}
                onPress={() => handleContactLocationChange('outraQual', !contactLocation.outraQual)}
                showTextInput={true}
                textValue={contactLocation.outraQualText}
                onTextChange={(text) => handleContactLocationChange('outraQualText', text)}
              />
            </>
          );
        })()}
      </Animated.View>

      {/* Images Section */}
      <View style={styles.imagesSectionContainer}>
        <Text style={styles.sectionTitle}>Adicionar Fotos</Text>
        <View style={styles.imagesContainer}>
          {selectedImages.map((uri, index) => (
            <View key={index} style={styles.imagePreviewContainer}>
              <LoadingImage 
                source={{ uri }} 
                style={styles.imagePreview}
                placeholderIcon="camera"
              />
              <TouchableOpacity
                style={styles.removeImageButton}
                onPress={() => handleDeletePhoto(index)}
                activeOpacity={0.7}>
                <FontAwesome name="times" size={14} color="#fff" />
              </TouchableOpacity>
              <View style={styles.imageDarkOverlay} />
            </View>
          ))}
          {selectedImages.length < 6 && (
            <TouchableOpacity
              style={styles.addImageButton}
              onPress={handleAddImages}>
              <FontAwesome name="camera" size={24} color="#0996a8" />
              <Text style={styles.addImageText}>Adicionar</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      <PhotoSelectionAlert
        visible={isPhotoSelectionVisible}
        onClose={() => setPhotoSelectionVisible(false)}
        onTakePhoto={() => {
          setPhotoSelectionVisible(false);
          pickImage('camera');
        }}
        onChoosePhoto={() => {
          setPhotoSelectionVisible(false);
          pickImage('gallery');
        }}
      />
    </ScrollView>
  );
});

export default ColaboradorReport; 