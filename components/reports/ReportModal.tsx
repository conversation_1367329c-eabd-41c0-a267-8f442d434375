import React, { useState, useRef, useImperativeHandle } from 'react';
import { View, Text, TouchableOpacity, Modal, StyleSheet, ActivityIndicator } from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import ColaboradorReport from './ColaboradorReport';
import TecnicosReport from './TecnicosReport';
import { useAuth } from '@/contexts/AuthContext';
import { useCustomAlert } from '@/hooks/useCustomAlert';
import { ColaboradorReportData, TecnicoReportData, TecnicoProtocol } from '@/types/reports';

// Portuguese translations
const pt = {
  cancel: 'Cancelar',
  submit: 'Enviar',
  continue: 'Continuar',
  iniciar: 'Iniciar',
  newReport: 'Novo relatório',
};

export interface ReportRef {
  submit: () => void;
}

interface ReportModalProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (data: ColaboradorReportData | TecnicoReportData) => Promise<void>;
  onStartMonitoring?: (protocol: TecnicoProtocol, startTime: Date, weatherData?: any, observersCount?: number, reportName?: string) => void;
  isLoading: boolean;
  isUploadingImages: boolean;
  uploadProgress: number;
}

export default function ReportModal({
  visible,
  onClose,
  onSubmit,
  onStartMonitoring,
  isLoading,
  isUploadingImages,
  uploadProgress
}: ReportModalProps) {
  const { userRole } = useAuth();
  const { showAlert } = useCustomAlert();
  const reportRef = useRef<ReportRef>(null);
  
  // State for tracking TecnicosReport steps
  const [currentStep, setCurrentStep] = useState<'protocol' | 'weather' | 'monitoring'>('protocol');
  const [selectedProtocol, setSelectedProtocol] = useState<TecnicoProtocol | null>(null);

  const handleSubmit = async (data: ColaboradorReportData | TecnicoReportData) => {
    try {
      await onSubmit(data);
      onClose();
    } catch (error) {
      console.error('Report submission error:', error);
      showAlert({
        type: 'error',
        message: 'Erro ao enviar relatório',
      });
    }
  };

  const handleModalSubmit = () => {
    if (reportRef.current) {
      reportRef.current.submit();
    }
  };

  const handleStepChange = (step: 'protocol' | 'weather' | 'monitoring') => {
    setCurrentStep(step);
    
    // If entering monitoring mode, close modal and start monitoring
    if (step === 'monitoring' && selectedProtocol && onStartMonitoring) {
      // Note: The actual startMonitoring call will come from TecnicosReport via onStartMonitoring prop
      handleClose(); // Close the modal
    }
  };

  const handleProtocolChange = (protocol: TecnicoProtocol | null) => {
    setSelectedProtocol(protocol);
  };

  // Reset state when modal closes
  const handleClose = () => {
    setCurrentStep('protocol');
    setSelectedProtocol(null);
    onClose();
  };

  // Get button text based on current step and user role
  const getButtonText = () => {
    if (userRole === 'tecnico_prorola') {
      switch (currentStep) {
        case 'protocol':
          return pt.continue;
        case 'weather':
          return pt.iniciar;
        case 'monitoring':
          return pt.submit; // This shouldn't be visible as monitoring has its own controls
        default:
          return pt.submit;
      }
    }
    return pt.submit;
  };

  // Get button icon based on current step and user role
  const getButtonIcon = () => {
    if (userRole === 'tecnico_prorola') {
      switch (currentStep) {
        case 'protocol':
          return 'arrow-right';
        case 'weather':
          return 'play';
        case 'monitoring':
          return 'check';
        default:
          return 'check';
      }
    }
    return 'check';
  };

  // Determine if buttons should be hidden (for monitoring step)
  const shouldHideButtons = userRole === 'tecnico_prorola' && currentStep === 'monitoring';
  
  console.log('🔍 ReportModal Debug:', {
    userRole,
    shouldHideButtons,
    currentStep
  });

  // Determine which report component to show based on user role
  const renderReportComponent = () => {
    if (userRole === 'tecnico_prorola') {
      return (
        <TecnicosReport
          ref={reportRef}
          onSubmit={handleSubmit}
          isLoading={isLoading}
          isUploadingImages={isUploadingImages}
          uploadProgress={uploadProgress}
          showAlert={showAlert}
          onStepChange={handleStepChange}
          onProtocolChange={handleProtocolChange}
          onStartMonitoring={onStartMonitoring}
          onCancel={handleClose}
        />
      );
    }
    
    // Default to ColaboradorReport for all other roles
    return (
      <ColaboradorReport
        ref={reportRef}
        onSubmit={handleSubmit}
        isLoading={isLoading}
        isUploadingImages={isUploadingImages}
        uploadProgress={uploadProgress}
        showAlert={showAlert}
      />
    );
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={handleClose}>
      <View style={styles.fullScreenModalContainer}>
        <View style={styles.reportModalContent}>
          <View style={styles.modalHeader}>
            <FontAwesome name="pencil" size={24} color="#0996a8" />
            <Text style={styles.modalHeaderText}>{pt.newReport}</Text>
          </View>

          {/* Scrollable content area with explicit constraints */}
          <View style={styles.contentWrapper}>
            {renderReportComponent()}
          </View>

          {/* Fixed buttons at bottom - hide during monitoring */}
          {!shouldHideButtons && (
            <View style={styles.modalButtonsFixed}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={handleClose}
                disabled={isLoading || isUploadingImages}>
                <FontAwesome name="times" size={16} color="#FFFFFF" style={styles.buttonIcon} />
                <Text style={[styles.modalButtonText, styles.cancelButtonText]}>{pt.cancel}</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.submitButton]}
                onPress={handleModalSubmit}
                disabled={isLoading || isUploadingImages}>
                {(isLoading || isUploadingImages) ? (
                  <View style={styles.loadingContainer}>
                    <ActivityIndicator color="#FFFFFF" />
                  </View>
                ) : (
                  <>
                    <FontAwesome name={getButtonIcon()} size={16} color="#FFFFFF" style={styles.buttonIcon} />
                    <Text style={[styles.modalButtonText, styles.submitButtonText]}>{getButtonText()}</Text>
                  </>
                )}
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  fullScreenModalContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  reportModalContent: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: '90%',
    display: 'flex',
    flexDirection: 'column',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingTop: 0,
    paddingBottom: 15,
    gap: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F0F0F0',
    flexShrink: 0,
  },
  modalHeaderText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#666',
  },
  contentWrapper: {
    flex: 1,
    paddingHorizontal: 20,
  },
  modalButtonsFixed: {
    flexDirection: 'row',
    gap: 20,
    paddingHorizontal: 20,
    paddingVertical: 20,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    backgroundColor: '#FFFFFF',
    flexShrink: 0,
  },
  modalButton: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 15,
    borderRadius: 10,
    borderWidth: 0,
  },
  cancelButton: {
    backgroundColor: '#757575',
  },
  submitButton: {
    backgroundColor: '#0996a8',
  },
  buttonIcon: {
    marginRight: 10,
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  cancelButtonText: {
    color: '#fff',
  },
  submitButtonText: {
    color: '#fff',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
}); 