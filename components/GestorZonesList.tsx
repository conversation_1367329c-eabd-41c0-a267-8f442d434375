import React, { useState, useEffect } from 'react';
import { StyleSheet, View, Text, ScrollView, TouchableOpacity, ActivityIndicator, RefreshControl, Modal, Image, Alert } from 'react-native';
import { FontAwesome } from '@expo/vector-icons';
import { useAuth } from '@/contexts/AuthContext';
import { getFirestore, collection, query, where, getDocs, orderBy } from 'firebase/firestore';
import TrajectoryViewScreen from './TrajectoryViewScreen';
import GestorMobileTrajectoryView from './GestorMobileTrajectoryView';
import GestorTrajectoryCreation from './GestorTrajectoryCreation';

// Configuration: Set to false to disable "Criar Trajeto GPS" button, true to enable
const ENABLE_CREATE_TRAJECTORY_GPS = false;

interface Zone {
  id: string;
  zona: string;
  nomeZona: string;
  nifEntidade: string;
  email: string;
  quotaZona: number;
  localidade: string;
  status: string;
  usedSeals?: number;
  availableQuota?: number;
}

interface Trajeto {
  id: string;
  name: string;
  zoneId: string;
  status: string;
  distance: string;
  createdAt: any;
  updatedAt: any;
  trajetoDate?: string; // The actual trajectory date (DD/MM/YYYY format)
  source?: 'manual' | 'mobile'; // Track trajectory source
  totalDistance?: number; // For mobile trajectories (in meters)
}

interface StatCardProps {
  icon: string;
  number: number;
  label: string;
  color: string;
}

const StatCard: React.FC<StatCardProps> = ({ icon, number, label, color }) => (
  <View style={[styles.statCard, { borderLeftColor: color }]}>
    <View style={[styles.statIcon, { backgroundColor: color }]}>
      <FontAwesome name={icon as any} size={14} color="#FFFFFF" />
    </View>
    <View style={styles.statContent}>
      <Text style={styles.statNumber}>{number}</Text>
      <Text style={styles.statLabel}>{label}</Text>
    </View>
  </View>
);

interface QuotaWidgetProps {
  totalQuota: number;
  usedSeals: number;
  hasTrajectory: boolean;
}

const QuotaWidget: React.FC<QuotaWidgetProps> = ({ totalQuota, usedSeals, hasTrajectory }) => {
  const availableQuota = Math.max(0, totalQuota - usedSeals);
  const percentageUsed = totalQuota > 0 ? Math.round((usedSeals / totalQuota) * 100) : 0;
  const percentageAvailable = 100 - percentageUsed;

  if (!hasTrajectory) {
    return (
      <View style={styles.quotaWidgetDisabled}>
        <View style={styles.quotaInfoRow}>
          <View style={styles.quotaIconContainer}>
            <Image 
              source={require('@/assets/images/icons/dove-icon.png')} 
              style={styles.doveIcon}
              resizeMode="contain"
            />
          </View>
          <View style={styles.quotaTextContainerDisabled}>
            <Text style={styles.quotaTitleDisabled}>QUOTA</Text>
            <Text style={styles.quotaValueDisabled}>Necessita Trajeto</Text>
          </View>
          <View style={styles.quotaStatsContainerDisabled}>
            <Text style={styles.quotaStatsDisabled}>—</Text>
          </View>
        </View>
        <View style={styles.quotaProgressContainerHorizontal}>
          <View style={styles.quotaProgressBarHorizontal}>
            <View style={[styles.quotaProgressDisabled, { width: '100%' }]} />
          </View>
          <Text style={styles.quotaPercentageDisabled}>—</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.quotaWidget}>
      <View style={styles.quotaInfoRow}>
        <View style={[styles.quotaIconContainer, { backgroundColor: '#e0f2fe' }]}>
          <Image 
            source={require('@/assets/images/icons/dove-icon.png')} 
            style={[styles.doveIcon, { tintColor: '#0369a1' }]}
            resizeMode="contain"
          />
        </View>
        <View style={styles.quotaTextContainer}>
          <Text style={styles.quotaTitle}>QUOTA</Text>
          <Text style={styles.quotaValue}>
            {availableQuota.toLocaleString()} disponíveis
          </Text>
        </View>
        <View style={styles.quotaStatsContainer}>
          <Text style={styles.quotaStats}>
            {usedSeals.toLocaleString()}/{totalQuota.toLocaleString()}
          </Text>
        </View>
      </View>
      <View style={styles.quotaProgressContainerHorizontal}>
        <View style={styles.quotaProgressBarHorizontal}>
          <View style={[styles.quotaProgressAvailable, { width: `${percentageAvailable}%` }]} />
          <View style={[styles.quotaProgressUsed, { width: `${percentageUsed}%` }]} />
        </View>
        <Text style={styles.quotaPercentage}>{percentageAvailable}%</Text>
      </View>
    </View>
  );
};

interface ZoneItemProps {
  zone: Zone;
  trajeto: Trajeto | null;
  onViewTrajeto: (trajetoId: string) => void;
  onEditTrajeto: (trajetoId: string, zoneId: string, zoneName: string) => void;
  onCreateTrajeto: (zoneId: string, zoneName: string) => void;
  onRedoTrajeto: (trajetoId: string, zoneId: string, zoneName: string) => void;
}

const ZoneItem: React.FC<ZoneItemProps> = ({ zone, trajeto, onViewTrajeto, onEditTrajeto, onCreateTrajeto, onRedoTrajeto }) => {
  const hasTrajetoActive = trajeto !== null;
  
  const formatDate = (timestamp: any) => {
    if (!timestamp) return '—';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString('pt-PT');
  };

  const getDisplayDate = (trajeto: Trajeto) => {
    // Use trajectory date if available, otherwise use createdAt (same logic as gestores web)
    if (trajeto.trajetoDate) {
      return trajeto.trajetoDate;
    }
    return formatDate(trajeto.createdAt);
  };

  const getDisplayDistance = (trajeto: Trajeto) => {
    // For mobile trajectories, use totalDistance (in meters), convert to km
    if (trajeto.source === 'mobile' && trajeto.totalDistance) {
      return `${(trajeto.totalDistance / 1000).toFixed(2)} km`;
    }
    // For manual trajectories, use distance field
    return trajeto.distance || '—';
  };

  return (
    <View style={styles.zoneItem}>
      <View style={styles.zoneHeader}>
        <Text style={styles.zoneName}>{zone.nomeZona}</Text>
        <View style={styles.zoneNumber}>
          <Text style={styles.zoneNumberText}>Zona {zone.zona}</Text>
        </View>
      </View>

      <View style={styles.zoneStatusSection}>
        <View style={[styles.statusIndicator, hasTrajetoActive ? styles.statusComplete : styles.statusPending]}>
          <FontAwesome 
            name={hasTrajetoActive ? "check-circle" : "exclamation-triangle"} 
            size={14} 
            color={hasTrajetoActive ? "#065f46" : "#92400e"} 
          />
          <Text style={[styles.statusText, hasTrajetoActive ? styles.statusCompleteText : styles.statusPendingText]}>
            {hasTrajetoActive 
              ? `Trajeto ${trajeto?.source === 'mobile' ? 'GPS' : 'Manual'}` 
              : 'Necessita Trajeto'
            }
          </Text>
        </View>
      </View>

      {hasTrajetoActive && trajeto && (
        <View style={styles.zoneMetaInline}>
          <View style={styles.metaItem}>
            <FontAwesome name="road" size={12} color="#0996a8" />
            <Text style={styles.metaText}>Distância: {getDisplayDistance(trajeto)}</Text>
          </View>
          <View style={styles.metaItem}>
            <FontAwesome name="calendar" size={12} color="#0996a8" />
            <Text style={styles.metaText}>
              {getDisplayDate(trajeto)}
              {trajeto.updatedAt && trajeto.updatedAt !== trajeto.createdAt && 
                ` | Editado: ${formatDate(trajeto.updatedAt)}`
              }
            </Text>
          </View>
        </View>
      )}

      {/* Quota Widget - positioned above action buttons */}
      <QuotaWidget 
        totalQuota={zone.quotaZona || 0}
        usedSeals={zone.usedSeals || 0}
        hasTrajectory={hasTrajetoActive}
      />

      <View style={styles.zoneActions}>
        {hasTrajetoActive && trajeto ? (
          <>
            {trajeto.source === 'mobile' ? (
              <View style={styles.mobileTrajetoActions}>
                <TouchableOpacity 
                  style={[styles.actionButton, styles.primaryButton]} 
                  onPress={() => onViewTrajeto(trajeto.id)}
                >
                  <FontAwesome name="mobile" size={16} color="#FFFFFF" />
                  <Text style={styles.actionButtonText}>Ver Trajeto GPS</Text>
                </TouchableOpacity>
                <TouchableOpacity 
                  style={[styles.actionButton, styles.warningButton]} 
                  onPress={() => onRedoTrajeto(trajeto.id, zone.id, zone.nomeZona)}
                >
                  <FontAwesome name="refresh" size={16} color="#FFFFFF" />
                  <Text style={styles.actionButtonText}>Refazer Trajeto GPS</Text>
                </TouchableOpacity>
              </View>
            ) : (
              // Manual trajectory - only show view button
              <TouchableOpacity 
                style={[styles.actionButton, styles.primaryButton]} 
                onPress={() => onViewTrajeto(trajeto.id)}
              >
                <FontAwesome name="eye" size={16} color="#FFFFFF" />
                <Text style={styles.actionButtonText}>Ver Trajeto Manual</Text>
              </TouchableOpacity>
            )}
          </>
        ) : (
          <TouchableOpacity 
            style={[
              styles.actionButton, 
              ENABLE_CREATE_TRAJECTORY_GPS ? styles.successButton : styles.disabledButton
            ]} 
            onPress={ENABLE_CREATE_TRAJECTORY_GPS ? () => onCreateTrajeto(zone.id, zone.nomeZona) : undefined}
            disabled={!ENABLE_CREATE_TRAJECTORY_GPS}
          >
            <FontAwesome name="plus" size={16} color={ENABLE_CREATE_TRAJECTORY_GPS ? "#FFFFFF" : "#999999"} />
            <Text style={[
              styles.actionButtonText,
              !ENABLE_CREATE_TRAJECTORY_GPS && styles.disabledButtonText
            ]}>
              Criar Trajeto GPS
            </Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

export default function GestorZonesList() {
  const { user } = useAuth();
  const [zones, setZones] = useState<Zone[]>([]);
  const [trajetos, setTrajetos] = useState<Trajeto[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedTrajectory, setSelectedTrajectory] = useState<{id: string, source: 'manual' | 'mobile'} | null>(null);
  const [showTrajectoryCreation, setShowTrajectoryCreation] = useState(false);
  const [selectedZoneForCreation, setSelectedZoneForCreation] = useState<{id: string, name: string} | null>(null);
  const [selectedTrajectoryToRedo, setSelectedTrajectoryToRedo] = useState<string | null>(null);
  const [showRedoConfirmModal, setShowRedoConfirmModal] = useState(false);
  const [redoTrajectoryData, setRedoTrajectoryData] = useState<{id: string, zoneId: string, zoneName: string} | null>(null);
  const [showHelpModal, setShowHelpModal] = useState(false);

  // Function to calculate used seals for a zone
  const getUsedSealsCount = async (db: any, zoneId: string): Promise<number> => {
    try {
      const jornadasQuery = query(
        collection(db, 'jornadasCaca'),
        where('zonaId', '==', zoneId)
      );
      const jornadasSnapshot = await getDocs(jornadasQuery);
      
      let totalUsedSeals = 0;
      
      jornadasSnapshot.forEach((doc) => {
        const jornada = doc.data();
        const selosAtribuidos = jornada.selosAtribuidos || '';
        
        if (selosAtribuidos) {
          if (typeof selosAtribuidos === 'string') {
            try {
              // Try to parse as JSON
              const selosJson = JSON.parse(selosAtribuidos);
              if (Array.isArray(selosJson)) {
                totalUsedSeals += selosJson.length;
              }
            } catch {
              // If not JSON, count comma-separated values
              const selosArray = selosAtribuidos.split(',').filter(s => s.trim());
              totalUsedSeals += selosArray.length;
            }
          }
        }
      });
      
      return totalUsedSeals;
    } catch (err) {
      console.error('Error calculating used seals for zone:', zoneId, err);
      return 0;
    }
  };

  const fetchData = async () => {
    if (!user?.email) return;

    try {
      const db = getFirestore();
      
      // Get zones by user's NIF (assuming it's stored in user profile)
      // For now, we'll use email to match zones
      const zonesQuery = query(
        collection(db, 'zonasCaca'),
        where('email', '==', user.email)
      );
      const zonesSnapshot = await getDocs(zonesQuery);
      const zonesData: Zone[] = [];
      
      // Process zones and calculate used seals for each
      for (const doc of zonesSnapshot.docs) {
        const zoneData = { id: doc.id, ...doc.data() } as Zone;
        
        // Calculate used seals for this zone
        const usedSeals = await getUsedSealsCount(db, doc.id);
        zoneData.usedSeals = usedSeals;
        zoneData.availableQuota = Math.max(0, (zoneData.quotaZona || 0) - usedSeals);
        
        zonesData.push(zoneData);
      }

      // Get manual trajetos created by this user from 'zonas' collection
      const manualTrajetosQuery = query(
        collection(db, 'zonas'),
        where('createdBy', '==', user.uid)
      );
      const manualTrajetosSnapshot = await getDocs(manualTrajetosQuery);
      const manualTrajetosData: Trajeto[] = [];
      
      manualTrajetosSnapshot.forEach((doc) => {
        manualTrajetosData.push({
          id: doc.id,
          source: 'manual',
          ...doc.data()
        } as Trajeto);
      });

      // Get mobile trajetos created by this user from 'gestorMobile_trajetos' collection
      const mobileTrajetosQuery = query(
        collection(db, 'gestorMobile_trajetos'),
        where('userId', '==', user.uid)
      );
      const mobileTrajetosSnapshot = await getDocs(mobileTrajetosQuery);
      const mobileTrajetosData: Trajeto[] = [];
      
      mobileTrajetosSnapshot.forEach((doc) => {
        const data = doc.data();
        // For mobile trajectories, calculate distance from totalDistance (stored in meters)
        const totalDistance = data.totalDistance || 0;
        const distanceKm = totalDistance > 0 ? `${(totalDistance / 1000).toFixed(2)} km` : '0 km';
        
        mobileTrajetosData.push({
          id: doc.id,
          source: 'mobile',
          distance: distanceKm, // Add formatted distance
          totalDistance: totalDistance, // Keep original value
          ...data
        } as Trajeto);
      });

      // Combine both trajectory types
      const allTrajetos = [...manualTrajetosData, ...mobileTrajetosData];

      setZones(zonesData);
      setTrajetos(allTrajetos);
      setError(null);
    } catch (err) {
      console.error('Error fetching gestor data:', err);
      setError('Erro ao carregar dados das zonas');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [user]);

  const onRefresh = () => {
    setRefreshing(true);
    fetchData();
  };

  const handleViewTrajeto = (trajetoId: string) => {
    // Find the trajectory to get its source
    const trajectory = trajetos.find(t => t.id === trajetoId);
    if (trajectory) {
      setSelectedTrajectory({ id: trajetoId, source: trajectory.source || 'manual' });
    }
  };

  const handleEditTrajeto = (trajetoId: string, zoneId: string, zoneName: string) => {
    // TODO: Navigate to trajeto edit screen
    console.log('Edit trajeto:', trajetoId, zoneId, zoneName);
  };

  const handleCreateTrajeto = (zoneId: string, zoneName: string) => {
    setSelectedZoneForCreation({ id: zoneId, name: zoneName });
    setShowTrajectoryCreation(true);
  };

  const handleRedoTrajeto = (trajetoId: string, zoneId: string, zoneName: string) => {
    // Show custom confirmation modal
    setRedoTrajectoryData({ id: trajetoId, zoneId, zoneName });
    setShowRedoConfirmModal(true);
  };

  const confirmRedoTrajeto = () => {
    if (redoTrajectoryData) {
      // Check network connectivity before starting redo
      // Note: This is a basic check - the actual network validation happens in GestorActiveMonitoring
      // where we have access to the NetInfo state
      
      // Start trajectory creation in "redo" mode
      setSelectedZoneForCreation({ id: redoTrajectoryData.zoneId, name: redoTrajectoryData.zoneName });
      setSelectedTrajectoryToRedo(redoTrajectoryData.id);
      setShowTrajectoryCreation(true);
      setShowRedoConfirmModal(false);
      setRedoTrajectoryData(null);
    }
  };

  const cancelRedoTrajeto = () => {
    setShowRedoConfirmModal(false);
    setRedoTrajectoryData(null);
  };

  const showHelp = () => {
    setShowHelpModal(true);
  };

  const closeHelp = () => {
    setShowHelpModal(false);
  };

  const closeTrajectoryView = () => {
    setSelectedTrajectory(null);
  };

  const closeTrajectoryCreation = () => {
    console.log('🔄 ZONES_LIST: closeTrajectoryCreation called');
    console.log('🔄 ZONES_LIST: Setting showTrajectoryCreation to false');
    setShowTrajectoryCreation(false);
    setSelectedZoneForCreation(null);
    setSelectedTrajectoryToRedo(null);
    // Refresh data to show newly created trajectory
    fetchData();
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0996a8" />
        <Text style={styles.loadingText}>A carregar zonas...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <FontAwesome name="exclamation-triangle" size={50} color="#FF3B30" />
        <Text style={styles.errorText}>{error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchData}>
          <Text style={styles.retryButtonText}>Tentar Novamente</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Calculate statistics
  const totalZones = zones.length;
  const totalTrajetos = trajetos.length;
  const operationalZones = zones.filter(zone => 
    trajetos.some(trajeto => trajeto.zoneId === zone.id)
  ).length;
  const needTrajeto = totalZones - operationalZones;

  return (
    <View style={styles.screenContainer}>

            <ScrollView 
        style={styles.container}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Statistics Cards with help button */}
        <View style={styles.statsContainer}>
          {/* Help button positioned above Trajetos Criados card */}
          <TouchableOpacity 
            style={styles.helpButtonStats}
            onPress={showHelp}
            activeOpacity={0.7}
          >
            <FontAwesome name="question-circle" size={14} color="#0996a8" />
            <Text style={styles.helpButtonStatsText}>Ajuda</Text>
          </TouchableOpacity>
        <StatCard 
          icon="binoculars" 
          number={totalZones} 
          label="Total de Zonas" 
          color="#0996a8" 
        />
        <StatCard 
          icon="road" 
          number={totalTrajetos} 
          label="Trajetos Criados" 
          color="#16a34a" 
        />
        <StatCard 
          icon="check-circle" 
          number={operationalZones} 
          label="Zonas Operacionais" 
          color="#9333ea" 
        />
        <StatCard 
          icon="exclamation-triangle" 
          number={needTrajeto} 
          label="Precisam Trajeto" 
          color="#ea580c" 
        />
      </View>

      {/* Zones List */}
      <View style={styles.zonesContainer}>
        {zones.length === 0 ? (
          <View style={styles.emptyState}>
            <FontAwesome name="binoculars" size={50} color="#94a3b8" />
            <Text style={styles.emptyStateTitle}>Nenhuma zona encontrada</Text>
            <Text style={styles.emptyStateText}>
              Não foram encontradas zonas de caça associadas à sua conta.
            </Text>
          </View>
        ) : (
          zones.map((zone) => {
            const trajeto = trajetos.find(t => t.zoneId === zone.id) || null;
            return (
              <ZoneItem
                key={zone.id}
                zone={zone}
                trajeto={trajeto}
                onViewTrajeto={handleViewTrajeto}
                onEditTrajeto={handleEditTrajeto}
                onCreateTrajeto={handleCreateTrajeto}
                onRedoTrajeto={handleRedoTrajeto}
              />
            );
          })
        )}
      </View>
      
      {/* Trajectory View Modal */}
      <Modal
        visible={selectedTrajectory !== null}
        animationType="slide"
        presentationStyle="fullScreen"
        onRequestClose={closeTrajectoryView}
      >
        {selectedTrajectory && selectedTrajectory.source === 'mobile' ? (
          <GestorMobileTrajectoryView
            trajectoryId={selectedTrajectory.id}
            onClose={closeTrajectoryView}
          />
        ) : selectedTrajectory && (
          <TrajectoryViewScreen
            trajectoryId={selectedTrajectory.id}
            onClose={closeTrajectoryView}
          />
        )}
      </Modal>

      {/* Trajectory Creation Modal */}
      <GestorTrajectoryCreation
        visible={showTrajectoryCreation}
        zoneId={selectedZoneForCreation?.id || ''}
        zoneName={selectedZoneForCreation?.name || ''}
        trajectoryToRedoId={selectedTrajectoryToRedo}
        onClose={() => {
          console.log('🔄 ZONES_LIST: GestorTrajectoryCreation onClose called');
          closeTrajectoryCreation();
        }}
      />

      {/* Redo Confirmation Modal */}
      <Modal
        visible={showRedoConfirmModal}
        transparent={true}
        animationType="fade"
        onRequestClose={cancelRedoTrajeto}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.redoModalContainer}>
            <View style={styles.redoModalHeader}>
              <FontAwesome name="refresh" size={24} color="#0996a8" />
              <Text style={styles.redoModalTitle}>Refazer Trajeto GPS</Text>
            </View>
            
            <View style={styles.redoModalContent}>
              <Text style={styles.redoModalQuestion}>
                Tem a certeza que pretende refazer o trajeto GPS para "{redoTrajectoryData?.zoneName}"?
              </Text>
              
              <View style={styles.warningContainer}>
                <View style={styles.warningHeader}>
                  <FontAwesome name="exclamation-triangle" size={16} color="#f59e0b" />
                  <Text style={styles.warningTitle}>ATENÇÃO:</Text>
                </View>
                
                <View style={styles.warningList}>
                  <View style={styles.warningItem}>
                    <Text style={styles.warningBullet}>•</Text>
                    <Text style={styles.warningText}>O trajeto atual será completamente substituído</Text>
                  </View>
                  <View style={styles.warningItem}>
                    <Text style={styles.warningBullet}>•</Text>
                    <Text style={styles.warningText}>Todos os contactos existentes serão removidos</Text>
                  </View>
                  <View style={styles.warningItem}>
                    <Text style={styles.warningBullet}>•</Text>
                    <Text style={styles.warningText}>Todas as fotos atuais serão eliminadas</Text>
                  </View>
                  <View style={styles.warningItem}>
                    <Text style={styles.warningBullet}>•</Text>
                    <Text style={styles.warningText}>Será iniciada uma nova sessão de gravação GPS</Text>
                  </View>
                </View>
                
                <Text style={styles.warningFooter}>Esta ação não pode ser desfeita.</Text>
              </View>
            </View>
            
            <View style={styles.redoModalButtons}>
              <TouchableOpacity
                style={styles.redoModalCancelButton}
                onPress={cancelRedoTrajeto}
                activeOpacity={0.7}
              >
                <FontAwesome name="times" size={16} color="#6b7280" />
                <Text style={styles.redoModalCancelText}>Cancelar</Text>
              </TouchableOpacity>
              
              <TouchableOpacity
                style={styles.redoModalConfirmButton}
                onPress={confirmRedoTrajeto}
                activeOpacity={0.7}
              >
                <FontAwesome name="refresh" size={16} color="#FFFFFF" />
                <Text style={styles.redoModalConfirmText}>Refazer</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* Help Modal */}
      <Modal
        visible={showHelpModal}
        transparent={true}
        animationType="fade"
        onRequestClose={closeHelp}
      >
        <View style={styles.helpModalOverlay}>
          <View style={styles.helpModalContainer}>
            <View style={styles.helpModalHeader}>
              <FontAwesome name="question-circle" size={24} color="#0996a8" />
              <Text style={styles.helpModalTitle}>Ajuda - Gestão de Zonas</Text>
            </View>
            
            <ScrollView style={styles.helpModalContent} showsVerticalScrollIndicator={false}>
              {/* Available Functions Section */}
              <View style={styles.helpSection}>
                <View style={styles.helpSectionHeader}>
                  <FontAwesome name="cogs" size={16} color="#0996a8" />
                  <Text style={styles.helpSectionTitle}>Funcionalidades disponíveis</Text>
                </View>
                <View style={styles.helpSectionBody}>
                  <View style={styles.helpItem}>
                    <FontAwesome name="plus" size={14} color="#16a34a" />
                    <Text style={styles.helpItemText}>
                      <Text style={styles.helpItemBold}>Criar Trajeto GPS:</Text> Adicione um trajeto GPS a zonas que ainda não têm
                    </Text>
                  </View>
                  <View style={styles.helpItem}>
                    <FontAwesome name="eye" size={14} color="#0996a8" />
                    <Text style={styles.helpItemText}>
                      <Text style={styles.helpItemBold}>Ver Trajeto:</Text> Visualize trajetos já existentes com todos os detalhes
                    </Text>
                  </View>
                  <View style={styles.helpItem}>
                    <FontAwesome name="mobile" size={14} color="#0996a8" />
                    <Text style={styles.helpItemText}>
                      <Text style={styles.helpItemBold}>Ver Trajeto GPS:</Text> Visualize trajetos GPS criados no telemóvel
                    </Text>
                  </View>
                  <View style={styles.helpItem}>
                    <FontAwesome name="refresh" size={14} color="#f59e0b" />
                    <Text style={styles.helpItemText}>
                      <Text style={styles.helpItemBold}>Refazer Trajeto GPS:</Text> Substitua completamente um trajeto GPS existente
                    </Text>
                  </View>
                </View>
              </View>

              {/* Zone States Section */}
              <View style={styles.helpSection}>
                <View style={styles.helpSectionHeader}>
                  <FontAwesome name="info-circle" size={16} color="#0996a8" />
                  <Text style={styles.helpSectionTitle}>Estados das zonas</Text>
                </View>
                <View style={styles.helpSectionBody}>
                  <View style={styles.helpItem}>
                    <FontAwesome name="check-circle" size={14} color="#16a34a" />
                    <Text style={styles.helpItemText}>
                      <Text style={styles.helpItemBold}>Trajeto Ativo:</Text> Zona tem trajeto configurado e operacional
                    </Text>
                  </View>
                  <View style={styles.helpItem}>
                    <FontAwesome name="exclamation-triangle" size={14} color="#f59e0b" />
                    <Text style={styles.helpItemText}>
                      <Text style={styles.helpItemBold}>Necessita Trajeto:</Text> Zona ainda não tem trajeto definido
                    </Text>
                  </View>
                </View>
              </View>

              {/* Quota Information Section */}
              <View style={styles.helpSection}>
                <View style={styles.helpSectionHeader}>
                  <Image 
                    source={require('@/assets/images/icons/dove-icon.png')} 
                    style={[styles.helpSectionIcon, { tintColor: '#0996a8' }]}
                    resizeMode="contain"
                  />
                  <Text style={styles.helpSectionTitle}>Informação de Quota</Text>
                </View>
                <View style={styles.helpSectionBody}>
                  <View style={styles.helpItem}>
                    <FontAwesome name="bar-chart" size={14} color="#0996a8" />
                    <Text style={styles.helpItemText}>
                      <Text style={styles.helpItemBold}>Widget de Quota:</Text> Mostra selos disponíveis vs utilizados para cada zona
                    </Text>
                  </View>
                  <View style={styles.helpItem}>
                    <FontAwesome name="ban" size={14} color="#6b7280" />
                    <Text style={styles.helpItemText}>
                      <Text style={styles.helpItemBold}>Quota Desativada:</Text> Aparece quando a zona não tem trajeto definido
                    </Text>
                  </View>
                </View>
              </View>

              {/* Statistics Section */}
              <View style={styles.helpSection}>
                <View style={styles.helpSectionHeader}>
                  <FontAwesome name="line-chart" size={16} color="#0996a8" />
                  <Text style={styles.helpSectionTitle}>Estatísticas</Text>
                </View>
                <View style={styles.helpSectionBody}>
                  <View style={styles.helpItem}>
                    <FontAwesome name="binoculars" size={14} color="#0996a8" />
                    <Text style={styles.helpItemText}>
                      <Text style={styles.helpItemBold}>Total de Zonas:</Text> Número total de zonas registadas na sua conta
                    </Text>
                  </View>
                  <View style={styles.helpItem}>
                    <FontAwesome name="road" size={14} color="#16a34a" />
                    <Text style={styles.helpItemText}>
                      <Text style={styles.helpItemBold}>Trajetos Criados:</Text> Número de trajetos já configurados
                    </Text>
                  </View>
                  <View style={styles.helpItem}>
                    <FontAwesome name="check-circle" size={14} color="#9333ea" />
                    <Text style={styles.helpItemText}>
                      <Text style={styles.helpItemBold}>Zonas Operacionais:</Text> Zonas com trajetos ativos e funcionais
                    </Text>
                  </View>
                  <View style={styles.helpItem}>
                    <FontAwesome name="exclamation-triangle" size={14} color="#ea580c" />
                    <Text style={styles.helpItemText}>
                      <Text style={styles.helpItemBold}>Precisam Trajeto:</Text> Zonas que ainda necessitam de trajeto
                    </Text>
                  </View>
                </View>
              </View>

              {/* Support Section */}
              <View style={styles.helpSection}>
                <View style={styles.helpSectionHeader}>
                  <FontAwesome name="envelope" size={16} color="#0996a8" />
                  <Text style={styles.helpSectionTitle}>Precisa de Ajuda?</Text>
                </View>
                <View style={styles.helpSectionBody}>
                  <View style={styles.supportContainer}>
                    <Text style={styles.supportText}>
                      Se tiver dúvidas ou problemas, contacte o nosso suporte técnico:
                    </Text>
                    <Text style={styles.supportEmail}><EMAIL></Text>
                  </View>
                </View>
              </View>
            </ScrollView>
            
            <View style={styles.helpModalFooter}>
              <TouchableOpacity
                style={styles.helpModalCloseButton}
                onPress={closeHelp}
                activeOpacity={0.7}
              >
                <FontAwesome name="check" size={16} color="#FFFFFF" />
                <Text style={styles.helpModalCloseText}>Entendi</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  screenContainer: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
    paddingTop: 35,
    marginBottom: 30,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#64748b',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
    padding: 20,
  },
  errorText: {
    marginTop: 16,
    fontSize: 16,
    color: '#64748b',
    textAlign: 'center',
  },
  retryButton: {
    marginTop: 16,
    backgroundColor: '#0996a8',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 12,
    gap: 8,
    position: 'relative',
    marginTop: 15,
  },
  statCard: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 6,
    flexDirection: 'row',
    alignItems: 'center',
    borderLeftWidth: 3,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 3,
  },
  helpButtonStats: {
    position: 'absolute',
    top: -30,
    right: 12,
    zIndex: 10,
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f9ff',
    paddingHorizontal: 8,
    paddingVertical: 4,
    height: 24,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#0996a8',
    gap: 4,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 3,
  },
  helpButtonStatsText: {
    fontSize: 10,
    fontWeight: '600',
    color: '#0996a8',
  },
  statIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 6,
  },
  statContent: {
    flex: 1,
  },
  statNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1e293b',
  },
  statLabel: {
    fontSize: 9,
    color: '#64748b',
    marginTop: 0,
    lineHeight: 11,
  },
  zonesContainer: {
    padding: 16,
    paddingTop: 10,
    paddingBottom: 30,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    marginTop: 20,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1e293b',
    marginTop: 16,
  },
  emptyStateText: {
    fontSize: 14,
    color: '#64748b',
    textAlign: 'center',
    marginTop: 8,
  },
  zoneItem: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 25,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
    position: 'relative',
  },
  zoneHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
    marginTop: 8,
  },
  zoneName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1e293b',
    flex: 1,
  },
  zoneNumber: {
    backgroundColor: '#0996a8',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 6,
  },
  zoneNumberText: {
    fontSize: 8,
    fontWeight: '600',
    color: '#FFFFFF',
    textTransform: 'uppercase',
  },
  zoneStatusSection: {
    marginBottom: 12,
    alignItems: 'center',
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 16,
    gap: 6,
    alignSelf: 'center',
  },
  statusComplete: {
    backgroundColor: '#dcfce7',
    borderWidth: 1,
    borderColor: '#16a34a',
  },
  statusPending: {
    backgroundColor: '#fef3c7',
    borderWidth: 1,
    borderColor: '#f59e0b',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  statusCompleteText: {
    color: '#065f46',
  },
  statusPendingText: {
    color: '#92400e',
  },
  zoneMetaInline: {
    flexDirection: 'row',
    gap: 8,
    marginBottom: 12,
    flexWrap: 'wrap',
    justifyContent: 'center',
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
    borderWidth: 1,
    borderColor: '#e2e8f0',
  },
  metaText: {
    fontSize: 10,
    color: '#374151',
    fontWeight: '500',
  },
  zoneActions: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 12,
    paddingHorizontal: 20,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 12,
    gap: 8,
    width: 180,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  primaryButton: {
    backgroundColor: '#0996a8',
  },
  successButton: {
    backgroundColor: '#16a34a',
  },
  warningButton: {
    backgroundColor: '#f59e0b',
  },
  disabledButton: {
    backgroundColor: '#e5e7eb',
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '600',
  },
  disabledButtonText: {
    color: '#999999',
  },
  quotaWidgetDisabled: {
    backgroundColor: '#f8fafc',
    borderWidth: 1,
    borderColor: '#e2e8f0',
    padding: 10,
    borderRadius: 6,
    marginBottom: 16,
    width: '100%',
  },
  quotaInfoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  quotaIconContainer: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'rgba(100, 116, 139, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  quotaTextContainer: {
    flex: 1,
    marginLeft: 6,
    alignItems: 'center',
  },
  quotaTitleDisabled: {
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    color: '#64748b',
  },
  quotaValueDisabled: {
    fontSize: 12,
    fontWeight: '600',
    color: '#64748b',
    marginTop: 1,
  },
  quotaTextContainerDisabled: {
    flex: 1,
    marginLeft: 6,
    alignItems: 'center',
  },
  quotaStatsContainerDisabled: {
    backgroundColor: '#f1f5f9',
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 4,
  },
  quotaStatsDisabled: {
    fontSize: 10,
    fontWeight: '700',
    color: '#64748b',
  },
  quotaProgressContainerHorizontal: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  quotaProgressBarHorizontal: {
    flex: 1,
    height: 6,
    backgroundColor: '#e5e7eb',
    borderRadius: 3,
    overflow: 'hidden',
    flexDirection: 'row',
  },
  quotaProgressDisabled: {
    backgroundColor: 'rgba(100, 116, 139, 0.2)',
  },
  quotaPercentageDisabled: {
    fontSize: 10,
    fontWeight: '700',
    color: '#64748b',
    minWidth: 30,
    textAlign: 'right',
  },
  quotaWidget: {
    backgroundColor: '#f0f9ff',
    borderWidth: 1,
    borderColor: '#0ea5e9',
    padding: 10,
    borderRadius: 6,
    marginBottom: 16,
    width: '100%',
  },
  quotaTitle: {
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    color: '#0369a1',
  },
  quotaValue: {
    fontSize: 12,
    fontWeight: '600',
    color: '#0c4a6e',
    marginTop: 1,
  },
  quotaStatsContainer: {
    backgroundColor: '#e0f2fe',
    paddingHorizontal: 8,
    paddingVertical: 3,
    borderRadius: 4,
  },
  quotaStats: {
    fontSize: 10,
    fontWeight: '700',
    color: '#0369a1',
  },
  quotaProgressAvailable: {
    backgroundColor: '#10b981',
    height: '100%',
  },
  quotaProgressUsed: {
    backgroundColor: '#ef4444',
    height: '100%',
  },
  quotaPercentage: {
    fontSize: 10,
    fontWeight: '700',
    color: '#0369a1',
    minWidth: 30,
    textAlign: 'right',
  },
  doveIcon: {
    width: 14,
    height: 14,
  },
  mobileTrajetoActions: {
    flexDirection: 'column',
    gap: 12,
    alignItems: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  redoModalContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    width: '100%',
    maxWidth: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  redoModalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    gap: 12,
  },
  redoModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1e293b',
    textAlign: 'center',
  },
  redoModalContent: {
    padding: 20,
  },
  redoModalQuestion: {
    fontSize: 16,
    color: '#374151',
    marginBottom: 20,
    lineHeight: 22,
  },
  warningContainer: {
    backgroundColor: '#fef3c7',
    borderWidth: 1,
    borderColor: '#f59e0b',
    borderRadius: 8,
    padding: 16,
  },
  warningHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  warningTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#92400e',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  warningList: {
    marginBottom: 12,
  },
  warningItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
    gap: 8,
  },
  warningBullet: {
    fontSize: 14,
    color: '#92400e',
    fontWeight: 'bold',
    marginTop: 1,
  },
  warningText: {
    fontSize: 14,
    color: '#92400e',
    flex: 1,
    lineHeight: 20,
  },
  warningFooter: {
    fontSize: 14,
    color: '#92400e',
    fontWeight: '600',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  redoModalButtons: {
    flexDirection: 'row',
    padding: 20,
    gap: 0,
    justifyContent: 'space-between',
  },
  redoModalCancelButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 8,
    gap: 8,
    backgroundColor: '#f1f5f9',
    borderWidth: 1,
    borderColor: '#e2e8f0',
    flex: 1,
    marginRight: 6,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  redoModalCancelText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6b7280',
  },
  redoModalConfirmButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 8,
    gap: 8,
    backgroundColor: '#f59e0b',
    flex: 1,
    marginLeft: 6,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  redoModalConfirmText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  // Header styles
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    marginBottom: 8,
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1e293b',
  },
  helpButton: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f9ff',
    width: 28,
    height: 28,
    borderRadius: 14,
    borderWidth: 1,
    borderColor: '#0996a8',
  },
  headerOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 100,
    zIndex: 1000,
    justifyContent: 'center',
    alignItems: 'flex-end',
    paddingRight: 16,
    paddingTop: 50,
    pointerEvents: 'box-none',
  },
  helpButtonHeader: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f9ff',
    width: 28,
    height: 28,
    borderRadius: 14,
    borderWidth: 1,
    borderColor: '#0996a8',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 3,
  },
  helpButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#0996a8',
  },
  // Help modal styles
  helpModalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  helpModalContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    width: '100%',
    maxWidth: 500,
    maxHeight: '90%',
    minHeight: 400,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  helpModalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
    gap: 12,
  },
  helpModalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1e293b',
    textAlign: 'center',
  },
  helpModalContent: {
    maxHeight: 400,
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  helpSection: {
    marginBottom: 20,
  },
  helpSectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  helpSectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1e293b',
  },
  helpSectionIcon: {
    width: 16,
    height: 16,
  },
  helpSectionBody: {
    gap: 12,
  },
  helpItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f8fafc',
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#0996a8',
  },
  helpItemText: {
    flex: 1,
    fontSize: 14,
    color: '#374151',
    lineHeight: 20,
  },
  helpItemBold: {
    fontWeight: 'bold',
    color: '#1e293b',
  },
  supportContainer: {
    backgroundColor: '#eff6ff',
    borderWidth: 1,
    borderColor: '#0996a8',
    borderRadius: 8,
    padding: 16,
  },
  supportText: {
    fontSize: 14,
    color: '#0996a8',
    marginBottom: 8,
  },
  supportEmail: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#0996a8',
  },
  helpModalFooter: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#e5e7eb',
  },
  helpModalCloseButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#0996a8',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 8,
    gap: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  helpModalCloseText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
}); 