import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  Dimensions,
  Platform,
  TouchableOpacity,
} from 'react-native';
import MapView, { 
  <PERSON>er, 
  Polyline, 
  PROVIDER_GOOGLE,
  Region,
  LatLng 
} from 'react-native-maps';
import { FontAwesome6 } from '@expo/vector-icons';

const { width, height } = Dimensions.get('window');

interface TrajectoryCoordinate {
  lat: number;
  lng: number;
  timestamp?: string;
}

interface Contact {
  id: string;
  lat: number;
  lng: number;
  type: string;
  circumstances: string;
  location: string;
  timestamp?: string;
}

interface TrajectoryMapViewProps {
  coordinates: TrajectoryCoordinate[];
  contacts?: Contact[];
  onMapReady?: () => void;
  onFullscreenToggle?: (isFullscreen: boolean) => void;
}

// Move DoveIcon outside component and memoize to prevent flickering
const DoveIcon = React.memo(({ size = 20, color = "#FFFFFF", style }: { size?: number; color?: string; style?: any }) => {
  return <FontAwesome6 name="dove" size={size} color={color} style={style} />;
});

// Contact marker style (moved outside to prevent recreation)
const contactMarkerStyle = {
  width: 28,
  height: 28,
  borderRadius: 14,
  backgroundColor: '#10B981', // Green color similar to webadmin
  justifyContent: 'center' as const,
  alignItems: 'center' as const,
  borderWidth: 2,
  borderColor: 'white',
  shadowColor: '#000',
  shadowOffset: {
    width: 0,
    height: 2,
  },
  shadowOpacity: 0.25,
  shadowRadius: 3.84,
  elevation: 5,
};

// Format contact description in Portuguese (moved outside component to prevent flickering)
const formatContactDescription = (circumstances: string, location: string) => {
  let description = '';
  
  if (circumstances) {
    // Translate common English terms to Portuguese
    const circumstancesTranslated = circumstances
      .replace(/adultDisplay/gi, 'Exibição de Adulto')
      .replace(/juvenileDisplay/gi, 'Exibição de Juvenil')
      .replace(/adultSinging/gi, 'Canto de Adulto')
      .replace(/juvenileSinging/gi, 'Canto de Juvenil')
      .replace(/adultFlying/gi, 'Voo de Adulto')
      .replace(/juvenileFlying/gi, 'Voo de Juvenil')
      .replace(/feeding/gi, 'Alimentação')
      .replace(/nesting/gi, 'Nidificação')
      .replace(/display/gi, 'Exibição')
      .replace(/singing/gi, 'Canto')
      .replace(/flying/gi, 'Voo');
    
    description += circumstancesTranslated;
  }
  
  if (location) {
    // Translate common location terms to Portuguese
    const locationTranslated = location
      .replace(/waterPoint/gi, 'Ponto de Água')
      .replace(/feedingArea/gi, 'Área de Alimentação')
      .replace(/nestingSite/gi, 'Local de Nidificação')
      .replace(/restingArea/gi, 'Área de Repouso')
      .replace(/openField/gi, 'Campo Aberto')
      .replace(/forest/gi, 'Floresta')
      .replace(/scrubland/gi, 'Mato')
      .replace(/agricultural/gi, 'Agrícola');
    
    if (description) {
      description += ' - ' + locationTranslated;
    } else {
      description = locationTranslated;
    }
  }
  
  return description || 'Contacto registado';
};

// Helper functions moved outside component to prevent recreation
const getMarkerIcon = (index: number, coordinatesLength: number) => {
  const isStart = index === 0;
  const isEnd = index === coordinatesLength - 1 && coordinatesLength > 1;

  if (isStart) {
    return {
      color: '#22c55e', // Green for start
      size: 16,
      name: 'circle' as const,
    };
  } else if (isEnd) {
    return {
      color: '#dc2626', // Red for end
      size: 16,
      name: 'circle' as const,
    };
  } else {
    return {
      color: '#0996a8', // Blue for regular points
      size: 16,
      name: 'circle' as const,
    };
  }
};

const getMarkerTitle = (index: number, coordinatesLength: number) => {
  const isStart = index === 0;
  const isEnd = index === coordinatesLength - 1 && coordinatesLength > 1;

  if (isStart) {
    return 'Início';
  } else if (isEnd) {
    return 'Fim';
  } else {
    return `Ponto ${index + 1}`;
  }
};

const TrajectoryMapView: React.FC<TrajectoryMapViewProps> = ({
  coordinates,
  contacts = [],
  onMapReady,
  onFullscreenToggle
}) => {
  console.log('🗺️ TrajectoryMapView rendered with:', {
    coordinatesCount: coordinates?.length || 0,
    contactsCount: contacts?.length || 0,
    coordinates: coordinates
  });

  const mapRef = useRef<MapView>(null);
  const [mapReady, setMapReady] = useState(false);
  const [region, setRegion] = useState<Region | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Memoize contact markers with deep comparison to prevent flickering
  const contactMarkers = useMemo(() => {
    if (!contacts || contacts.length === 0) {
      return [];
    }
    
    return contacts.map((contact) => {
      // Create stable coordinate object
      const coordinate = {
        latitude: Number(contact.lat),
        longitude: Number(contact.lng),
      };
      
      // Pre-calculate description to avoid recalculation
      const description = formatContactDescription(contact.circumstances || '', contact.location || '');
      
      return (
        <Marker
          key={`contact-${contact.id}`}
          coordinate={coordinate}
          title="Contacto com Rola-brava"
          description={description}
          zIndex={500}
          tracksViewChanges={false} // This is key - prevents unnecessary re-renders
        >
          <View style={contactMarkerStyle}>
            <DoveIcon size={16} color="#FFFFFF" />
          </View>
        </Marker>
      );
    });
  }, [contacts?.length, contacts?.map(c => `${c.id}-${c.lat}-${c.lng}-${c.circumstances}-${c.location}`).join('|')]);

  // Memoize route point markers to prevent flickering
  const routePointMarkers = useMemo(() => {
    if (!coordinates || coordinates.length === 0) {
      return [];
    }
    
    return coordinates.map((coord, index) => {
      const icon = getMarkerIcon(index, coordinates.length);
      const coordinate = {
        latitude: Number(coord.lat),
        longitude: Number(coord.lng),
      };
      
      return (
        <Marker
          key={`route-${index}`}
          coordinate={coordinate}
          title={getMarkerTitle(index, coordinates.length)}
          description={`Ponto ${index + 1} do trajeto`}
          zIndex={100}
          anchor={{ x: 0.5, y: 0.5 }}
          tracksViewChanges={false}
        >
          <View style={[
            styles.markerContainer,
            {
              backgroundColor: icon.color,
              width: icon.size,
              height: icon.size,
            }
          ]}>
            <Text style={styles.pointNumberText}>{index + 1}</Text>
          </View>
        </Marker>
      );
    });
  }, [coordinates?.length, coordinates?.map(c => `${c.lat}-${c.lng}`).join('|')]);

  // Memoize special markers (INÍCIO/FIM) to prevent flickering
  const specialMarkers = useMemo(() => {
    if (!coordinates || coordinates.length === 0) {
      return [];
    }
    
    return coordinates.map((coord, index) => {
      const isStart = index === 0;
      const isEnd = index === coordinates.length - 1;
      
      if (!isStart && !isEnd) {
        return null;
      }
      
      const coordinate = {
        latitude: Number(coord.lat),
        longitude: Number(coord.lng),
      };
      
      return (
        <Marker
          key={`special-${index}`}
          coordinate={coordinate}
          title={isStart ? 'Início do Trajeto' : 'Fim do Trajeto'}
          zIndex={1000}
          anchor={{ x: 0.42, y: 1.5 }}
          tracksViewChanges={false}
        >
          <View style={[
            styles.specialMarkerContainer,
            { backgroundColor: isStart ? '#22c55e' : '#dc2626' }
          ]}>
            <Text style={styles.specialMarkerText}>
              {isStart ? 'INÍCIO' : 'FIM'}
            </Text>
          </View>
        </Marker>
      );
    }).filter(Boolean);
  }, [coordinates?.length, coordinates?.map(c => `${c.lat}-${c.lng}`).join('|')]);

  useEffect(() => {
    console.log('🔄 TrajectoryMapView useEffect - coordinates:', coordinates);
    if (coordinates && coordinates.length > 0) {
      console.log('📍 Calculating initial region for coordinates:', coordinates);
      calculateInitialRegion();
    } else {
      console.log('⚠️ No coordinates provided or empty array');
    }
  }, [coordinates]);

  const calculateInitialRegion = () => {
    console.log('🧮 calculateInitialRegion called');
    if (!coordinates || coordinates.length === 0) {
      console.log('❌ No coordinates to calculate region');
      return;
    }

    console.log('📊 Calculating bounds for coordinates:', coordinates);

    // Calculate bounds from coordinates
    let minLat = coordinates[0].lat;
    let maxLat = coordinates[0].lat;
    let minLng = coordinates[0].lng;
    let maxLng = coordinates[0].lng;

    coordinates.forEach(coord => {
      minLat = Math.min(minLat, coord.lat);
      maxLat = Math.max(maxLat, coord.lat);
      minLng = Math.min(minLng, coord.lng);
      maxLng = Math.max(maxLng, coord.lng);
    });

    // Add padding
    const latDelta = Math.max((maxLat - minLat) * 1.2, 0.01);
    const lngDelta = Math.max((maxLng - minLng) * 1.2, 0.01);

    const centerLat = (minLat + maxLat) / 2;
    const centerLng = (minLng + maxLng) / 2;

    const calculatedRegion = {
      latitude: centerLat,
      longitude: centerLng,
      latitudeDelta: latDelta,
      longitudeDelta: lngDelta,
    };

    console.log('🎯 Calculated region:', calculatedRegion);
    setRegion(calculatedRegion);
  };

  const handleMapReady = () => {
    setMapReady(true);
    if (onMapReady) {
      onMapReady();
    }
    
    // Fit to coordinates after map is ready
    if (coordinates && coordinates.length > 0) {
      setTimeout(() => {
        fitToCoordinates();
      }, 500);
    }
  };

  const fitToCoordinates = () => {
    if (!mapRef.current || !coordinates || coordinates.length === 0) return;

    const coords = coordinates.map(coord => ({
      latitude: coord.lat,
      longitude: coord.lng,
    }));

    mapRef.current.fitToCoordinates(coords, {
      edgePadding: {
        top: 50,
        right: 50,
        bottom: 50,
        left: 50,
      },
      animated: true,
    });
  };



  // Convert coordinates to LatLng format for polyline
  const polylineCoordinates: LatLng[] = coordinates.map(coord => ({
    latitude: coord.lat,
    longitude: coord.lng,
  }));

  if (!region) {
    console.log('⏳ No region calculated yet, showing loading...');
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0996a8" />
        <Text style={styles.loadingText}>A carregar mapa...</Text>
      </View>
    );
  }

  console.log('🗺️ Rendering map with region:', region);
  console.log('📍 Polyline coordinates count:', polylineCoordinates.length);

  return (
    <View style={styles.container}>
      <MapView
        ref={mapRef}
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        initialRegion={region}
        onMapReady={handleMapReady}
        showsUserLocation={false}
        showsMyLocationButton={false}
        showsCompass={true}
        rotateEnabled={true}
        scrollEnabled={true}
        zoomEnabled={true}
        mapType="standard"
        customMapStyle={[
          {
            featureType: "poi",
            elementType: "labels",
            stylers: [{ visibility: "off" }]
          },
          {
            featureType: "poi.business",
            stylers: [{ visibility: "off" }]
          }
        ]}
      >
        {/* Trajectory Polyline - Shadow */}
        {polylineCoordinates.length > 1 && (
          <Polyline
            coordinates={polylineCoordinates}
            strokeColor="rgba(0, 0, 0, 0.4)"
            strokeWidth={6}
            geodesic={true}
          />
        )}

        {/* Trajectory Polyline - Main */}
        {polylineCoordinates.length > 1 && (
          <Polyline
            coordinates={polylineCoordinates}
            strokeColor="#0996a8"
            strokeWidth={4}
            geodesic={true}
          />
        )}

        {/* Route Point Markers - All numbered points */}
        {routePointMarkers}

        {/* Special markers for start and end points */}
        {specialMarkers}

        {/* Contact Markers */}
        {contactMarkers}
      </MapView>

      {/* Fullscreen Button */}
      <TouchableOpacity 
        style={styles.fullscreenButton}
        onPress={() => {
          const newFullscreenState = !isFullscreen;
          setIsFullscreen(newFullscreenState);
          if (onFullscreenToggle) {
            onFullscreenToggle(newFullscreenState);
          }
        }}
        activeOpacity={0.7}
      >
        <FontAwesome6 
          name={isFullscreen ? "compress" : "expand"} 
          size={16} 
          color="#374151" 
        />
      </TouchableOpacity>

      {/* Loading overlay */}
      {!mapReady && (
        <View style={styles.mapLoadingOverlay}>
          <ActivityIndicator size="small" color="#0996a8" />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    borderRadius: 8,
    overflow: 'hidden',
    position: 'relative',
  },
  map: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8fafc',
    borderRadius: 8,
  },
  loadingText: {
    marginTop: 8,
    fontSize: 14,
    color: '#6b7280',
  },
  markerContainer: {
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'white',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
    position: 'relative',
  },

  pointNumberText: {
    fontSize: 10,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    textAlignVertical: 'center',
    includeFontPadding: false,
    lineHeight: 13,
  },
  specialMarkerContainer: {
    borderRadius: 8,
    paddingHorizontal: 3,
    paddingVertical: 2,
    minWidth: 25,
    height: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'white',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.25,
    shadowRadius: 2,
    elevation: 3,
  },
  specialMarkerText: {
    fontSize: 7,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    textAlignVertical: 'center',
    includeFontPadding: false,
    lineHeight: 7,
  },

  mapLoadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(248, 250, 252, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullscreenButton: {
    position: 'absolute',
    bottom: 10,
    right: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 6,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 3,
  },
});

export default TrajectoryMapView; 