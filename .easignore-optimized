# Optimized .easignore for iOS-only builds

# Build outputs
build/
dist/
.expo/

# Development files
.vscode/
.idea/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# OS files
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp

# Android-specific files (not needed for iOS)
google-services.json
*.jks
android/

# Large files not needed for build
*.apk
*.aab
*.ipa
*.dSYM.zip

# Documentation (keep only README.md)
CODER_INSTRUCTIONS.md
FIREBASE_IOS_IMPLEMENTATION_REPORT.md
GOOGLE_MAPS_FIXES.md
GOOGLE_PLAY_DATA_SAFETY_DECLARATION.md
GOOGLE_PLAY_FORM_RESPONSES.md
GOOGLE_PLAY_SUBMISSION_DOCUMENTATION.md
GPS_AND_UI_IMPROVEMENTS_2025-07-30.md
GPS_TRAJECTORY_AND_IMAGE_FIXES.md
IMAGE_UPLOAD_PROGRESS_IMPLEMENTATION.md
IOS_BUILD_TEST_RESULTS.md
IOS_TESTING_INSTRUCTIONS.md
KEYSTORE_INFO.md
NETWORK_CONNECTIVITY_GUIDE.md
TEST_CREDENTIALS_SETUP.md
TRAJETO_EDIT_IMPLEMENTATION.md
VIDEO_DEMO_SCRIPT.md
WEATHER_SETUP.md
iOS_BUILD_TEST_REPORT.md

# Cache directories
node_modules/.cache/
.cache/

# Test files
__tests__/
*.test.js
*.test.ts
*.test.tsx

# Development scripts
scripts/cleanup-dev-artifacts.js
scripts/cleanup-nodemodules.js
scripts/reset-project.js

# Web and server components (not needed for mobile)
webadmin/
gestores/
tecnicos/
stats/
privacy/
app-updates/
email/

# Development tools
keytool/
test-date-time-pickers.html
