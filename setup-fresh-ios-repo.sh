#!/bin/bash

# Setup script for fresh ProROLA iOS repository
# This script populates a fresh repository with optimized iOS-only structure

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -f "app.json" ]; then
    print_error "This script must be run from the RolaApp source directory"
    print_error "Current directory: $(pwd)"
    exit 1
fi

print_header "ProROLA iOS Fresh Repository Setup"

TARGET_DIR="/Users/<USER>/Desktop/ProROLA-iOS"

# Validate target directory
if [ ! -d "$TARGET_DIR" ]; then
    print_error "Target directory does not exist: $TARGET_DIR"
    exit 1
fi

if [ ! -d "$TARGET_DIR/.git" ]; then
    print_error "Target directory is not a git repository: $TARGET_DIR"
    exit 1
fi

SOURCE_DIR="$(pwd)"
print_status "Source directory: $SOURCE_DIR"
print_status "Target directory: $TARGET_DIR"

print_header "Step 1: Copying Essential Files"

cd "$SOURCE_DIR"

# Copy root configuration files
ESSENTIAL_FILES=(
    "App.tsx"
    "app.json"
    "eas.json"
    "metro.config.js"
    "tsconfig.json"
    "expo-env.d.ts"
    "firebase.json"
    "GoogleService-Info.plist"
    "i18n.ts"
)

print_status "Copying root configuration files..."
for file in "${ESSENTIAL_FILES[@]}"; do
    if [ -f "$file" ]; then
        cp "$file" "$TARGET_DIR/"
        print_success "✓ $file"
    else
        print_warning "✗ $file (not found)"
    fi
done

# Copy source directories
ESSENTIAL_DIRS=(
    "app"
    "components"
    "constants"
    "contexts"
    "hooks"
    "services"
    "types"
    "utils"
    "config"
    "assets"
)

print_status "Copying source directories..."
for dir in "${ESSENTIAL_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        cp -r "$dir" "$TARGET_DIR/"
        print_success "✓ $dir/"
    else
        print_warning "✗ $dir/ (not found)"
    fi
done

print_header "Step 2: Creating Optimized Configuration Files"

# Copy optimized configuration files
cp "ios-optimized-configs/package.json" "$TARGET_DIR/"
cp "ios-optimized-configs/.gitignore" "$TARGET_DIR/"
cp "ios-optimized-configs/.easignore" "$TARGET_DIR/"
cp "ios-optimized-configs/README.md" "$TARGET_DIR/"

# Copy ci_scripts directory
cp -r "ios-optimized-configs/ci_scripts" "$TARGET_DIR/"

# Make scripts executable
chmod +x "$TARGET_DIR/ci_scripts/"*.sh

# Copy validation script to target
cp "validate-ios-repo.sh" "$TARGET_DIR/"
chmod +x "$TARGET_DIR/validate-ios-repo.sh"

print_success "Configuration files created"

print_header "Step 3: Git Operations"

cd "$TARGET_DIR"

# Add all files to git
git add -A

# Commit changes
git commit -m "Initial iOS-optimized repository setup

- Added essential React Native/Expo files for iOS builds
- Configured Xcode Cloud integration scripts
- Optimized for iOS-only builds (excluded web admin, Android files)
- Repository size optimized by ~80-90%
- Ready for Xcode Cloud integration"

print_success "Changes committed to git"

print_header "Step 4: Repository Information"

# Display repository status
print_status "Repository status:"
git status --short

# Display file count and size
FILE_COUNT=$(find . -type f ! -path './.git/*' | wc -l | tr -d ' ')
if command -v du >/dev/null 2>&1; then
    REPO_SIZE=$(du -sh . 2>/dev/null | cut -f1)
    print_status "Repository contains $FILE_COUNT files ($REPO_SIZE)"
else
    print_status "Repository contains $FILE_COUNT files"
fi

print_header "Setup Complete!"

print_success "✅ Fresh iOS repository setup completed successfully!"

echo
print_status "Next steps:"
echo "1. Review the repository structure: ls -la"
echo "2. Validate setup: ./validate-ios-repo.sh"
echo "3. Push to GitHub: git push -u origin main --force"
echo "4. Set up Xcode Cloud integration"
echo
print_status "Repository location: $TARGET_DIR"
