{"expo": {"name": "ProROLA", "slug": "rola", "version": "1.0.10", "orientation": "default", "icon": "./assets/images/icon.png", "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "native", "backgroundColor": "#ffffff"}, "scheme": "prorola", "userInterfaceStyle": "automatic", "ios": {"supportsTablet": true, "bundleIdentifier": "com.prorola.app", "buildNumber": "5", "googleServicesFile": "./GoogleService-Info.plist", "config": {"googleMapsApiKey": "AIzaSyDwV-im8XW3FH0iWgxC0bXCe-Epi5JIvz4"}, "infoPlist": {"NSLocationWhenInUseUsageDescription": "Esta aplicação precisa de acesso à sua localização para mostrar a sua posição no mapa.", "NSLocationAlwaysUsageDescription": "Esta aplicação precisa de acesso à sua localização para mostrar a sua posição no mapa.", "NSCameraUsageDescription": "Esta aplicação precisa de acesso à câmera para tirar fotos.", "NSPhotoLibraryUsageDescription": "Esta aplicação precisa de acesso à galeria para selecionar fotos.", "ITSAppUsesNonExemptEncryption": false}}, "android": {"package": "com.prorola.app", "versionCode": 11, "googleServicesFile": "./google-services.json", "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "native", "backgroundColor": "#ffffff"}, "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "permissions": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.RECORD_AUDIO"], "config": {"googleMaps": {"apiKey": "AIzaSyDwV-im8XW3FH0iWgxC0bXCe-Epi5JIvz4"}}}, "web": {"bundler": "metro", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Esta aplicação precisa de acesso à sua localização para mostrar a sua posição no mapa."}], ["expo-image-picker", {"photosPermission": "Esta aplicação precisa de acesso à galeria para selecionar fotos.", "cameraPermission": "Esta aplicação precisa de acesso à câmera para tirar fotos."}], ["react-native-edge-to-edge", {"android": {"parentTheme": "EdgeToEdge", "enforceNavigationBarContrast": false}}], ["@react-native-firebase/app", {"ios": {"useFrameworks": "dynamic", "useModularHeaders": true, "modularHeadersExclude": [], "podfileProperties": {"GTMSessionFetcher": {"modular_headers": true}, "FirebaseAuth": {"modular_headers": true}, "FirebaseFirestore": {"modular_headers": true}, "FirebaseStorage": {"modular_headers": true}, "FirebaseCoreInternal": {"modular_headers": true}}}}]], "experiments": {"typedRoutes": true}, "newArchEnabled": false, "extra": {"router": {"origin": false}, "eas": {"projectId": "3116af1e-7504-4b62-936d-786d1696b0aa"}}, "owner": "dtales"}}