#!/bin/bash

# Migration script for creating iOS-optimized repository
# Usage: ./migrate-to-ios-repo.sh /path/to/source/RolaApp /path/to/target/ProROLA-iOS

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check arguments
if [ $# -ne 2 ]; then
    print_error "Usage: $0 <source_directory> <target_directory>"
    print_error "Example: $0 /Users/<USER>/Desktop/RolaApp /Users/<USER>/Desktop/ProROLA-iOS"
    exit 1
fi

SOURCE_DIR="$1"
TARGET_DIR="$2"

# Validate source directory
if [ ! -d "$SOURCE_DIR" ]; then
    print_error "Source directory does not exist: $SOURCE_DIR"
    exit 1
fi

# Validate target directory
if [ ! -d "$TARGET_DIR" ]; then
    print_error "Target directory does not exist: $TARGET_DIR"
    print_error "Please create the target directory first or clone the ProROLA-iOS repository"
    exit 1
fi

# Check if target is a git repository
if [ ! -d "$TARGET_DIR/.git" ]; then
    print_warning "Target directory is not a git repository"
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

print_status "Starting iOS repository migration..."
print_status "Source: $SOURCE_DIR"
print_status "Target: $TARGET_DIR"

# Create backup of target if it has content
if [ "$(ls -A $TARGET_DIR 2>/dev/null | grep -v '^\.git$' | wc -l)" -gt 0 ]; then
    print_warning "Target directory contains files. Creating backup..."
    BACKUP_DIR="${TARGET_DIR}_backup_$(date +%Y%m%d_%H%M%S)"
    cp -r "$TARGET_DIR" "$BACKUP_DIR"
    print_success "Backup created at: $BACKUP_DIR"
fi

# Clean target directory (except .git)
print_status "Cleaning target directory..."
cd "$TARGET_DIR"
find . -mindepth 1 -maxdepth 1 ! -name '.git' -exec rm -rf {} +

# Copy essential files and directories
print_status "Copying essential files..."

# Root configuration files
ESSENTIAL_FILES=(
    "App.tsx"
    "app.json"
    "eas.json"
    "metro.config.js"
    "tsconfig.json"
    "expo-env.d.ts"
    "firebase.json"
    "GoogleService-Info.plist"
    "i18n.ts"
)

for file in "${ESSENTIAL_FILES[@]}"; do
    if [ -f "$SOURCE_DIR/$file" ]; then
        cp "$SOURCE_DIR/$file" "$TARGET_DIR/"
        print_success "Copied: $file"
    else
        print_warning "File not found: $file"
    fi
done

# Source code directories
ESSENTIAL_DIRS=(
    "app"
    "components"
    "constants"
    "contexts"
    "hooks"
    "services"
    "types"
    "utils"
    "config"
    "assets"
)

for dir in "${ESSENTIAL_DIRS[@]}"; do
    if [ -d "$SOURCE_DIR/$dir" ]; then
        cp -r "$SOURCE_DIR/$dir" "$TARGET_DIR/"
        print_success "Copied directory: $dir"
    else
        print_warning "Directory not found: $dir"
    fi
done

print_status "Migration completed successfully!"
print_status "Next steps:"
echo "1. Navigate to $TARGET_DIR"
echo "2. Run the setup script to create optimized configuration files"
echo "3. Install dependencies: npm install"
echo "4. Test the build: npx expo prebuild --platform ios"
