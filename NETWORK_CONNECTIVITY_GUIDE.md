# Network Connectivity Detection System

This guide explains how to use the network connectivity detection system implemented in the ProROLA bird reporting app.

## Overview

The app includes a comprehensive network connectivity detection system that:
- Monitors internet connectivity in real-time
- Provides visual indicators of network status
- Automatically warns users when attempting network operations without internet
- Allows certain operations to work offline (like editing profile locally)

## Components

### 1. Network Hook (`hooks/useNetworkConnectivity.ts`)

The core hook that monitors network connectivity using `@react-native-community/netinfo`.

```typescript
import { useNetworkConnectivity } from '@/hooks/useNetworkConnectivity';

const { 
  isConnected, 
  isInternetReachable, 
  hasInternetConnection, 
  getConnectionType 
} = useNetworkConnectivity();
```

### 2. Network Context (`contexts/NetworkContext.tsx`)

Provides network state globally throughout the app.

```typescript
import { useNetwork } from '@/contexts/NetworkContext';

const { 
  hasInternetConnection, 
  checkNetworkAndWarn,
  getConnectionType 
} = useNetwork();
```

### 3. Network Status Indicator (`components/NetworkStatusIndicator.tsx`)

Visual component that shows current network status.

```typescript
import NetworkStatusIndicator from '@/components/NetworkStatusIndicator';

// Basic usage
<NetworkStatusIndicator />

// Without text
<NetworkStatusIndicator showText={false} />

// With custom styling
<NetworkStatusIndicator style={customStyle} />
```

### 4. Network Aware Button (`components/NetworkAwareButton.tsx`)

Button component that automatically checks network connectivity before executing operations.

```typescript
import NetworkAwareButton from '@/components/NetworkAwareButton';

<NetworkAwareButton
  onPress={handleSave}
  title="Guardar"
  operation="guardar as alterações do perfil"
  icon="check"
  variant="primary"
  requiresNetwork={true} // Set to false for offline operations
/>
```

## Implementation Examples

### Adding Network Awareness to a Screen

1. **Import the necessary components:**

```typescript
import { useNetwork } from '@/contexts/NetworkContext';
import NetworkStatusIndicator from '@/components/NetworkStatusIndicator';
import NetworkAwareButton from '@/components/NetworkAwareButton';
```

2. **Add network status indicator:**

```typescript
return (
  <View style={styles.container}>
    {/* Network Status Indicator */}
    <View style={styles.networkStatusContainer}>
      <NetworkStatusIndicator />
    </View>
    
    {/* Your content */}
  </View>
);
```

3. **Replace regular buttons with network-aware buttons:**

```typescript
// Before
<TouchableOpacity onPress={handleSave}>
  <Text>Guardar</Text>
</TouchableOpacity>

// After
<NetworkAwareButton
  onPress={handleSave}
  title="Guardar"
  operation="guardar as alterações"
  icon="check"
  variant="primary"
/>
```

### Manual Network Checking

For custom operations that need network checking:

```typescript
const { checkNetworkAndWarn } = useNetwork();

const handleCustomOperation = () => {
  const canProceed = checkNetworkAndWarn(
    'realizar esta operação',
    () => {
      // This function runs only if network is available
      performNetworkOperation();
    }
  );
  
  if (!canProceed) {
    // Network not available, warning was shown
    return;
  }
};
```

### Checking Network Status

```typescript
const { hasInternetConnection, getConnectionType } = useNetwork();

if (hasInternetConnection()) {
  console.log('Connected via:', getConnectionType());
  // Proceed with network operation
} else {
  console.log('No internet connection');
  // Handle offline state
}
```

## Button Variants

The `NetworkAwareButton` supports different visual styles:

- `primary`: Blue background (default)
- `secondary`: Transparent with blue border
- `danger`: Red background for destructive actions

## Network-Dependent vs Offline Operations

### Network-Dependent Operations (requiresNetwork: true)
- Saving profile changes to server
- Changing password
- Uploading photos
- Fetching weather data
- Logging out
- Deleting account

### Offline Operations (requiresNetwork: false)
- Entering edit mode
- Canceling edits
- Local form validation
- Navigation between screens

## Error Messages

The system shows Portuguese error messages:

- **Title**: "Sem ligação à Internet"
- **Message**: "Não é possível [operation] sem ligação à Internet. Por favor, verifique a sua ligação e tente novamente."

## Styling

### Network Status Indicator Styles

```typescript
const styles = StyleSheet.create({
  networkStatusContainer: {
    marginBottom: 10,
    // or for absolute positioning:
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    padding: 10,
  },
});
```

## Integration with Existing Code

The system is designed to be minimally invasive. To add network awareness to existing screens:

1. Add the NetworkProvider to your app layout (already done in `app/_layout.tsx`)
2. Import and add NetworkStatusIndicator to screens
3. Replace network-dependent TouchableOpacity buttons with NetworkAwareButton
4. Use the `requiresNetwork` prop to specify which operations need internet

## Best Practices

1. **Always show network status**: Add NetworkStatusIndicator to screens with network operations
2. **Be specific with operation descriptions**: Use clear Portuguese descriptions for what the user is trying to do
3. **Distinguish offline vs online operations**: Set `requiresNetwork={false}` for operations that work offline
4. **Handle loading states**: The NetworkAwareButton supports loading indicators
5. **Test offline scenarios**: Ensure your app gracefully handles network unavailability

## Connection Types

The system recognizes these connection types:
- Wi-Fi
- Dados móveis (Cellular)
- Ethernet
- Bluetooth
- Desconhecido (Unknown)
- Sem ligação (No connection)

## Future Enhancements

Potential improvements to consider:
- Offline data caching
- Queue network operations for when connectivity returns
- Different warning messages based on operation criticality
- Network quality indicators (slow vs fast connection)
- Retry mechanisms with exponential backoff 