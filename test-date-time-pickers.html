<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Date Time Pickers</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" rel="stylesheet">
    <!-- Date Time Pickers CSS -->
    <link href="tecnicos/assets/css/date-time-pickers.css" rel="stylesheet">
    
    <style>
        body {
            padding: 2rem;
            background: #f8fafc;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 16px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2 class="mb-4">
            <i class="fas fa-test-tube me-2"></i>
            Test Date Time Pickers
        </h2>
        
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="trajectoryDate" class="form-label">
                        <i class="fas fa-calendar me-1"></i>
                        Data
                    </label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="trajectoryDate" placeholder="Selecione a data" readonly required>
                        <button class="btn btn-outline-secondary" type="button" id="datePickerBtn">
                            <i class="fas fa-calendar"></i>
                        </button>
                    </div>
                    <div class="form-text text-muted" style="font-size: 0.75rem;">
                        <i class="fas fa-info-circle me-1"></i>
                        Clique em qualquer parte do campo para selecionar a data
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label for="trajectoryStartTime" class="form-label">
                        <i class="fas fa-clock me-1"></i>
                        Hora de Início
                    </label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="trajectoryStartTime" placeholder="Selecione a hora" readonly required>
                        <button class="btn btn-outline-secondary" type="button" id="timePickerBtn">
                            <i class="fas fa-clock"></i>
                        </button>
                    </div>
                    <div class="form-text text-muted" style="font-size: 0.75rem;">
                        <i class="fas fa-info-circle me-1"></i>
                        Clique em qualquer parte do campo para selecionar a hora
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-4">
            <button type="button" class="btn btn-primary" onclick="testMultipleOpens()">
                <i class="fas fa-redo me-2"></i>
                Test Multiple Opens
            </button>
            <button type="button" class="btn btn-info ms-2" onclick="showCurrentValues()">
                <i class="fas fa-eye me-2"></i>
                Show Values
            </button>
        </div>
        
        <div id="testResults" class="mt-4"></div>
    </div>

    <!-- Include the modals from the main page -->
    <!-- Custom Time Picker Modal -->
    <div class="modal fade" id="timePickerModal" tabindex="-1" aria-labelledby="timePickerModalLabel" aria-hidden="true" data-bs-backdrop="static" style="z-index: 1080;">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="timePickerModalLabel">
                        <i class="fas fa-clock me-2"></i>Selecionar Hora
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-6">
                            <label class="form-label text-center d-block">Horas</label>
                            <select class="form-select" id="hourSelect" size="8">
                                <option value="00">00</option>
                                <option value="01">01</option>
                                <option value="02">02</option>
                                <option value="03">03</option>
                                <option value="04">04</option>
                                <option value="05">05</option>
                                <option value="06">06</option>
                                <option value="07">07</option>
                                <option value="08">08</option>
                                <option value="09">09</option>
                                <option value="10">10</option>
                                <option value="11">11</option>
                                <option value="12">12</option>
                                <option value="13">13</option>
                                <option value="14">14</option>
                                <option value="15">15</option>
                                <option value="16">16</option>
                                <option value="17">17</option>
                                <option value="18">18</option>
                                <option value="19">19</option>
                                <option value="20">20</option>
                                <option value="21">21</option>
                                <option value="22">22</option>
                                <option value="23">23</option>
                            </select>
                        </div>
                        <div class="col-6">
                            <label class="form-label text-center d-block">Minutos</label>
                            <select class="form-select" id="minuteSelect" size="8">
                                <option value="00">00</option>
                                <option value="05">05</option>
                                <option value="10">10</option>
                                <option value="15">15</option>
                                <option value="20">20</option>
                                <option value="25">25</option>
                                <option value="30">30</option>
                                <option value="35">35</option>
                                <option value="40">40</option>
                                <option value="45">45</option>
                                <option value="50">50</option>
                                <option value="55">55</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-3 text-center">
                        <div class="alert alert-info">
                            <i class="fas fa-clock me-2"></i>
                            <span id="selectedTimeDisplay">--:--</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </button>
                    <button type="button" class="btn btn-primary" id="confirmTimeBtn">
                        <i class="fas fa-check me-2"></i>Confirmar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom Date Picker Modal -->
    <div class="modal fade" id="datePickerModal" tabindex="-1" aria-labelledby="datePickerModalLabel" aria-hidden="true" data-bs-backdrop="false" style="z-index: 1070;">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="datePickerModalLabel">
                        <i class="fas fa-calendar me-2"></i>Selecionar Data
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-4">
                            <label class="form-label text-center d-block">Dia</label>
                            <select class="form-select" id="daySelect" size="8">
                                <!-- Days will be populated by JavaScript -->
                            </select>
                        </div>
                        <div class="col-4">
                            <label class="form-label text-center d-block">Mês</label>
                            <select class="form-select" id="monthSelect" size="8">
                                <option value="01">Janeiro</option>
                                <option value="02">Fevereiro</option>
                                <option value="03">Março</option>
                                <option value="04">Abril</option>
                                <option value="05">Maio</option>
                                <option value="06">Junho</option>
                                <option value="07">Julho</option>
                                <option value="08">Agosto</option>
                                <option value="09">Setembro</option>
                                <option value="10">Outubro</option>
                                <option value="11">Novembro</option>
                                <option value="12">Dezembro</option>
                            </select>
                        </div>
                        <div class="col-4">
                            <label class="form-label text-center d-block">Ano</label>
                            <select class="form-select" id="yearSelect" size="8">
                                <!-- Years will be populated by JavaScript -->
                            </select>
                        </div>
                    </div>
                    <div class="mt-3 text-center">
                        <div class="alert alert-info">
                            <i class="fas fa-calendar me-2"></i>
                            <span id="selectedDateDisplay">--/--/----</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancelar
                    </button>
                    <button type="button" class="btn btn-primary" id="confirmDateBtn">
                        <i class="fas fa-check me-2"></i>Confirmar
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Date Time Pickers JS -->
    <script src="tecnicos/assets/js/date-time-pickers.js"></script>
    
    <script>
        // Test functions
        function testMultipleOpens() {
            const results = document.getElementById('testResults');
            results.innerHTML = '<div class="alert alert-info">Testing multiple modal opens...</div>';

            let testCount = 0;
            const maxTests = 5;
            let testType = 'time'; // Start with time picker

            function runTest() {
                testCount++;
                const pickerType = testCount % 2 === 1 ? 'time' : 'date';
                const btnId = pickerType === 'time' ? 'timePickerBtn' : 'datePickerBtn';
                const modalId = pickerType === 'time' ? 'timePickerModal' : 'datePickerModal';

                results.innerHTML += `<p>Test ${testCount}: Opening ${pickerType} picker...</p>`;

                // Open picker
                document.getElementById(btnId).click();

                setTimeout(() => {
                    // Close modal using cancel button to test navigation
                    const cancelBtn = document.getElementById(pickerType === 'time' ? 'cancelTimeBtn' : 'cancelDateBtn');
                    if (cancelBtn) {
                        cancelBtn.click();
                    } else {
                        // Fallback to modal instance
                        const modal = bootstrap.Modal.getInstance(document.getElementById(modalId));
                        if (modal) {
                            modal.hide();
                        }
                    }

                    setTimeout(() => {
                        if (testCount < maxTests) {
                            runTest();
                        } else {
                            results.innerHTML += '<div class="alert alert-success mt-2">✅ Multiple open test completed! Both time and date pickers tested.</div>';
                        }
                    }, 800);
                }, 1500);
            }

            runTest();
        }
        
        function showCurrentValues() {
            const date = document.getElementById('trajectoryDate').value;
            const time = document.getElementById('trajectoryStartTime').value;
            const results = document.getElementById('testResults');
            
            results.innerHTML = `
                <div class="alert alert-info">
                    <h6>Current Values:</h6>
                    <p><strong>Date:</strong> ${date || 'Not set'}</p>
                    <p><strong>Time:</strong> ${time || 'Not set'}</p>
                </div>
            `;
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeDateTimePickers();
            console.log('✅ Date time pickers initialized');
        });
    </script>
</body>
</html>
