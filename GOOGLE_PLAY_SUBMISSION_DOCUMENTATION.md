# Google Play Submission Documentation for ProROLA

## App Overview
**ProROLA** is a wildlife monitoring mobile application specifically designed for tracking and monitoring the European turtle dove (Streptopelia turtur) population in Portugal. The app supports scientific research and hunting management in compliance with European Union environmental regulations.

## Form Responses

### 1. Basic Information
- **Developer email address**: [Your email associated with Google Play Console]
- **Developer/Business Name**: ProROLA / ICNF - Instituto da Conservação da Natureza e das Florestas
- **Did somebody register this developer account on your behalf?**: No

### 2. Core Functionality

#### Geographic and Language Functionality
**"Does your app function differently based on user's geolocation or language?"**

**Answer**: Yes

**Explanation**: 
The ProROLA app extensively uses geolocation for its core wildlife monitoring functionality:
- GPS tracking for trajectory recording during field surveys
- Real-time location mapping for wildlife observation points
- Geofenced monitoring areas for hunting zones
- Location-based weather data integration
- Portuguese language interface with location-specific content for Portuguese hunting zones

#### Intellectual Property
**"Have you uploaded all Proof of Permission for intellectual property?"**

**Answer**: No third party intellectual property appears in my app

**Explanation**: The app uses only open-source libraries, official SDKs, and original content developed specifically for the ProROLA project.

### 3. Login Wall Content

**"Please select the statement that applies to you:"**

**Answer**: I have content locked behind a login wall and have already provided Google with valid credentials to bypass this wall

**Test Credentials Provided**:

#### Technician Account (Técnico ProROLA)
- **Email**: <EMAIL>
- **Password**: TesteProrola2024!
- **Role**: Field technician with full monitoring capabilities
- **Access**: Can create field reports, record trajectories, capture geotagged photos, and sync data

#### Hunt Manager Account (Gestor de Zona de Caça)
- **Email**: <EMAIL>  
- **Password**: GestorTeste2024!
- **Role**: Hunting zone manager
- **Access**: Can manage hunting zones, create hunting trajectories, and monitor hunting activities

#### Collaborator Account (Colaborador)
- **Email**: <EMAIL>
- **Password**: ColabTeste2024!
- **Role**: General collaborator
- **Access**: Basic wildlife observation and reporting features

### 4. Video Demo Requirements

**Video Content Must Include**:
1. **Login Process**: Demonstration of authentication for all user types
2. **Core Features**:
   - Wildlife monitoring and trajectory recording
   - GPS-based field reporting
   - Photo capture with geolocation
   - Weather conditions integration
   - Data synchronization
3. **User Roles**: Show different interfaces for technicians vs. hunt managers
4. **Offline Functionality**: Demonstrate offline data collection capabilities
5. **Map Integration**: Show real-time location tracking and mapping features

### 5. SDKs and Third-Party Code

#### SDKs Used and Justification

**Firebase SDK**
- **Purpose**: User authentication, real-time database, and cloud storage
- **Justification**: Essential for secure multi-user access, data synchronization, and offline-first architecture required for field work in remote areas

**Google Maps SDK**
- **Purpose**: Mapping, location services, and GPS tracking
- **Justification**: Critical for wildlife monitoring, trajectory recording, and hunting zone management. Provides accurate geolocation services required for scientific data collection

**Expo SDK**
- **Purpose**: Cross-platform development framework
- **Justification**: Enables consistent experience across iOS and Android while providing native device access (camera, GPS, storage)

**React Native Location Services**
- **Purpose**: Precise GPS tracking and location monitoring
- **Justification**: Essential for accurate wildlife observation positioning and trajectory recording during field surveys

**Camera/Image Picker SDK**
- **Purpose**: Photo capture and gallery access
- **Justification**: Required for documenting wildlife observations with geotagged photos for scientific verification

**AsyncStorage**
- **Purpose**: Local data persistence
- **Justification**: Enables offline data collection in remote areas where internet connectivity is limited

**React Native NetInfo**
- **Purpose**: Network connectivity monitoring
- **Justification**: Manages offline/online state for data synchronization when field workers return to connected areas

#### Third-Party Code Compliance

**How we ensure compliance with Google Play policies**:

1. **Official Sources**: All SDKs are from official, verified publishers (Google, Facebook/Meta, Expo)
2. **Regular Updates**: Libraries are maintained at current versions for security and policy compliance
3. **Minimal Data Collection**: Only collect data necessary for wildlife monitoring functionality
4. **Privacy Compliance**: All data collection follows GDPR requirements and Portuguese privacy laws
5. **Security**: All third-party code is vetted for security vulnerabilities
6. **Transparency**: Clear privacy policy explains all data usage
7. **User Consent**: Explicit permission requested for location, camera, and storage access

## App Features and Functionality

### Core Features
1. **Wildlife Monitoring**: GPS-tracked field surveys for turtle dove population monitoring
2. **Trajectory Recording**: Precise route tracking during monitoring sessions
3. **Photo Documentation**: Geotagged photo capture for scientific verification
4. **Weather Integration**: Automatic weather condition recording
5. **Offline Capability**: Full functionality without internet connection
6. **Data Synchronization**: Automatic sync when connectivity is restored
7. **Multi-User Support**: Different interfaces for technicians, hunt managers, and collaborators
8. **Hunting Zone Management**: Tools for managing hunting areas and compliance
9. **Scientific Reporting**: Structured data collection for research purposes
10. **Real-time Mapping**: Live location tracking and mapping interface

### User Roles and Access Control

#### Técnico ProROLA (Field Technician)
- Primary field researchers
- Full access to monitoring tools
- Can create and manage field reports
- GPS trajectory recording
- Photo documentation capabilities
- Weather data collection
- Requires admin approval for account activation

#### Gestor de Zona de Caça (Hunt Manager)
- Hunting zone administrators
- Manage hunting areas and activities
- Create hunting-specific trajectories
- Monitor compliance with hunting regulations
- Access to zone-specific reporting tools

#### Colaborador (Collaborator)
- General wildlife observers
- Basic reporting capabilities
- Limited administrative access
- Can contribute to data collection efforts

#### Administrador (Administrator)
- System administrators
- **Note**: Admin accounts are restricted to web interface only
- Cannot access mobile app (security measure)

### Data Collection and Privacy

#### Data Collected
- **Location Data**: GPS coordinates for scientific monitoring (with explicit user consent)
- **User Identification**: Name, email, role for access control
- **Wildlife Observations**: Species sightings, behavior notes, environmental conditions
- **Photos**: Geotagged images for scientific verification
- **Weather Data**: Environmental conditions during observations
- **Usage Analytics**: Basic app usage for performance optimization

#### Privacy Compliance
- Full GDPR compliance
- Portuguese privacy law adherence
- Explicit consent for all data collection
- Transparent privacy policy
- User control over data sharing
- Secure data storage and transmission

### Technical Architecture

#### Offline-First Design
- Full functionality without internet connection
- Local data storage using AsyncStorage
- Automatic synchronization when connectivity restored
- Conflict resolution for concurrent edits

#### Security Features
- Firebase Authentication for secure user management
- Role-based access control
- Encrypted data transmission
- Secure token management
- Admin account restrictions

#### Performance Optimization
- Efficient map rendering with viewport-based loading
- Image compression for photo uploads
- Cached data for improved performance
- Progressive data loading

## Contact Information

**Technical Support**: <EMAIL>
**Organization**: ICNF - Instituto da Conservação da Natureza e das Florestas
**Address**: Av. Dr. Alfredo Magalhães Ramalho 1, 1495-165 Algés, Portugal

## Additional Notes

- App is specifically designed for Portuguese wildlife monitoring requirements
- Complies with EU environmental regulations
- Supports scientific research initiatives
- Enables sustainable hunting management
- Provides data for conservation efforts
- Integrates with existing Portuguese wildlife management systems

---

**Document Version**: 1.0  
**Last Updated**: January 2025  
**Prepared for**: Google Play Store Review Process 