#!/bin/sh

# ci_post_clone.sh
# Xcode Cloud build script for React Native Expo app

set -e

echo "🔧 Setting up Node.js environment..."

# Install Node.js (use the version your project requires)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"

# Install and use Node.js 18 (adjust version as needed)
nvm install 18
nvm use 18

echo "📦 Installing dependencies..."

# Install npm dependencies
npm ci

echo "🏗️ Installing Expo CLI..."

# Install Expo CLI globally
npm install -g @expo/cli

echo "📱 Pre-building iOS project..."

# Prebuild the iOS project
npx expo prebuild --platform ios --clean

echo "✅ Setup complete!"
