{"name": "rola", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "analyze-size": "node ./scripts/cleanup-nodemodules.js", "cleanup-artifacts": "node ./scripts/cleanup-dev-artifacts.js", "clean-install": "rm -rf node_modules package-lock.json && npm install", "clean-install-win": "rmdir /s node_modules && del package-lock.json && npm install"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@expo/vector-icons": "^14.0.2", "@firebase/auth-types": "^0.13.0", "@mattermost/react-native-turbo-mailer": "^0.2.4", "@react-native-async-storage/async-storage": "1.23.1", "@react-native-community/geolocation": "^3.4.0", "@react-native-community/netinfo": "^11.4.1", "@react-native-firebase/app": "^21.11.0", "@react-native-firebase/auth": "^21.14.0", "@react-native-firebase/firestore": "^21.11.0", "@react-native-firebase/storage": "^21.11.0", "@react-native-masked-view/masked-view": "^0.3.2", "@react-native/gradle-plugin": "^0.76.7", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@tanstack/react-query": "^5.67.1", "buffer": "^6.0.3", "crypto-js": "^4.2.0", "expo": "~52.0.36", "expo-blur": "~14.0.3", "expo-constants": "~17.0.7", "expo-dev-client": "~5.0.12", "expo-file-system": "~18.0.11", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-image-picker": "~16.0.6", "expo-linking": "~7.0.5", "expo-location": "~18.0.7", "expo-navigation-bar": "~4.0.9", "expo-router": "~4.0.17", "expo-splash-screen": "~0.29.22", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.8", "expo-web-browser": "~14.0.2", "firebase": "^11.4.0", "i18next": "^24.2.2", "react": "18.3.1", "react-dom": "18.3.1", "react-i18next": "^15.4.1", "react-native": "^0.76.7", "react-native-edge-to-edge": "^1.6.2", "react-native-gesture-handler": "~2.20.2", "react-native-keep-awake": "^4.0.0", "react-native-maps": "1.18.0", "react-native-modal": "^13.0.1", "react-native-reanimated": "~3.16.1", "react-native-safe-area-context": "4.12.0", "react-native-screens": "~4.4.0", "react-native-svg": "15.8.0", "react-native-web": "~0.19.13", "react-native-webview": "13.12.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@react-native-community/cli": "^15.1.3", "@react-native/metro-config": "^0.78.0", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.12", "@types/react": "~18.3.12", "@types/react-test-renderer": "^18.3.0", "babel-plugin-module-resolver": "^5.0.2", "jest": "^29.2.1", "jest-expo": "~52.0.4", "metro-config": "^0.81.2", "react-native-dotenv": "^3.4.11", "react-native-svg-transformer": "^1.5.0", "react-test-renderer": "18.3.1", "typescript": "^5.3.3"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"exclude": ["@emailjs/browser", "@firebase/auth-types", "@headlessui/react", "@heroicons/react", "@react-native/gradle-plugin", "@tailwindcss/forms", "buffer", "chart.js", "daisyui", "firebase", "firebase-admin", "leaflet", "nodemailer", "react-chartjs-2", "react-native-leaflet-view"], "listUnknownPackages": false}}}, "private": true}