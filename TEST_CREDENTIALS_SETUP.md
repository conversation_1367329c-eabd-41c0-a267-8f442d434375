# Test Credentials Setup for Google Play Review

## Overview
This document provides instructions for creating and managing test accounts for Google Play Store reviewers to access the ProROLA app's functionality behind the login wall.

## Test Accounts Required

### 1. Technician Account (Técnico ProROLA)
- **Email**: `<EMAIL>`
- **Password**: `TesteProrola2024!`
- **Role**: `tecnico_prorola`
- **Status**: `verified: true` (pre-approved)
- **Access Level**: Full monitoring capabilities

### 2. Hunt Manager Account (Gestor de Zona de Caça)
- **Email**: `<EMAIL>`
- **Password**: `GestorTeste2024!`
- **Role**: `gestor_caca`
- **Status**: `verified: true`
- **Access Level**: Hunting zone management

### 3. Collaborator Account (Colaborador)
- **Email**: `<EMAIL>`
- **Password**: `ColabTeste2024!`
- **Role**: `colaborador`
- **Status**: `verified: true`
- **Access Level**: Basic wildlife observation

## Setup Instructions

### Step 1: Create Firebase Auth Users

For each test account, create users in Firebase Authentication:

```javascript
// Firebase Admin SDK - Create test users
const admin = require('firebase-admin');

const testUsers = [
  {
    email: '<EMAIL>',
    password: 'TesteProrola2024!',
    displayName: 'Técnico Teste',
    emailVerified: true
  },
  {
    email: '<EMAIL>',
    password: 'GestorTeste2024!',
    displayName: 'Gestor Teste',
    emailVerified: true
  },
  {
    email: '<EMAIL>',
    password: 'ColabTeste2024!',
    displayName: 'Colaborador Teste',
    emailVerified: true
  }
];

// Create each user
testUsers.forEach(async (userData) => {
  try {
    const userRecord = await admin.auth().createUser(userData);
    console.log('Successfully created user:', userRecord.uid);
  } catch (error) {
    console.error('Error creating user:', error);
  }
});
```

### Step 2: Create Firestore User Documents

#### Technician User Document
Collection: `users`
Document ID: `{technician_user_uid}`

```json
{
  "email": "<EMAIL>",
  "name": "Técnico Teste ProROLA",
  "role": "tecnico_prorola",
  "verified": true,
  "created_at": "2025-01-01T00:00:00Z",
  "updated_at": "2025-01-01T00:00:00Z",
  "uid": "{technician_user_uid}",
  "test_account": true,
  "approved_by": "admin",
  "approved_at": "2025-01-01T00:00:00Z"
}
```

#### Hunt Manager User Document
Collection: `gestoresZonaCaca`
Document ID: `{hunt_manager_user_uid}`

```json
{
  "email": "<EMAIL>",
  "name": "Gestor Teste Zona de Caça",
  "role": "gestor_caca",
  "verified": true,
  "created_at": "2025-01-01T00:00:00Z",
  "updated_at": "2025-01-01T00:00:00Z",
  "uid": "{hunt_manager_user_uid}",
  "test_account": true,
  "nif": "*********",
  "zones": ["test_zone_1"]
}
```

#### Collaborator User Document
Collection: `users`
Document ID: `{collaborator_user_uid}`

```json
{
  "email": "<EMAIL>",
  "name": "Colaborador Teste",
  "role": "colaborador",
  "verified": true,
  "created_at": "2025-01-01T00:00:00Z",
  "updated_at": "2025-01-01T00:00:00Z",
  "uid": "{collaborator_user_uid}",
  "test_account": true
}
```

### Step 3: Create Test Data

#### Sample Hunting Zone for Hunt Manager
Collection: `zonas`
Document ID: `test_zone_1`

```json
{
  "id": "test_zone_1",
  "name": "Zona de Caça Teste",
  "description": "Zona de teste para demonstração",
  "createdBy": "{hunt_manager_user_uid}",
  "created_at": "2025-01-01T00:00:00Z",
  "coordinates": [
    {"lat": 39.7392, "lng": -8.8073},
    {"lat": 39.7392, "lng": -8.8063},
    {"lat": 39.7382, "lng": -8.8063},
    {"lat": 39.7382, "lng": -8.8073}
  ],
  "test_data": true
}
```

#### Sample Trajectory for Technician
Collection: `trajetos`
Document ID: `test_trajectory_1`

```json
{
  "id": "test_trajectory_1",
  "name": "Trajeto Teste Técnico",
  "createdBy": "{technician_user_uid}",
  "created_at": "2025-01-01T00:00:00Z",
  "coordinates": [
    {"lat": 39.7392, "lng": -8.8073, "timestamp": "2025-01-01T10:00:00Z"},
    {"lat": 39.7395, "lng": -8.8070, "timestamp": "2025-01-01T10:05:00Z"},
    {"lat": 39.7398, "lng": -8.8067, "timestamp": "2025-01-01T10:10:00Z"}
  ],
  "status": "completed",
  "test_data": true
}
```

## Account Access Instructions for Google Reviewers

### For Google Play Review Team

**Account 1: Field Technician**
- Email: `<EMAIL>`
- Password: `TesteProrola2024!`
- This account demonstrates full field monitoring capabilities including GPS tracking, photo capture, and report creation.

**Account 2: Hunt Manager**
- Email: `<EMAIL>`
- Password: `GestorTeste2024!`
- This account demonstrates hunting zone management, trajectory creation for hunting areas, and compliance monitoring.

**Account 3: General Collaborator**
- Email: `<EMAIL>`
- Password: `ColabTeste2024!`
- This account demonstrates basic wildlife observation and reporting capabilities.

### Login Instructions
1. Open the ProROLA app
2. Tap "Login" on the welcome screen
3. Enter the provided email and password
4. Tap "Sign In"
5. The app will automatically redirect to the appropriate dashboard based on user role

### Features to Test

#### With Technician Account:
- ✅ GPS trajectory recording
- ✅ Wildlife observation reports
- ✅ Photo capture with geolocation
- ✅ Weather data integration
- ✅ Offline functionality
- ✅ Data synchronization

#### With Hunt Manager Account:
- ✅ Hunting zone management
- ✅ Hunting trajectory creation
- ✅ Zone-specific reporting
- ✅ Compliance monitoring tools

#### With Collaborator Account:
- ✅ Basic wildlife observations
- ✅ Simple reporting features
- ✅ Photo documentation

## Security Notes

### Test Account Security
- All test accounts are clearly marked with `test_account: true` in the database
- Test accounts cannot access production data
- Test accounts have limited permissions appropriate to their role
- All test data is clearly labeled and separate from production data

### Cleanup After Review
- Test accounts will remain active for 90 days post-review
- Test data will be automatically purged after review completion
- Credentials will be rotated if needed for security

## Troubleshooting

### Common Issues

**Login Fails**
- Ensure email is entered exactly as provided (case-sensitive)
- Check password for typos
- Verify internet connection

**Features Not Visible**
- Confirm correct account type is being used
- Check user role in app profile section
- Restart app if needed

**GPS Not Working**
- Grant location permissions when prompted
- Ensure device location services are enabled
- Test outdoors for best GPS accuracy

### Support Contact
For any issues with test accounts during review:
- Email: `<EMAIL>`
- Subject: "Google Play Review - Test Account Issue"
- Include: Account email, issue description, device information

## Verification Checklist

Before submitting to Google Play, verify:

- [ ] All test accounts created in Firebase Auth
- [ ] All user documents created in Firestore
- [ ] Email verification set to true for all test accounts
- [ ] Appropriate roles assigned to each account
- [ ] Test data created for demonstration
- [ ] All accounts tested and functional
- [ ] Login credentials documented in submission
- [ ] Video demo recorded using test accounts
- [ ] Support contact information provided

---

**Important**: These test credentials are specifically for Google Play Store review purposes. They provide reviewers with full access to demonstrate the app's functionality behind the login wall while maintaining security and data integrity. 