# Google Play Form Responses - Quick Reference

## Basic Information
- **Developer email address**: <EMAIL>
- **Developer/Business Name**: HISTÓRIAS AVULSO, LDA / ProROLA
- **Did somebody register this developer account on your behalf?**: No

**Note**: Even though you have multiple developers working on the account, answer "No" because the account was created by your own organization (HISTÓRIAS AVULSO, LDA), not by an external third party or agency.

## Core Functionality

### Geographic/Language Question
**Does your app function differently based on user's geolocation or language?**

**Answer**: Yes

**Explanation**: 
```
The ProROLA app uses geolocation extensively for wildlife monitoring:
- GPS tracking for field survey trajectories
- Real-time location mapping for wildlife observations
- Geofenced hunting zones management
- Location-based weather data integration
- Portuguese language interface with Portugal-specific content
```

### Intellectual Property
**Have you uploaded all Proof of Permission for intellectual property?**

**Answer**: No third party intellectual property appears in my app

## Login Wall
**Please select the statement that applies to you:**

**Answer**: I have content locked behind a login wall and have already provided Google with valid credentials to bypass this wall

**Test Credentials**:
```
Technician Account:
Email: <EMAIL>
Password: TesteProrola2024!

Hunt Manager Account:
Email: <EMAIL>
Password: GestorTeste2024!

Collaborator Account:
Email: <EMAIL>
Password: ColabTeste2024!
```

## SDKs and Third-Party Code

### What SDKs does your app use and why?
```
Firebase SDK - User authentication, real-time database, cloud storage for secure multi-user access and data synchronization required for field work in remote areas

Google Maps SDK - Mapping, location services, GPS tracking for wildlife monitoring, trajectory recording, and hunting zone management with accurate geolocation services for scientific data collection

Expo SDK - Cross-platform development framework enabling consistent iOS/Android experience with native device access (camera, GPS, storage)

React Native Location Services - Precise GPS tracking for wildlife observation positioning and trajectory recording during field surveys

Camera/Image Picker SDK - Photo capture and gallery access for documenting wildlife observations with geotagged photos for scientific verification

AsyncStorage - Local data persistence enabling offline data collection in remote areas with limited connectivity

React Native NetInfo - Network connectivity monitoring for offline/online state management and data synchronization
```

### How do you ensure third-party code compliance?
```
All third-party SDKs are from official, verified sources (Google, Meta, Expo) and comply with Google Play policies. We ensure compliance through:

1. Using only official SDKs from reputable publishers
2. Regular updates to latest versions for security and policy compliance
3. Minimal data collection limited to wildlife monitoring functionality
4. GDPR and Portuguese privacy law compliance
5. Security vetting of all third-party code
6. Transparent privacy policy explaining all data usage
7. Explicit user consent for location, camera, and storage access
```

## Video Demo Checklist
- [ ] Show all three login types (technician, hunt manager, collaborator)
- [ ] Demonstrate GPS trajectory recording
- [ ] Show photo capture with geolocation
- [ ] Display weather integration
- [ ] Demonstrate offline functionality
- [ ] Show data synchronization
- [ ] Explain different user roles
- [ ] Show privacy settings and permissions
- [ ] Demonstrate admin account restriction
- [ ] Include app purpose and scientific context

## Key Points to Remember
1. **This is a scientific research tool** - not a casual app
2. **Offline capability is essential** - for remote field work
3. **Location access is core functionality** - required for wildlife monitoring
4. **Multiple user roles** - technicians, hunt managers, collaborators
5. **GDPR compliant** - follows Portuguese and EU privacy laws
6. **Admin restrictions** - administrators cannot access mobile app (security)

## Contact Information
- **Technical Support**: <EMAIL>
- **Organization**: ICNF - Instituto da Conservação da Natureza e das Florestas
- **Address**: Av. Dr. Alfredo Magalhães Ramalho 1, 1495-165 Algés, Portugal

---

**Important**: Make sure to create the test accounts before submitting and verify they work properly. The video demo should show all functionality behind the login wall using these test credentials. 