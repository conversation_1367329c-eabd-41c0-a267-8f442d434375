# ProROLA App Video Demo Script for Google Play Submission

## Video Requirements
- **Duration**: 3-5 minutes maximum
- **Quality**: HD (1080p minimum)
- **Audio**: Clear narration in English or Portuguese with English subtitles
- **Content**: Must show ALL functionality behind login wall

## Demo Script Structure

### 1. Introduction (15 seconds)
**Scene**: App icon and splash screen
**Narration**: "This is ProROLA, a wildlife monitoring application designed for tracking European turtle dove populations in Portugal. The app supports scientific research and hunting management in compliance with EU environmental regulations."

### 2. Login Process (30 seconds)

#### Technician Login
**Scene**: Login screen
**Actions**:
- Enter email: `<EMAIL>`
- Enter password: `TesteProrola2024!`
- Tap login button
- Show successful login and main dashboard

**Narration**: "The app requires authentication for all users. Here we're logging in as a field technician who has full monitoring capabilities."

#### Role-based Access
**Scene**: Main dashboard showing technician interface
**Actions**:
- Navigate through main tabs (Reports, Trajectories, Profile, Settings)
- Show technician-specific features

**Narration**: "Different user roles have different access levels. Technicians can create field reports, record GPS trajectories, and capture geotagged photos."

### 3. Core Wildlife Monitoring Features (90 seconds)

#### GPS Trajectory Recording
**Scene**: Map screen with GPS tracking
**Actions**:
- Start a new trajectory recording
- Show real-time GPS tracking on map
- Walk around to demonstrate trajectory recording
- Show trajectory path being drawn on map
- Stop recording and save trajectory

**Narration**: "The core feature is GPS-based trajectory recording for field surveys. The app tracks the user's precise location during wildlife monitoring sessions."

#### Photo Documentation
**Scene**: Camera interface
**Actions**:
- Take a photo using the app camera
- Show geotagging information being added
- Save photo with location data
- View saved photos with GPS coordinates

**Narration**: "All photos are automatically geotagged for scientific verification. This ensures accurate location data for wildlife observations."

#### Weather Integration
**Scene**: Weather conditions screen
**Actions**:
- Show current weather conditions
- Display temperature, humidity, wind speed
- Show how weather data is automatically recorded with observations

**Narration**: "Weather conditions are automatically recorded with each observation, providing important environmental context for scientific analysis."

#### Offline Functionality
**Scene**: Settings screen showing offline mode
**Actions**:
- Enable airplane mode on device
- Show app still functioning
- Create a report while offline
- Show data being stored locally
- Re-enable connectivity
- Show automatic data synchronization

**Narration**: "The app works fully offline, essential for field work in remote areas. Data automatically syncs when connectivity is restored."

### 4. Hunt Manager Role (45 seconds)

#### Logout and Re-login
**Scene**: Profile screen logout, then login screen
**Actions**:
- Logout from technician account
- Login with hunt manager credentials:
  - Email: `<EMAIL>`
  - Password: `GestorTeste2024!`

**Narration**: "Now we'll demonstrate the hunt manager interface, which has different capabilities for managing hunting zones."

#### Hunting Zone Management
**Scene**: Hunt manager dashboard
**Actions**:
- View hunting zones list
- Create a hunting trajectory
- Show hunting-specific reporting tools
- Demonstrate zone-specific features

**Narration**: "Hunt managers can create hunting-specific trajectories and manage compliance with hunting regulations."

### 5. Data Management and Reports (30 seconds)

#### Report Creation
**Scene**: Report creation screen
**Actions**:
- Create a new wildlife observation report
- Fill in species information
- Add photos and location data
- Save report

**Narration**: "Users can create detailed wildlife observation reports with species information, photos, and precise location data."

#### Data Synchronization
**Scene**: Sync status screen
**Actions**:
- Show pending data for sync
- Trigger manual sync
- Show successful data upload
- Display sync status

**Narration**: "All data is securely synchronized to the cloud database for scientific analysis and research purposes."

### 6. Privacy and Security (15 seconds)

#### Privacy Settings
**Scene**: Privacy policy screen
**Actions**:
- Show privacy policy
- Display data collection permissions
- Show user consent options

**Narration**: "The app follows strict privacy guidelines, requesting explicit consent for location access and clearly explaining all data usage."

### 7. Admin Restrictions (15 seconds)

#### Admin Account Restriction
**Scene**: Login screen with admin attempt
**Actions**:
- Attempt login with admin credentials
- Show error message blocking admin access
- Display message about web-only admin access

**Narration**: "For security, administrator accounts are restricted to the web interface only and cannot access the mobile app."

### 8. Conclusion (15 seconds)

**Scene**: App overview with key features highlighted
**Narration**: "ProROLA provides comprehensive wildlife monitoring tools with GPS tracking, photo documentation, weather integration, and offline capabilities, all designed for scientific research and sustainable hunting management in Portugal."

## Technical Recording Notes

### Camera Settings
- Record in landscape mode for better visibility
- Use screen recording software for clean capture
- Ensure good lighting for screen visibility
- Keep device steady during recording

### Audio Requirements
- Clear, professional narration
- Background music optional but keep low
- Avoid background noise
- Consider adding English subtitles if narrating in Portuguese

### Editing Guidelines
- Keep transitions smooth
- Add callout arrows/highlights for important UI elements
- Include app name and version in video
- Add contact information at the end
- Export in high quality (1080p minimum)

### Key Points to Emphasize
1. **Scientific Purpose**: This is a research tool, not a casual app
2. **Offline Capability**: Essential for field work in remote areas
3. **Security**: Role-based access and admin restrictions
4. **Compliance**: GDPR and Portuguese privacy law adherence
5. **Geolocation**: Core functionality requires location access
6. **Multi-user**: Different interfaces for different roles

## Submission Checklist

- [ ] Video shows all login processes
- [ ] All major features demonstrated
- [ ] Offline functionality clearly shown
- [ ] Different user roles explained
- [ ] Privacy and security measures highlighted
- [ ] Video quality is HD (1080p+)
- [ ] Duration is under 5 minutes
- [ ] Audio is clear and professional
- [ ] Contact information included
- [ ] File size is reasonable for upload

---

**Note**: This video will be the primary way Google reviewers understand your app's functionality. Make sure every feature behind the login wall is clearly demonstrated and explained. 