#!/bin/sh

# ci_pre_xcodebuild.sh
# Pre-build script for Xcode Cloud - ProROLA iOS

set -e

echo "🔧 ProROLA iOS - Pre-Xcodebuild setup..."

# Ensure we're in the right directory
cd "$CI_WORKSPACE"

echo "📋 Current directory: $(pwd)"
echo "📋 Directory contents:"
ls -la

# Verify iOS project exists
if [ ! -d "ios" ]; then
    echo "❌ iOS directory not found. Running prebuild..."
    npx expo prebuild --platform ios --clean --no-install
fi

# Navigate to iOS directory
cd ios

echo "📱 iOS project directory contents:"
ls -la

# Update CocoaPods if needed
echo "🔄 Updating CocoaPods repository..."
pod repo update

# Install/update pods
echo "📦 Installing iOS dependencies (CocoaPods)..."
pod install --verbose

# Verify Podfile.lock exists
if [ -f "Podfile.lock" ]; then
    echo "✅ Podfile.lock found"
else
    echo "❌ Podfile.lock not found"
    exit 1
fi

# Return to root directory
cd ..

echo "✅ Pre-Xcodebuild setup completed successfully!"
