# iOS-Optimized .easignore for Xcode Cloud builds

# Build outputs
build/
dist/
.expo/

# Development files
.vscode/
.idea/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# OS files
.DS_Store
Thumbs.db

# Temporary files
*.tmp
*.temp

# Android-specific files (not needed for iOS)
google-services.json
*.jks
*.keystore
android/

# Large files not needed for build
*.apk
*.aab
*.ipa
*.dSYM.zip

# Documentation (keep only README.md)
*.md
!README.md

# Cache directories
node_modules/.cache/
.cache/

# Test files (optional - remove if you want to include tests)
__tests__/
*.test.js
*.test.ts
*.test.tsx
*.spec.js
*.spec.ts
*.spec.tsx

# Development scripts
scripts/

# Web components (not needed for mobile)
web-build/

# IDE settings
.vscode/
.idea/

# Backup files
*.bak
*.backup

# Local environment files
.env*.local
