<?php
require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Check if user is logged in and is admin
if (!is_logged_in()) {
    header('Location: ' . SITE_URL . '/pages/auth/login.php');
    exit();
}

if (!isAdmin()) {
    header('Location: ' . SITE_URL . '/pages/dashboard/');
    exit();
}

$error = '';
$success = '';
$huntingZones = [];

// Check for session success message (from redirect after POST)
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
if (isset($_SESSION['success_message'])) {
    $success = $_SESSION['success_message'];
    unset($_SESSION['success_message']); // Clear it so it doesn't show again
    // Debug: Log that we found a session message
    error_log("Session success message found: " . $success);
}

// Initialize Firebase
$firebase = new Firebase(FIREBASE_PROJECT_ID, FIREBASE_API_KEY);



// Handle conflict resolution
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'resolve_conflicts') {
    session_start();
    $conflictResolutions = json_decode($_POST['conflict_resolutions'], true);
    
    // Get zones from session
    $allZones = $_SESSION['pending_zones'] ?? [];
    
    if (empty($allZones)) {
        $error = 'Dados da sessão expirados. Por favor, carregue o ficheiro novamente.';
    } else {
        try {
            $result = processZonesWithResolutions($allZones, $conflictResolutions, $firebase);
            
            $successMsg = [];
            if ($result['imported'] > 0) {
                $successMsg[] = "{$result['imported']} novas zonas importadas";
            }
            if ($result['updated'] > 0) {
                $successMsg[] = "{$result['updated']} zonas atualizadas";
            }
            if ($result['skipped'] > 0) {
                $successMsg[] = "{$result['skipped']} zonas ignoradas (dados inalterados)";
            }
            
            if (!empty($successMsg)) {
                $success = "Processamento concluído: " . implode(', ', $successMsg) . ".";
            } else {
                $error = "Nenhuma zona foi processada.";
            }
            
            // Store detailed results for JavaScript
            if (session_status() === PHP_SESSION_NONE) {
                session_start();
            }
            $_SESSION['import_details'] = $result;
            error_log("Conflict resolution details stored in session " . session_id() . ": " . print_r($result, true));
            
            // Clear session data
            unset($_SESSION['pending_zones']);
            unset($_SESSION['conflict_analysis']);
            
        } catch (Exception $e) {
            $error = 'Erro durante o processamento: ' . $e->getMessage();
        }
    }
}

// Handle file upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'upload' && isset($_FILES['csv_file'])) {
    error_log("Upload request received");
    $uploadedFile = $_FILES['csv_file'];
    error_log("Uploaded file info: " . print_r($uploadedFile, true));
    
    if ($uploadedFile['error'] === UPLOAD_ERR_OK) {
        $fileExtension = strtolower(pathinfo($uploadedFile['name'], PATHINFO_EXTENSION));
        
        if ($fileExtension === 'csv') {
            // Process the file directly from PHP's temporary upload location
            $uploadPath = $uploadedFile['tmp_name'];
            error_log("Processing uploaded file directly: $uploadPath");
            
                try {
                    $zones = parseCsvFile($uploadPath);
                    global $csvDebugInfo, $firebaseDebugErrors;
                    
                    if (!empty($zones)) {
                        // Check for conflicts instead of directly saving
                        error_log("Starting conflict analysis for " . count($zones) . " zones from CSV");
                        try {
                            $conflictAnalysis = analyzeZoneConflicts($zones, $firebase);
                            error_log("Conflict analysis completed. Conflicts found: " . count($conflictAnalysis['conflicts']));
                        } catch (Exception $e) {
                            error_log("Error during conflict analysis: " . $e->getMessage());
                            $error = 'Erro ao analisar conflitos: ' . $e->getMessage();
                            // Continue with regular flow instead of JSON response
                            $conflictAnalysis = ['conflicts' => [], 'summary' => ['new' => count($zones), 'conflicts' => 0, 'identical' => 0]];
                        }
                        
                        if (!empty($conflictAnalysis['conflicts'])) {
                            // Store data in session for conflict resolution
                            session_start();
                            $_SESSION['pending_zones'] = $zones;
                            $_SESSION['conflict_analysis'] = $conflictAnalysis;
                            
                            // Clean output buffer to ensure clean JSON
                            if (ob_get_length()) {
                                ob_clean();
                            }
                            
                            // Return JSON response for conflicts
                            header('Content-Type: application/json; charset=utf-8');
                            header('Cache-Control: no-cache, must-revalidate');
                            
                            $response = [
                                'status' => 'conflicts',
                                'conflicts' => $conflictAnalysis['conflicts'],
                                'summary' => [
                                    'total' => count($zones),
                                    'new' => $conflictAnalysis['summary']['new'],
                                    'conflicts' => $conflictAnalysis['summary']['conflicts'],
                                    'identical' => $conflictAnalysis['summary']['identical']
                                ]
                            ];
                            
                            error_log("Sending conflict response: " . json_encode($response));
                            echo json_encode($response);
                            exit();
                        } else {
                            // No conflicts, proceed with import
                            $result = processZonesWithResolutions($zones, [], $firebase);
                            
                            $successMsg = [];
                            if ($result['imported'] > 0) {
                                $successMsg[] = "{$result['imported']} novas zonas importadas";
                            }
                            if ($result['skipped'] > 0) {
                                $successMsg[] = "{$result['skipped']} zonas ignoradas (já existem com dados inalterados)";
                            }
                            
                            // Store detailed results for JavaScript
                            if (session_status() === PHP_SESSION_NONE) {
                                session_start();
                            }
                            $_SESSION['import_details'] = $result;
                            error_log("Import details stored in session " . session_id() . ": " . print_r($result, true));
                            
                            $success = "Importação concluída: " . implode(', ', $successMsg) . ".";
                        }
                    } else {
                        $error = 'Nenhuma zona válida encontrada no ficheiro CSV. Verifique o formato: Zona;NIFEntidade;NomeZona;Email;QuotaZona;MinSelo;MaxSelo';
                        
                        // Add debug info about why no zones were found
                        $totalLines = getTotalLines($uploadPath);
                        $error .= " [DEBUG: Total linhas: $totalLines; " . implode("; ", $csvDebugInfo ?? ['no debug info']) . "]";
                    }
                } catch (Exception $e) {
                    $error = 'Erro durante a importação: ' . $e->getMessage();
            }
        } else {
            $error = 'Please upload a CSV file.';
        }
    }
}

// Handle delete action
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete') {
    $zoneId = $_POST['zone_id'] ?? '';
    if (!empty($zoneId)) {
        try {
            $result = deleteZoneFromFirestore($zoneId, $firebase);
            if ($result) {
                // Set success message in session to display after redirect
                if (session_status() === PHP_SESSION_NONE) {
                    session_start();
                }
                $_SESSION['success_message'] = 'Zona de caça eliminada com sucesso.';
                // Redirect to prevent POST resend dialog
                header('Location: ' . $_SERVER['PHP_SELF']);
                exit();
            } else {
                $error = 'Failed to delete hunting zone.';
            }
        } catch (Exception $e) {
            $error = 'Error deleting zone: ' . $e->getMessage();
        }
    }
}

// Handle edit action
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'edit') {
    error_log("Edit action triggered for zone ID: " . ($_POST['zone_id'] ?? 'unknown'));
    $zoneId = $_POST['zone_id'] ?? '';
    if (!empty($zoneId)) {
        try {
            // Validate required fields
            $requiredFields = ['zona', 'nifEntidade', 'nomeZona', 'email', 'quotaZona', 'minSelo', 'maxSelo', 'status'];
            foreach ($requiredFields as $field) {
                if (empty($_POST[$field])) {
                    $error = 'Todos os campos obrigatórios devem ser preenchidos.';
                    break;
                }
            }
            
            if (empty($error)) {
                // Additional validation for registered zones
                if ($_POST['status'] === 'registered') {
                    if (empty($_POST['registeredBy']) || empty($_POST['registeredFirstName']) || empty($_POST['registeredLastName'])) {
                        $error = 'Para zonas registadas, o email e nome completo de quem registou são obrigatórios.';
                        error_log("Edit validation error: Missing registered fields");
                    }
                }
            } else {
                error_log("Edit validation error: " . $error);
            }
            
            if (empty($error)) {
                // Check for duplicates (excluding current zone)
                $zones = getHuntingZonesFromFirestore($firebase);
                
                foreach ($zones as $zone) {
                    // Skip current zone being edited
                    if (isset($zone['id']) && $zone['id'] === $zoneId) {
                        continue;
                    }
                    
                    if ($zone['zona'] == $_POST['zona']) {
                        $error = 'Zona número ' . $_POST['zona'] . ' já existe na base de dados (' . $zone['nomeZona'] . ').';
                        break;
                    }
                    // NIF validation removed - multiple zones can have the same NIF
                }
            }
            
            if (empty($error)) {
                $zoneData = [
                    'zona' => intval($_POST['zona']),
                    'nifEntidade' => $_POST['nifEntidade'],
                    'nomeZona' => $_POST['nomeZona'],
                    'email' => $_POST['email'],
                    'quotaZona' => intval($_POST['quotaZona']),
                    'minSelo' => intval($_POST['minSelo']),
                    'maxSelo' => intval($_POST['maxSelo']),
                    'status' => $_POST['status'],
                    'updatedAt' => date('c')
                ];

                // Add registeredBy fields if status is registered and email is provided
                if ($_POST['status'] === 'registered' && !empty($_POST['registeredBy'])) {
                    $zoneData['registeredBy'] = $_POST['registeredBy'];
                    $zoneData['registeredFirstName'] = $_POST['registeredFirstName'] ?? '';
                    $zoneData['registeredLastName'] = $_POST['registeredLastName'] ?? '';
                } else {
                    // Remove registeredBy fields if status is not registered
                    $zoneData['registeredBy'] = null;
                    $zoneData['registeredFirstName'] = null;
                    $zoneData['registeredLastName'] = null;
                }
                
                $result = updateZoneInFirestore($zoneId, $zoneData, $firebase);
                if ($result) {
                    // Set success message in session to display after redirect
                    if (session_status() === PHP_SESSION_NONE) {
                        session_start();
                    }
                    $_SESSION['success_message'] = 'Zona de caça atualizada com sucesso.';
                    error_log("Edit action: Setting session message and redirecting");
                    error_log("Session ID: " . session_id());
                    error_log("Redirect URL: " . $_SERVER['PHP_SELF']);
                    // Redirect to prevent POST resend dialog
                    header('Location: ' . $_SERVER['PHP_SELF']);
                    exit();
                } else {
                    $error = 'Falha ao atualizar zona de caça.';
                    error_log("Edit action: Update failed");
                }
            }
        } catch (Exception $e) {
            $error = 'Erro ao atualizar zona: ' . $e->getMessage();
            error_log("Edit action exception: " . $e->getMessage());
        }
    }
}

// Handle validation action
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'validate') {
    $field = $_POST['field'] ?? '';
    $value = $_POST['value'] ?? '';
    $excludeZoneId = $_POST['exclude_zone_id'] ?? ''; // For edit mode
    
    if (empty($value)) {
        echo json_encode(['valid' => true]);
        exit();
    }
    
    try {
        $zones = getHuntingZonesFromFirestore($firebase);
        $exists = false;
        
        if ($field === 'zona') {
            $conflictingZone = null;
            foreach ($zones as $zone) {
                // Skip the zone being edited
                if (!empty($excludeZoneId) && isset($zone['id']) && $zone['id'] === $excludeZoneId) {
                    continue;
                }
                
                if ($zone['zona'] == $value) {
                    $exists = true;
                    $conflictingZone = $zone;
                    break;
                }
            }
            
            if ($exists && $conflictingZone) {
                echo json_encode(['valid' => false, 'message' => 'Zona número ' . $value . ' já existe na base de dados', 'zoneName' => $conflictingZone['nomeZona']]);
            } else {
                echo json_encode(['valid' => true]);
            }
        } elseif ($field === 'nif') {
            // NIF validation removed - multiple zones can have the same NIF
            echo json_encode(['valid' => true]);
        } else {
            echo json_encode(['valid' => true]);
        }
    } catch (Exception $e) {
        echo json_encode(['valid' => true]);
    }
    
    exit();
}

// Handle import details request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'get_import_details') {
    session_start();
    error_log("Import details request received. Session ID: " . session_id());
    $details = $_SESSION['import_details'] ?? null;
    error_log("Import details found in session: " . ($details ? 'YES' : 'NO'));
    
    if ($details) {
        error_log("Returning import details: " . print_r($details, true));
        // Clear the details from session after retrieving
        unset($_SESSION['import_details']);
        
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode($details);
        exit();
    } else {
        error_log("No import details found in session");
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode(['error' => 'No import details found']);
        exit();
    }
}

// Handle create action
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'create') {
    try {
        // Validate required fields
        $requiredFields = ['zona', 'nifEntidade', 'nomeZona', 'email', 'quotaZona', 'minSelo', 'maxSelo', 'status'];
        foreach ($requiredFields as $field) {
            if (empty($_POST[$field])) {
                $error = 'Todos os campos obrigatórios devem ser preenchidos.';
                break;
            }
        }
        
        if (empty($error)) {
            // Check for duplicates
            $zones = getHuntingZonesFromFirestore($firebase);
            
            foreach ($zones as $zone) {
                if ($zone['zona'] == $_POST['zona']) {
                    $error = 'Zona número ' . $_POST['zona'] . ' já existe na base de dados (' . $zone['nomeZona'] . ').';
                    break;
                }
                // NIF validation removed - multiple zones can have the same NIF
            }
        }
        
        if (empty($error)) {
            $zoneData = [
                'zona' => intval($_POST['zona']),
                'nifEntidade' => $_POST['nifEntidade'],
                'nomeZona' => $_POST['nomeZona'],
                'email' => $_POST['email'],
                'quotaZona' => intval($_POST['quotaZona']),
                'minSelo' => intval($_POST['minSelo']),
                'maxSelo' => intval($_POST['maxSelo']),
                'status' => $_POST['status'],
                'createdAt' => date('c'),
                'updatedAt' => date('c')
            ];

            // Add registeredBy fields if status is registered and email is provided
            if ($_POST['status'] === 'registered' && !empty($_POST['registeredBy'])) {
                $zoneData['registeredBy'] = $_POST['registeredBy'];
                $zoneData['registeredFirstName'] = $_POST['registeredFirstName'] ?? '';
                $zoneData['registeredLastName'] = $_POST['registeredLastName'] ?? '';
            }
            
            // Generate a unique document ID based on zona number
            $documentId = 'zone_' . $_POST['zona'];
            
            $result = createZoneInFirestore($documentId, $zoneData, $firebase);
            if ($result) {
                // Set success message in session to display after redirect
                if (session_status() === PHP_SESSION_NONE) {
                    session_start();
                }
                $_SESSION['success_message'] = 'Nova zona de caça criada com sucesso.';
                // Redirect to prevent POST resend dialog
                header('Location: ' . $_SERVER['PHP_SELF']);
                exit();
            } else {
                $error = 'Falha ao criar zona de caça.';
            }
        }
    } catch (Exception $e) {
        $error = 'Erro ao criar zona: ' . $e->getMessage();
    }
}

// Helper function to check if a zone is a test zone
function isTestZone($nif) {
    // Check if NIF starts with "12345678" followed by a single digit (0-9)
    return preg_match('/^12345678[0-9]$/', $nif);
}

// Fetch hunting zones from Firestore
try {
    $allHuntingZones = getHuntingZonesFromFirestore($firebase);
    
    // Filter zones based on user type
    if (isSuperAdmin()) {
        // Super admins see all zones (including test zones)
        $huntingZones = $allHuntingZones;
    } else {
        // Regular admins don't see test zones
        $huntingZones = array_filter($allHuntingZones, function($zone) {
            return !isTestZone($zone['nifEntidade']);
        });
    }
} catch (Exception $e) {
    $error = 'Error fetching hunting zones: ' . $e->getMessage();
}

function parseCsvFile($filePath) {
    $zones = [];
    $debugInfo = [];
    
    // Set locale to handle Portuguese characters properly
    setlocale(LC_ALL, 'pt_PT.UTF-8', 'pt_PT', 'Portuguese_Portugal.1252');
    
    // Try to detect file encoding
    $fileContent = file_get_contents($filePath);
    $encoding = mb_detect_encoding($fileContent, ['UTF-8', 'ISO-8859-1', 'Windows-1252', 'CP1252'], true);
    error_log("Detected CSV file encoding: " . ($encoding ?: 'unknown'));
    
    // Convert file to UTF-8 if needed
    if ($encoding && $encoding !== 'UTF-8') {
        $fileContent = mb_convert_encoding($fileContent, 'UTF-8', $encoding);
        error_log("Converting file from $encoding to UTF-8");
        // Write UTF-8 version to temporary file
        $tempPath = $filePath . '.utf8';
        file_put_contents($tempPath, $fileContent);
        $filePath = $tempPath;
    }
    
    if (($handle = fopen($filePath, "r")) !== FALSE) {
        $header = fgetcsv($handle, 1000, ";");
        error_log("CSV Header: " . print_r($header, true));
        $debugInfo[] = "Header: " . implode("|", $header ?: ['no header']);
        
        $lineNumber = 1;
        $validLines = 0;
        $skippedLines = 0;
        
        while (($data = fgetcsv($handle, 1000, ";")) !== FALSE) {
            $lineNumber++;
            
            // Debug: show first few lines in detail
            if ($lineNumber <= 5) {
                error_log("Line $lineNumber (debug): " . print_r($data, true));
                error_log("Line $lineNumber column count: " . count($data));
                $debugInfo[] = "Line $lineNumber (" . count($data) . " cols): " . implode("|", $data);
            }
            
            if (count($data) >= 7 && !empty(trim($data[0]))) {
                $zone = [
                    'zona' => trim($data[0]),
                    'nifEntidade' => trim($data[1]),
                    'nomeZona' => trim($data[2]),
                    'email' => trim($data[3]),
                    'quotaZona' => intval($data[4]),
                    'minSelo' => intval($data[5]),
                    'maxSelo' => intval($data[6]),
                    'status' => 'not registered',
                    'createdAt' => date('c')
                ];
                
                // Log zones with non-ASCII characters for debugging
                if (!mb_check_encoding($zone['nomeZona'], 'ASCII')) {
                    error_log("CSV: Zone with accented chars - '{$zone['zona']}': '{$zone['nomeZona']}'");
                }
                $zones[] = $zone;
                $validLines++;
                
                // Debug: show first few valid zones
                if ($validLines <= 3) {
                    error_log("Valid zone #$validLines: " . print_r($zone, true));
                }
            } else {
                $skippedLines++;
                if ($lineNumber <= 5) {
                    error_log("Skipped line $lineNumber - insufficient data: " . count($data) . " columns, first col: '" . ($data[0] ?? 'empty') . "'");
                }
            }
        }
        fclose($handle);
        
        error_log("CSV Processing Summary:");
        error_log("- Total lines read: $lineNumber");
        error_log("- Valid zones: $validLines");
        error_log("- Skipped lines: $skippedLines");
    } else {
        error_log("Failed to open CSV file: $filePath");
    }
    
    error_log("Final zones count: " . count($zones));
    
    // Store debug info in a global variable for later use
    global $csvDebugInfo;
    $csvDebugInfo = $debugInfo;
    
    return $zones;
}

function analyzeZoneConflicts($zones, $firebase) {
    $conflicts = [];
    $summary = ['new' => 0, 'conflicts' => 0, 'identical' => 0];
    
    error_log("Starting conflict analysis for " . count($zones) . " zones");
    
    // Use admin token for reading existing zones
    try {
        $adminToken = $firebase->getAdminAccessToken();
        if ($adminToken) {
            $firebase->setAccessToken($adminToken);
        }
    } catch (Exception $e) {
        error_log("Error getting admin token for conflict analysis: " . $e->getMessage());
    }
    
    // Get existing zones from database with pagination
    $existingZones = [];
    try {
        $allExistingZones = [];
        $pageToken = null;
        
        do {
            $pageZones = $firebase->listDocuments('zonasCaca', $pageToken, 1000);
            
            // Extract pagination token if present
            $pageToken = $pageZones['_nextPageToken'] ?? null;
            unset($pageZones['_nextPageToken']); // Remove pagination token from results
            
            // Merge results
            $allExistingZones = array_merge($allExistingZones, $pageZones);
            
        } while ($pageToken);
        
        if ($allExistingZones && is_array($allExistingZones)) {
            foreach ($allExistingZones as $docId => $data) {
                if ($docId !== '_nextPageToken' && isset($data['zona'])) {
                    $existingZones[$data['zona']] = $data;
                }
            }
        }
        error_log("Found " . count($existingZones) . " existing zones in database");
    } catch (Exception $e) {
        error_log("Error reading existing zones: " . $e->getMessage());
        throw new Exception("Erro ao verificar zonas existentes: " . $e->getMessage());
    }
    
    foreach ($zones as $zone) {
        $zoneNumber = $zone['zona'];
        
        if (!isset($existingZones[$zoneNumber])) {
            // Zone doesn't exist - will be imported
            $summary['new']++;
        } else {
            // Zone exists - check if data is different
            $existing = $existingZones[$zoneNumber];
            $hasChanges = false;
            $changes = [];
            
            // Compare relevant fields
            $fieldsToCompare = ['nifEntidade', 'nomeZona', 'email', 'quotaZona', 'minSelo', 'maxSelo'];
            foreach ($fieldsToCompare as $field) {
                $existingValue = $existing[$field] ?? '';
                $newValue = $zone[$field] ?? '';
                
                // Convert to string for comparison
                $existingValue = (string)$existingValue;
                $newValue = (string)$newValue;
                
                if ($existingValue !== $newValue) {
                    $hasChanges = true;
                    $changes[$field] = [
                        'old' => $existingValue,
                        'new' => $newValue
                    ];
                }
            }
            
            if ($hasChanges) {
                // Data is different - conflict detected
                $conflicts[] = [
                    'zona' => $zoneNumber,
                    'existing' => $existing,
                    'new' => $zone,
                    'changes' => $changes
                ];
                $summary['conflicts']++;
            } else {
                // Data is identical - will be skipped  
                $summary['identical']++;
            }
        }
    }
    
    error_log("Conflict analysis summary: " . print_r($summary, true));
    
    return [
        'conflicts' => $conflicts,
        'summary' => $summary
    ];
}

function processZonesWithResolutions($zones, $conflictResolutions, $firebase) {
    $imported = 0;
    $updated = 0;
    $skipped = 0;
    $errors = [];
    $importedZones = [];
    $updatedZones = [];
    $skippedZones = [];
    $errorDetails = [];
    
    error_log("=== PROCESSING ZONES WITH RESOLUTIONS ===");
    error_log("Starting to process " . count($zones) . " zones with resolutions");
    error_log("Conflict resolutions: " . print_r($conflictResolutions, true));
    
    // Use admin token for zone operations
    try {
        $adminToken = $firebase->getAdminAccessToken();
        if ($adminToken) {
            $firebase->setAccessToken($adminToken);
            error_log("✅ Admin token obtained and set successfully");
        } else {
            error_log("⚠️ No admin token received");
        }
    } catch (Exception $e) {
        error_log("❌ Error getting admin token: " . $e->getMessage());
    }
    
    // Get existing zones from database with pagination
    $existingZones = [];
    try {
        error_log("📋 Reading existing zones from database...");
        $allExistingZones = [];
        $pageToken = null;
        
        do {
            $pageZones = $firebase->listDocuments('zonasCaca', $pageToken, 1000);
            
            // Extract pagination token if present
            $pageToken = $pageZones['_nextPageToken'] ?? null;
            unset($pageZones['_nextPageToken']); // Remove pagination token from results
            
            // Merge results
            $allExistingZones = array_merge($allExistingZones, $pageZones);
            
        } while ($pageToken);
        
        error_log("📋 Raw response from listDocuments: " . print_r($allExistingZones, true));
        
        if ($allExistingZones && is_array($allExistingZones)) {
            foreach ($allExistingZones as $docId => $data) {
                if ($docId !== '_nextPageToken' && isset($data['zona'])) {
                    $existingZones[$data['zona']] = $docId;
                }
            }
            error_log("📋 Found " . count($existingZones) . " existing zones in database");
        } else {
            error_log("📋 No existing zones found or invalid response format");
        }
    } catch (Exception $e) {
        error_log("❌ Error reading existing zones: " . $e->getMessage());
        throw new Exception("Erro ao verificar zonas existentes: " . $e->getMessage());
    }
    
    foreach ($zones as $zone) {
        $zoneNumber = $zone['zona'];
        $zoneName = $zone['nomeZona'];
        $safeZoneId = preg_replace('/[^a-zA-Z0-9_-]/', '_', $zoneNumber);
        $docId = 'zone_' . $safeZoneId;
        
        try {
            error_log("Processing zone $zoneNumber ($zoneName) - exists in DB: " . (isset($existingZones[$zoneNumber]) ? 'YES' : 'NO'));
            
            if (!isset($existingZones[$zoneNumber])) {
                // New zone - import it
                error_log("Attempting to import new zone $zoneNumber with docId: $docId");
                $result = $firebase->setDocument('zonasCaca', $docId, $zone);
                error_log("Firebase setDocument result for zone $zoneNumber: " . ($result ? 'SUCCESS' : 'FAILED'));
                
                if ($result) {
                    $imported++;
                    $importedZones[] = "Zona $zoneNumber ($zoneName)";
                    error_log("✅ Successfully imported new zone: $zoneNumber");
                } else {
                    $errors[] = "Failed to import zone: $zoneNumber";
                    $errorDetails[] = [
                        'zone' => $zoneNumber,
                        'name' => $zoneName,
                        'error' => 'Falha ao guardar na base de dados'
                    ];
                    error_log("❌ Failed to import zone: $zoneNumber");
                }
            } else {
                // Zone exists - check conflict resolution
                $resolution = $conflictResolutions[$zoneNumber] ?? 'skip';
                error_log("Zone $zoneNumber exists, resolution: $resolution");
                
                if ($resolution === 'update') {
                    // Update existing zone
                    $result = $firebase->setDocument('zonasCaca', $docId, $zone);
                    if ($result) {
                        $updated++;
                        $updatedZones[] = "Zona $zoneNumber ($zoneName)";
                        error_log("✅ Successfully updated existing zone: $zoneNumber");
                    } else {
                        $errors[] = "Failed to update zone: $zoneNumber";
                        $errorDetails[] = [
                            'zone' => $zoneNumber,
                            'name' => $zoneName,
                            'error' => 'Falha ao atualizar na base de dados'
                        ];
                        error_log("❌ Failed to update zone: $zoneNumber");
                    }
                } else {
                    // Skip zone (either identical or user chose to skip)
                    $skipped++;
                    $skippedZones[] = "Zona $zoneNumber ($zoneName)";
                    error_log("⏭️ Skipped zone: $zoneNumber");
                }
            }
        } catch (Exception $e) {
            $errors[] = "Error processing zone $zoneNumber: " . $e->getMessage();
            $errorDetails[] = [
                'zone' => $zoneNumber,
                'name' => $zoneName,
                'error' => $e->getMessage()
            ];
            error_log("❌ Exception processing zone $zoneNumber: " . $e->getMessage());
        }
    }
    
    error_log("=== PROCESSING COMPLETE ===");
    error_log("Imported: $imported, Updated: $updated, Skipped: $skipped, Errors: " . count($errors));
    error_log("Imported zones: " . print_r($importedZones, true));
    error_log("Updated zones: " . print_r($updatedZones, true));
    error_log("Skipped zones: " . print_r($skippedZones, true));
    
    if (!empty($errors)) {
        error_log("Errors encountered: " . implode("; ", $errors));
        error_log("Error details: " . print_r($errorDetails, true));
    }
    
    // Test if we can read back the zones we just saved
    try {
        error_log("Testing zone read-back from database...");
        $testZones = getHuntingZonesFromFirestore($firebase);
        error_log("Total zones found in database after import: " . count($testZones));
        if (count($testZones) > 0) {
            error_log("Sample zone from database: " . print_r($testZones[0], true));
        }
    } catch (Exception $e) {
        error_log("Error reading zones back from database: " . $e->getMessage());
    }
    
    $result = [
        'imported' => $imported,
        'updated' => $updated,
        'skipped' => $skipped,
        'errors' => $errors,
        'importedZones' => $importedZones,
        'updatedZones' => $updatedZones,
        'skippedZones' => $skippedZones,
        'errorDetails' => $errorDetails,
        'totalProcessed' => count($zones)
    ];
    
    error_log("Final result array: " . print_r($result, true));
    return $result;
}

function saveZonesToFirestore($zones, $firebase) {
    $savedCount = 0;
    $errorCount = 0;
    $firebaseErrors = [];
    $savedZones = [];
    $errorDetails = [];
    
    error_log("Starting to save " . count($zones) . " zones to Firestore");
    
    // Use admin token for zone imports (like profile image deletion)
    try {
        $adminToken = $firebase->getAdminAccessToken();
        if ($adminToken) {
            $firebase->setAccessToken($adminToken);
            error_log("Using admin access token for zone import");
        } else {
            error_log("Failed to get admin token, using regular user token");
        }
    } catch (Exception $e) {
        error_log("Error getting admin token: " . $e->getMessage());
        $firebaseErrors[] = "Admin token error: " . $e->getMessage();
    }
    
    // Test Firebase connection first
    try {
        // Test zones access with pagination
        $testResult = [];
        $pageToken = null;
        
        do {
            $pageZones = $firebase->listDocuments('zonasCaca', $pageToken, 1000);
            
            // Extract pagination token if present
            $pageToken = $pageZones['_nextPageToken'] ?? null;
            unset($pageZones['_nextPageToken']); // Remove pagination token from results
            
            // Merge results
            $testResult = array_merge($testResult, $pageZones);
            
        } while ($pageToken);
        error_log("Firebase connection test successful");
    } catch (Exception $e) {
        error_log("Firebase connection test failed: " . $e->getMessage());
        $firebaseErrors[] = "Connection test failed: " . $e->getMessage();
    }
    
    foreach ($zones as $index => $zone) {
        try {
            // Generate safe document ID by removing/replacing special characters
            $safeZoneId = preg_replace('/[^a-zA-Z0-9_-]/', '_', $zone['zona']);
            $docId = 'zone_' . $safeZoneId;
            
            // Log detailed info for each zone attempt
            error_log("Attempting to save zone #" . ($index + 1) . ":");
            error_log("  Original zona: '{$zone['zona']}'");
            error_log("  Safe zona ID: '$safeZoneId'");
            error_log("  Document ID: '$docId'");
            error_log("  Nome da zona: '{$zone['nomeZona']}'");
            
            // Check if zone name contains non-ASCII characters
            if (!mb_check_encoding($zone['nomeZona'], 'ASCII')) {
                error_log("  INFO: Zone name contains accented characters");
            }
            
            $result = $firebase->setDocument('zonasCaca', $docId, $zone);
            
            if ($result) {
                $savedCount++;
                $savedZones[] = "Zona {$zone['zona']} ({$zone['nomeZona']})";
                error_log("  SUCCESS: Successfully saved zone: $docId");
            } else {
                $errorCount++;
                $errorMsg = "Zone {$zone['zona']} ({$zone['nomeZona']}): No result returned";
                error_log("  FAILED: Failed to save zone: $docId - No result returned");
                $firebaseErrors[] = $errorMsg;
                $errorDetails[] = [
                    'zone' => $zone['zona'],
                    'name' => $zone['nomeZona'],
                    'error' => 'No result returned from Firebase'
                ];
            }
            
        } catch (Exception $e) {
            $errorCount++;
            $errorMsg = "ERROR saving zone '{$zone['zona']}' ({$zone['nomeZona']}): " . $e->getMessage();
            error_log($errorMsg);
            $firebaseErrors[] = $errorMsg;
            $errorDetails[] = [
                'zone' => $zone['zona'],
                'name' => $zone['nomeZona'],
                'error' => $e->getMessage()
            ];
            
            // Log the full zone data for failed saves
            error_log("  Failed zone data: " . print_r($zone, true));
        }
    }
    
    error_log("Firestore save summary:");
    error_log("- Successfully saved: $savedCount");
    error_log("- Errors: $errorCount");
    error_log("- Total attempted: " . count($zones));
    
    // Store Firebase errors globally for UI display
    global $firebaseDebugErrors, $savedZonesDetails, $errorZoneDetails;
    $firebaseDebugErrors = $firebaseErrors;
    $savedZonesDetails = $savedZones;
    $errorZoneDetails = $errorDetails;
    
    return $savedCount;
}

function addEmailVerificationStatus($zones, $firebase) {
    // Collect unique valid emails
    $emailsToCheck = [];
    foreach ($zones as $zone) {
        $email = $zone['email'];
        if ($email !== 'N/A' && filter_var($email, FILTER_VALIDATE_EMAIL) && !in_array($email, $emailsToCheck)) {
            $emailsToCheck[] = $email;
        }
    }
    
    // Check verification status for each unique email
    $emailStatuses = [];
    foreach ($emailsToCheck as $email) {
        $emailStatuses[$email] = checkEmailVerificationStatus($email, $firebase);
    }
    
    // Add verification status to zones
    foreach ($zones as &$zone) {
        $email = $zone['email'];
        if ($email !== 'N/A' && filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $zone['emailVerified'] = $emailStatuses[$email] ?? 'unknown';
        } else {
            $zone['emailVerified'] = 'unknown';
        }
    }
    
    return $zones;
}

function checkEmailVerificationStatus($email, $firebase, $useCache = true) {
    // Cache file path
    $cacheDir = __DIR__ . '/../../cache/email_verification/';
    if (!is_dir($cacheDir)) {
        mkdir($cacheDir, 0755, true);
    }
    $cacheFile = $cacheDir . md5($email) . '.json';
    
    // Check cache first (cache for 1 hour)
    if ($useCache && file_exists($cacheFile)) {
        $cacheData = json_decode(file_get_contents($cacheFile), true);
        if ($cacheData && (time() - $cacheData['timestamp']) < 3600) {
            return $cacheData['status'];
        }
    }
    
    try {
        // Use admin token to check Firebase Auth user
        $adminToken = $firebase->getAdminAccessToken();
        if (!$adminToken) {
            return 'unknown';
        }
        
        // Make request to Firebase Auth Admin API to get user by email
        $url = "https://identitytoolkit.googleapis.com/v1/accounts:lookup?key=" . FIREBASE_API_KEY;
        $data = [
            'email' => [$email]
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $adminToken
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10); // Reduced timeout
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        $status = 'unknown';
        if ($httpCode === 200) {
            $result = json_decode($response, true);
            if (isset($result['users']) && !empty($result['users'])) {
                $user = $result['users'][0];
                $status = isset($user['emailVerified']) && $user['emailVerified'] ? 'verified' : 'not_verified';
            } else {
                $status = 'not_registered';
            }
        }
        
        // Cache the result
        if ($useCache) {
            file_put_contents($cacheFile, json_encode([
                'status' => $status,
                'timestamp' => time(),
                'email' => $email
            ]));
        }
        
        return $status;
    } catch (Exception $e) {
        error_log("Error checking email verification for $email: " . $e->getMessage());
        return 'unknown';
    }
}

function getHuntingZonesFromFirestore($firebase) {
    try {
        error_log("=== STARTING TO READ ZONES FROM FIRESTORE ===");
        
        // Use admin token for reading zones (consistent with saving)
        try {
            $adminToken = $firebase->getAdminAccessToken();
            if ($adminToken) {
                $firebase->setAccessToken($adminToken);
                error_log("Using admin access token for reading zones");
            } else {
                error_log("No admin token available for reading zones");
            }
        } catch (Exception $e) {
            error_log("Error getting admin token for reading: " . $e->getMessage());
        }
        
        error_log("Attempting to list ALL documents from 'zonasCaca' collection...");
        $zones = [];
        $pageToken = null;
        $totalFetched = 0;
        
        do {
            $result = $firebase->listDocuments('zonasCaca', $pageToken, 1000);
            
            // Extract nextPageToken before processing
            $nextPageToken = null;
            if (is_array($result) && isset($result['_nextPageToken'])) {
                $nextPageToken = $result['_nextPageToken'];
                unset($result['_nextPageToken']); // Remove pagination token from results
            }
            
            error_log("listDocuments batch result: " . (is_array($result) ? count($result) . " documents found" : "Not an array: " . gettype($result)));
            
            if ($result && is_array($result)) {
                $totalFetched += count($result);
                error_log("Processing batch of " . count($result) . " zones (total so far: $totalFetched)...");
                
                foreach ($result as $docId => $data) {
                    // Skip pagination token entries
                    if ($docId === '_nextPageToken') continue;
                    
                    $email = $data['email'] ?? 'N/A';
                    
                    $zones[] = [
                        'id' => $docId,
                        'zona' => $data['zona'] ?? 'N/A',
                        'nomeZona' => $data['nomeZona'] ?? 'N/A', 
                        'nifEntidade' => $data['nifEntidade'] ?? 'N/A',
                        'email' => $email,
                        'quotaZona' => $data['quotaZona'] ?? 0,
                        'minSelo' => $data['minSelo'] ?? 0,
                        'maxSelo' => $data['maxSelo'] ?? 0,
                        'status' => $data['status'] ?? 'not registered',
                        'registeredBy' => $data['registeredBy'] ?? null,
                        'registeredFirstName' => $data['registeredFirstName'] ?? null,
                        'registeredLastName' => $data['registeredLastName'] ?? null
                    ];
                }
                
                // Set pageToken for next iteration
                $pageToken = $nextPageToken;
                if ($pageToken) {
                    error_log("Found next page token, fetching more documents...");
                }
            } else {
                error_log("No result or result is not array. Result type: " . gettype($result));
                break;
            }
        } while ($pageToken);
        
        error_log("Successfully processed total of " . count($zones) . " zones for display");
        
        // Email verification status is now checked on-demand via diagnostic tool
        // to avoid Firebase API rate limits
        
        error_log("=== FINISHED READING ZONES - RETURNING " . count($zones) . " ZONES ===");
        return $zones;
    } catch (Exception $e) {
        error_log("Exception in getHuntingZonesFromFirestore: " . $e->getMessage());
        throw new Exception('Error fetching hunting zones: ' . $e->getMessage());
    }
}

function deleteZoneFromFirestore($zoneId, $firebase) {
    try {
        // Use admin token for deleting
        $adminToken = $firebase->getAdminAccessToken();
        if ($adminToken) {
            $firebase->setAccessToken($adminToken);
        }
        
        $result = $firebase->deleteDocument('zonasCaca', $zoneId);
        return $result !== false;
    } catch (Exception $e) {
        throw new Exception('Error deleting zone: ' . $e->getMessage());
    }
}

function updateZoneInFirestore($zoneId, $zoneData, $firebase) {
    try {
        // Use admin token for writing
        $adminToken = $firebase->getAdminAccessToken();
        if ($adminToken) {
            $firebase->setAccessToken($adminToken);
        }
        
        $result = $firebase->setDocument('zonasCaca', $zoneId, $zoneData);
        return $result !== false;
    } catch (Exception $e) {
        throw new Exception('Error updating zone: ' . $e->getMessage());
    }
}

function createZoneInFirestore($zoneId, $zoneData, $firebase) {
    try {
        // Use admin token for writing
        $adminToken = $firebase->getAdminAccessToken();
        if ($adminToken) {
            $firebase->setAccessToken($adminToken);
        }
        
        $result = $firebase->setDocument('zonasCaca', $zoneId, $zoneData);
        return $result !== false;
    } catch (Exception $e) {
        throw new Exception('Error creating zone: ' . $e->getMessage());
    }
}

function getTotalLines($filePath) {
    $lineCount = 0;
    if (($handle = fopen($filePath, "r")) !== FALSE) {
        while (($line = fgets($handle)) !== FALSE) {
            $lineCount++;
        }
        fclose($handle);
    }
    return $lineCount;
}
?>
<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo SITE_NAME; ?> - Zonas de Caça</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="<?php echo SITE_URL; ?>/assets/css/style.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    
    <style>
        .content {
            padding-top: 5rem;
            padding-left: 0;
            padding-right: 0;
            padding-bottom: 0;
        }

        .page-header {
            background: white;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .page-header h1 {
            margin: 0;
            font-size: 1.5rem;
            color: #374151;
            font-weight: 500;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 1rem;
            margin: 0 1.5rem 1.5rem 1.5rem;
            overflow-x: auto;
            padding-bottom: 0.5rem;
        }

        /* Responsive stats grid */
        @media (max-width: 1200px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .stats-grid {
                display: flex;
                flex-wrap: nowrap;
                overflow-x: auto;
                scroll-behavior: smooth;
                -webkit-overflow-scrolling: touch;
                scrollbar-width: thin;
                scrollbar-color: #cbd5e1 #f1f5f9;
                margin: 0 1rem 1.5rem 1rem;
                padding: 0 0.5rem 0.5rem 0.5rem;
            }

            .stats-grid::-webkit-scrollbar {
                height: 6px;
            }

            .stats-grid::-webkit-scrollbar-track {
                background: #f1f5f9;
                border-radius: 3px;
            }

            .stats-grid::-webkit-scrollbar-thumb {
                background: #cbd5e1;
                border-radius: 3px;
            }

            .stats-grid::-webkit-scrollbar-thumb:hover {
                background: #94a3b8;
            }
        }

        @media (max-width: 480px) {
            .stats-grid {
                margin: 0 0.5rem 1.5rem 0.5rem;
                padding: 0 0.25rem 0.5rem 0.25rem;
            }
        }

        .stat-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        /* Responsive stat card adjustments */
        @media (max-width: 768px) {
            .stat-card {
                min-width: 280px;
                flex-shrink: 0;
                padding: 1.25rem;
            }
        }

        @media (max-width: 480px) {
            .stat-card {
                min-width: 260px;
                padding: 1rem;
            }
            
            .stat-card .icon {
                width: 36px;
                height: 36px;
            }
            
            .stat-card .icon i {
                font-size: 16px;
            }
            
            .stat-card .title {
                font-size: 0.8rem;
            }
            
            .stat-card .value {
                font-size: 1.25rem;
            }
        }

        .stat-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .stat-card .icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .stat-card .icon i {
            font-size: 18px;
            color: white;
        }

        .stat-content {
            flex: 1;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .stat-card .title {
            font-size: 0.875rem;
            color: #6B7280;
            font-weight: 600;
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 0.025em;
        }

        .stat-card .value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #374151;
            margin: 0;
        }

        .bg-blue {
            background-color: #3B82F6;
        }

        .bg-purple {
            background-color: #8B5CF6;
        }

        .bg-green {
            background-color: #10B981;
        }

        .bg-orange {
            background-color: #F59E0B;
        }

        /* Table card styling */
        .zones-table-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin: 0 1.5rem 1.5rem 1.5rem;
            overflow: hidden;
        }

        .zones-table-header {
            padding: 1.5rem;
            border-bottom: 1px solid #E5E7EB;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .zones-table-header h2 {
            margin: 0;
            font-size: 1.125rem;
            color: #374151;
            font-weight: 500;
        }

        .zones-table-body {
            padding: 0 1.5rem 1.5rem 1.5rem;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        /* DataTable responsive wrapper */
        .zones-table-body table {
            min-width: 1200px; /* Minimum width to trigger horizontal scroll */
        }

        /* Custom scrollbar for table */
        .zones-table-body::-webkit-scrollbar {
            height: 8px;
        }

        .zones-table-body::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 4px;
        }

        .zones-table-body::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 4px;
        }

        .zones-table-body::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        /* Firefox scrollbar */
        .zones-table-body {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e1 #f1f5f9;
        }

        /* Responsive table adjustments */
        @media (max-width: 768px) {
            .zones-table-card {
                margin: 0 1rem 1.5rem 1rem;
            }
            
            .zones-table-header {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }
            
            .zones-table-header h2 {
                text-align: center;
            }
            
            .header-actions {
                justify-content: center;
                flex-wrap: wrap;
                gap: 0.5rem;
            }
            
            .zones-table-body {
                padding: 0 1rem 1rem 1rem;
            }
        }

        @media (max-width: 480px) {
            .zones-table-card {
                margin: 0 0.5rem 1.5rem 0.5rem;
            }
            
            .zones-table-header {
                padding: 0.75rem;
            }
            
            .zones-table-body {
                padding: 0 0.75rem 0.75rem 0.75rem;
            }
            
            .header-actions .btn {
                font-size: 0.875rem;
                padding: 0.5rem 1rem;
            }
        }
        
        .zone-status {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }
        
        .zone-status.registered {
            background-color: #d1fae5;
            color: #065f46;
        }
        
        .zone-status.not-registered {
            background-color: #fef3cd;
            color: #92400e;
        }
        
        .zone-status.inactive {
            background-color: #fee2e2;
            color: #991b1b;
        }

        /* Email verification status styling */
        .email-status {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
            white-space: nowrap;
            display: inline-flex;
            align-items: center;
            gap: 0.25rem;
        }
        
        .email-status.verified {
            background-color: #d1fae5;
            color: #065f46;
        }
        
        .email-status.not-verified {
            background-color: #fef3cd;
            color: #92400e;
        }
        
        .email-status.not-registered {
            background-color: #fee2e2;
            color: #991b1b;
        }
        
        .email-status.unknown {
            background-color: #f3f4f6;
            color: #6b7280;
        }

        /* Test zone styling - more prominent red background */
        .test-zone-row {
            background-color: #fee2e2 !important;
        }

        .test-zone-row td {
            background-color: #fee2e2 !important;
        }

        /* Test zone label */
        .test-zone-label {
            display: inline-block;
            background-color: #dc2626;
            color: white;
            font-size: 0.7rem;
            font-weight: 600;
            padding: 0.2rem 0.5rem;
            border-radius: 4px;
            margin-left: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .text-center {
            text-align: center !important;
        }

        /* Prevent line breaking in table cells */
        table td {
            white-space: nowrap;
        }

        table td:nth-child(2) {
            white-space: normal; /* Allow wrapping only for zone name */
            min-width: 200px;
        }

        /* Email column styling - wider to accommodate the diagnostic button */
        table th:nth-child(4),
        table td:nth-child(4) {
            min-width: 200px; /* Email column with diagnostic button */
        }

        .text-muted {
            color: #9ca3af !important;
            font-size: 0.875rem;
        }

        .me-2 {
            margin-right: 0.5rem !important;
        }

        /* Email styling */
        .email-link {
            color: #374151;
            text-decoration: none;
            font-size: 0.875rem;
            transition: color 0.2s ease;
        }

        .email-link:hover {
            color: var(--primary-color);
            text-decoration: underline;
        }

        /* Registered by styling */
        .registered-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            white-space: nowrap;
            display: inline-block;
        }

        .system-badge {
            background-color: #e0e7ff;
            color: #3730a3;
        }

        .mobile-badge {
            background-color: #dcfce7;
            color: #166534;
        }

        .form-help {
            font-size: 0.7rem;
            color: #6b7280;
            margin-top: 0.25rem;
            font-style: italic;
        }

        .header-actions {
            display: flex;
            gap: 0.5rem;
            align-items: center;
        }

        .alert {
            padding: 0.75rem 1rem;
            border-radius: 6px;
            border: 1px solid transparent;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.875rem;
        }

        .alert-danger {
            background-color: #fef2f2;
            border-color: #fecaca;
            color: #dc2626;
        }

        .alert-danger i {
            color: #dc2626;
        }

        .btn-loading {
            position: relative;
            pointer-events: none;
            opacity: 0.7;
        }

        .loading-spinner {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Enhanced Edit Modal Styling */
        .edit-modal {
            border-radius: 12px;
            border: none;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            display: flex;
            flex-direction: column;
            max-height: 90vh;
            overflow: hidden;
        }

        .edit-modal .modal-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-bottom: 2px solid #e2e8f0;
            border-radius: 12px 12px 0 0;
            padding: 0.3rem 1.5rem;
            flex-shrink: 0;
            height: 60px;
            min-height: 60px;
        }

        .edit-modal .modal-body {
            padding: 1rem;
            background: #fafbfc;
            flex: 1;
            overflow-y: auto;
            min-height: 0;
        }

        .edit-modal .modal-body::-webkit-scrollbar {
            width: 6px;
        }

        .edit-modal .modal-body::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }

        .edit-modal .modal-body::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }

        .edit-modal .modal-body::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }

        .form-section {
            background: white;
            border-radius: 8px;
            padding: 0.75rem;
            margin-bottom: 0.75rem;
            border: 1px solid #e2e8f0;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .section-header {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.75rem;
            padding-bottom: 0.4rem;
            border-bottom: 1px solid #f1f5f9;
        }

        .section-header i {
            color: var(--primary-color);
            font-size: 1rem;
            background: rgba(10, 126, 164, 0.1);
            padding: 0.4rem;
            border-radius: 6px;
        }

        .section-header span {
            font-weight: 600;
            color: #374151;
            font-size: 0.9rem;
        }

        .form-grid {
            display: grid;
            gap: 1rem;
        }

        .form-grid.two-col {
            grid-template-columns: 1fr 1fr;
        }

        .form-grid.three-col {
            grid-template-columns: 1fr 1fr 1fr;
        }

        .form-field {
            display: flex;
            flex-direction: column;
            min-width: 0;
        }

        .form-label {
            display: flex;
            align-items: center;
            gap: 0.4rem;
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.4rem;
            font-size: 0.8rem;
        }

        .form-label i {
            color: #6b7280;
            font-size: 0.8rem;
            width: 14px;
            text-align: center;
        }

        .form-input, .form-select {
            padding: 0.6rem 0.8rem;
            border: 2px solid #e2e8f0;
            border-radius: 6px;
            font-size: 0.8rem;
            color: #374151;
            background: white;
            transition: all 0.2s ease;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
            width: 100%;
            box-sizing: border-box;
            min-width: 0;
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(10, 126, 164, 0.1);
        }

        .form-input:hover, .form-select:hover {
            border-color: #94a3b8;
        }

        .form-select {
            cursor: pointer;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.6rem center;
            background-repeat: no-repeat;
            background-size: 1.2em 1.2em;
            padding-right: 2rem;
        }

        .edit-modal .modal-footer {
            background: #f8fafc;
            border-top: 2px solid #e2e8f0;
            border-radius: 0 0 12px 12px;
            padding: 1rem 1.5rem;
            gap: 0.75rem;
            flex-shrink: 0;
            display: flex;
            justify-content: flex-end;
            height: 60px;
            min-height: 60px;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .modal-dialog {
                max-width: 95vw !important;
                margin: 1rem auto !important;
                height: 85vh !important;
            }
            
            .form-grid.two-col,
            .form-grid.three-col {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }
            
            .edit-modal .modal-body {
                padding: 1rem;
            }
            
            .form-section {
                padding: 0.75rem;
                margin-bottom: 0.75rem;
            }
            
            .section-header {
                margin-bottom: 0.75rem;
            }
        }
        
        @media (max-width: 480px) {
            .edit-modal .modal-header,
            .edit-modal .modal-footer {
                padding: 0.75rem 1rem;
            }
            
            .form-label {
                font-size: 0.75rem;
            }
            
            .form-input, .form-select {
                font-size: 0.75rem;
                padding: 0.5rem 0.6rem;
            }
        }

        /* Modal styling */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
        }
        
        .modal-dialog {
            position: relative;
            margin: 2rem auto;
            max-width: 500px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .modal-header {
            padding: 1.5rem 1.5rem 1rem 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: none;
        }
        
                 .modal-header h5 {
             margin: 0;
             font-size: 1.25rem;
             color: #374151;
             font-weight: 600;
             display: flex;
             align-items: center;
             gap: 0.5rem;
         }
        
        .modal-body {
            padding: 1rem 1.5rem 1.5rem 1.5rem;
        }
        
        .modal-footer {
            padding: 1rem 1.5rem 1.5rem 1.5rem;
            display: flex;
            justify-content: flex-end;
            align-items: center;
            gap: 0.75rem;
        }
        
        .close {
            background: #f3f4f6;
            border: none;
            border-radius: 8px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            cursor: pointer;
            color: #6b7280;
            transition: all 0.2s ease;
        }
        
        .close:hover {
            background: #e5e7eb;
            color: #374151;
        }
        
        .upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 12px;
            padding: 2.5rem 1.5rem;
            text-align: center;
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .upload-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, transparent 0%, rgba(10, 126, 164, 0.05) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .upload-area:hover::before,
        .upload-area.dragover::before {
            opacity: 1;
        }
        
        .upload-area:hover,
        .upload-area.dragover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px -8px rgba(10, 126, 164, 0.3);
        }
        
        .upload-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto 1rem;
            background: linear-gradient(135deg, var(--primary-color), #0891b2);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(10, 126, 164, 0.3);
        }
        
        .upload-icon i {
            color: white;
            font-size: 28px;
        }
        
                 .upload-area h5 {
             color: #374151;
             margin-bottom: 0.5rem;
             font-weight: 600;
             font-size: 1.125rem;
         }
        
        .upload-area p {
            color: #6b7280;
            font-size: 0.875rem;
            margin-bottom: 0.75rem;
        }
        
                 .csv-format-info {
             display: flex;
             justify-content: center;
             padding: 1rem 1.5rem 0.5rem 1.5rem;
         }
         
         .format-badge {
             display: inline-flex;
             flex-direction: column;
             align-items: center;
             gap: 0.3rem;
             padding: 0.6rem 1rem;
             background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
             color: #475569;
             border: 1px solid #bae6fd;
             border-radius: 12px;
             font-size: 0.7rem;
             font-weight: 500;
             box-shadow: 0 2px 4px rgba(14, 165, 233, 0.1);
             text-align: center;
         }
         
         .format-badge-header {
             display: flex;
             align-items: center;
             gap: 0.4rem;
             color: #64748b;
         }
         
         .format-badge i {
             color: #0891b2;
             font-size: 0.7rem;
         }
         
         .format-badge .format-text {
             font-family: 'Courier New', monospace;
             font-size: 0.65rem;
             font-weight: 600;
             color: #334155;
             word-break: break-all;
             line-height: 1.2;
         }
        
                 .file-selected {
             margin-top: 1.5rem;
             padding: 1rem;
             background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
             border: 1px solid #10b981;
             border-radius: 8px;
             display: flex;
             align-items: center;
             gap: 0.75rem;
         }
         
         .file-selected i {
             color: #059669;
             font-size: 1.25rem;
         }
         
         .filename {
             color: #065f46;
             font-weight: 500;
             font-size: 0.875rem;
             white-space: nowrap !important;
             overflow: hidden;
             text-overflow: ellipsis;
             flex: 1;
             min-width: 0;
         }
        
        /* DataTables styling - move search to same line as length selector */
        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter {
            float: left;
            margin-bottom: 1rem;
        }
        
        .dataTables_wrapper .dataTables_filter {
            float: right;
        }
        
        .dataTables_wrapper .dataTables_length select,
        .dataTables_wrapper .dataTables_filter input {
            padding: 0.375rem 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
            margin-left: 0.5rem;
        }
        
        .dataTables_wrapper .dataTables_filter input {
            margin-left: 0.5rem;
            width: 250px;
        }
        
        .dataTables_wrapper .dataTables_length label,
        .dataTables_wrapper .dataTables_filter label {
            font-weight: 500;
            color: #374151;
            margin-bottom: 0;
            display: flex;
            align-items: center;
        }
        
        /* Ensure proper spacing and alignment */
        .dataTables_wrapper {
            margin-top: 0;
        }
        
        .dataTables_wrapper .dataTables_length,
        .dataTables_wrapper .dataTables_filter,
        .dataTables_wrapper .dataTables_info,
        .dataTables_wrapper .dataTables_paginate {
            padding-top: 0.75rem;
        }

        /* DataTables pagination styling */
        .dataTables_wrapper .dataTables_paginate {
            text-align: center !important;
            float: none !important;
            margin-top: 1rem;
            width: 100% !important;
            display: block !important;
        }
        
        .dataTables_wrapper .dataTables_paginate .pagination {
            justify-content: center !important;
            margin: 0 auto !important;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button {
            display: inline-flex !important;
            align-items: center;
            justify-content: center;
            min-width: 40px;
            height: 40px;
            margin: 0 2px;
            padding: 8px 12px;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            background: white;
            color: #374151;
            text-decoration: none;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            box-sizing: border-box;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
            background: #f9fafb;
            border-color: #d1d5db;
            color: #374151;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.current {
            background: var(--primary-color);
            border-color: var(--primary-color);
            color: white;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
            background: #0891b2;
            border-color: #0891b2;
            color: white;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.disabled {
            background: #f9fafb;
            border-color: #f3f4f6;
            color: #d1d5db;
            cursor: not-allowed;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.disabled:hover {
            background: #f9fafb;
            border-color: #f3f4f6;
            color: #d1d5db;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button.previous,
        .dataTables_wrapper .dataTables_paginate .paginate_button.next {
            font-weight: 600;
            padding: 8px 16px;
        }

        /* Align pagination with bottom info */
        .dataTables_wrapper .row:last-child {
            display: block !important;
            margin-top: 1rem;
            text-align: center !important;
        }

        .dataTables_wrapper .dataTables_info {
            float: left !important;
            margin: 0;
            padding-top: 0.75rem;
            text-align: left !important;
        }

        .dataTables_wrapper .dataTables_paginate {
            float: none !important;
            margin: 0 auto !important;
            text-align: center !important;
            clear: both;
            padding-top: 1rem;
            display: block !important;
            width: 100% !important;
        }

        /* Override DataTables specific pagination classes */
        #zones-table_paginate {
            text-align: center !important;
            margin: 0 auto !important;
            display: block !important;
            width: 100% !important;
        }

        .dataTables_wrapper div.dataTables_paginate {
            text-align: center !important;
            margin: 0 auto !important;
        }

        /* Sortable column headers cursor */
        .dataTables_wrapper table thead th.sorting,
        .dataTables_wrapper table thead th.sorting_asc,
        .dataTables_wrapper table thead th.sorting_desc {
            cursor: pointer !important;
        }

        .dataTables_wrapper table thead th.sorting:hover,
        .dataTables_wrapper table thead th.sorting_asc:hover,
        .dataTables_wrapper table thead th.sorting_desc:hover {
            background-color: #f8fafc !important;
        }

        /* Remove email link styling since emails are now plain text */
        .email-link {
            display: none;
        }
        
        /* Enhanced Validation Message Styling */
        .validation-message {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.75rem;
            background: linear-gradient(135deg, #dbeafe 0%, #eff6ff 100%);
            color: #0a7ea4;
            border: 1px solid #60a5fa;
            border-radius: 8px;
            padding: 0.75rem 1rem;
            margin-top: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(10, 126, 164, 0.15);
            animation: validationBlink 0.6s ease-in-out;
            position: relative;
            overflow: hidden;
            text-align: center;
        }
        
        .validation-message::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
            animation: validationShimmer 2s infinite;
        }
        
        .validation-message i {
            color: #0a7ea4;
            font-size: 1rem;
            animation: validationPulse 1.5s infinite;
            flex-shrink: 0;
        }
        
        .validation-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 0.25rem;
            text-align: center;
        }
        
        .validation-text {
            line-height: 1.4;
            font-weight: 600;
            color: #0a7ea4;
        }
        
        .validation-zone-name {
            font-size: 0.8rem;
            opacity: 0.85;
            font-weight: 500;
            font-style: italic;
            color: #0369a1;
        }
        
        /* Animations */
        @keyframes validationBlink {
            0% {
                opacity: 0;
                transform: translateY(-10px);
            }
            50% {
                opacity: 1;
                transform: translateY(0);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes validationPulse {
            0% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.7;
                transform: scale(1.1);
            }
            100% {
                opacity: 1;
                transform: scale(1);
            }
        }
        
        @keyframes validationShimmer {
            0% {
                left: -100%;
            }
            100% {
                left: 100%;
            }
        }
        
        /* Fix dropdown arrow styling */
        .form-select {
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.5rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-right: 2.5rem;
        }
        
        /* Remove any existing arrow styles */
        select::-ms-expand {
            display: none;
        }
        
        /* Dark mode support for validation messages */
        @media (prefers-color-scheme: dark) {
            .validation-message {
                background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
                border-color: #3b82f6;
                color: #93c5fd;
            }
            
            .validation-message i {
                color: #93c5fd;
            }
            
            .validation-text {
                color: #93c5fd;
            }
            
            .validation-zone-name {
                color: #dbeafe;
            }
        }
        
        
    </style>
</head>
<body>
    <?php 
    include_file('includes/header.php');
    include_file('includes/sidebar.php');
    ?>
    
    <div class="content">
                <!-- Statistics Grid -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="icon bg-blue">
                    <i class="fas fa-map-marker-alt"></i>
                </div>
                <div class="stat-content">
                    <p class="title">Total de Zonas</p>
                    <p class="value"><?php echo number_format(count($huntingZones)); ?></p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="icon bg-green">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-content">
                    <p class="title">Zonas Registadas</p>
                    <p class="value"><?php 
                        $registeredZones = array_filter($huntingZones, function($zone) { 
                            return isset($zone['status']) && $zone['status'] === 'registered'; 
                        });
                        echo number_format(count($registeredZones)); 
                    ?></p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="icon bg-purple">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <div class="stat-content">
                    <p class="title">Quota Total</p>
                    <p class="value"><?php echo number_format(array_sum(array_column($huntingZones, 'quotaZona'))); ?></p>
                </div>
            </div>
            
            <div class="stat-card">
                <div class="icon bg-orange">
                    <i class="fas fa-building"></i>
                </div>
                <div class="stat-content">
                    <p class="title">Entidades</p>
                    <p class="value"><?php echo number_format(count(array_unique(array_column($huntingZones, 'email')))); ?></p>
                </div>
            </div>
        </div>

        <!-- Hunting Zones Table -->
        <div class="zones-table-card">
            <div class="zones-table-header">
                <h2>
                    <i class="fas fa-list-alt" style="color: var(--primary-color); margin-right: 0.5rem;"></i>
                    Lista de Zonas de Caça
                </h2>
                <div class="header-actions">
                    <?php if (isSuperAdmin()): ?>
                        <a href="check_email_verification.php" class="btn btn-info me-2">
                            <i class="fas fa-search"></i>
                            Verificar Email
                        </a>
                        <a href="diagnose_user_state.php" class="btn btn-warning me-2">
                            <i class="fas fa-stethoscope"></i>
                            Diagnóstico
                        </a>
                        <a href="migrate_user_generic.php" class="btn btn-danger me-2">
                            <i class="fas fa-sync-alt"></i>
                            Migração
                        </a>
                    <?php endif; ?>
                    <button type="button" class="btn btn-success me-2" onclick="openCreateModal()">
                        <i class="fas fa-plus"></i>
                        Criar Zona de Caça
                    </button>
                    <button type="button" class="btn btn-primary" onclick="showUploadModal()">
                        <i class="fas fa-upload"></i>
                        Importar Zonas de Caça
                    </button>
                </div>
            </div>
            <div class="zones-table-body">
                <table id="zones-table" class="table">
                        <thead>
                            <tr>
                                <th class="text-center">Zona</th>
                                <th>Nome</th>
                                <th class="text-center">NIF</th>
                                <th class="text-center">Email</th>

                                <th class="text-center">Quota</th>
                                <th class="text-center">Selo Min</th>
                                <th class="text-center">Selo Max</th>
                                <th class="text-center">Status</th>
                                <th class="text-center">Registado Por</th>
                                <th class="text-center"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($huntingZones as $zone): ?>
                            <?php $isTestZoneRow = isSuperAdmin() && isTestZone($zone['nifEntidade']); ?>
                            <tr<?php echo $isTestZoneRow ? ' class="test-zone-row"' : ''; ?>>
                                <td class="text-center"><strong><?php echo htmlspecialchars($zone['zona']); ?></strong></td>
                                <td>
                                    <?php echo htmlspecialchars($zone['nomeZona']); ?>
                                    <?php if ($isTestZoneRow): ?>
                                        <span class="test-zone-label">ZONA TESTE</span>
                                    <?php endif; ?>
                                </td>
                                <td class="text-center"><?php echo htmlspecialchars($zone['nifEntidade']); ?></td>
                                <td class="text-center">
                                    <?php echo htmlspecialchars($zone['email']); ?>
                                    <?php if (isSuperAdmin() && $zone['email'] !== 'N/A' && filter_var($zone['email'], FILTER_VALIDATE_EMAIL)): ?>
                                        <a href="check_email_verification.php?email=<?php echo urlencode($zone['email']); ?>" 
                                           class="text-muted ms-2" 
                                           title="Verificar status de email (Super Admin)"
                                           style="text-decoration: none; font-size: 12px;">
                                            <i class="fas fa-search"></i>
                                        </a>
                                    <?php endif; ?>
                                </td>
                                <td class="text-center"><?php echo number_format($zone['quotaZona']); ?></td>
                                <td class="text-center"><?php echo number_format($zone['minSelo']); ?></td>
                                <td class="text-center"><?php echo number_format($zone['maxSelo']); ?></td>
                                <td class="text-center">
                                    <span class="zone-status <?php echo str_replace(' ', '-', $zone['status']); ?>">
                                        <?php 
                                        switch($zone['status']) {
                                            case 'registered':
                                                echo 'Registada';
                                                break;
                                            case 'not registered':
                                                echo 'Não Registada';
                                                break;
                                            case 'inactive':
                                                echo 'Inativa';
                                                break;
                                            default:
                                                echo 'Não Registada';
                                        }
                                        ?>
                                    </span>
                                </td>
                                <td class="text-center">
                                    <?php 
                                    // Show registeredBy if status is registered and field exists and is not null/empty
                                    if ($zone['status'] === 'registered' && isset($zone['registeredBy']) && !empty($zone['registeredBy'])) {
                                        $registeredBy = $zone['registeredBy'];
                                        $isSystemUser = strpos($registeredBy, 'webadmin') !== false || strpos($registeredBy, 'system') !== false;
                                        $displayText = $isSystemUser ? 'WebAdmin' : $registeredBy;
                                        ?>
                                        <span class="registered-badge <?php echo $isSystemUser ? 'system-badge' : 'mobile-badge'; ?>">
                                            <?php echo htmlspecialchars($displayText); ?>
                                        </span>
                                        <?php
                                    } else {
                                        echo '<span class="text-muted">—</span>';
                                    }
                                    ?>
                                </td>
                                <td class="text-center">
                                    <button type="button" class="btn btn-sm btn-primary me-2" onclick="editZone('<?php echo $zone['id']; ?>', <?php echo htmlspecialchars(json_encode($zone)); ?>)">
                                        <i class="fas fa-edit"></i>
                                        Editar
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger" onclick="deleteZone('<?php echo $zone['id']; ?>', '<?php echo htmlspecialchars($zone['nomeZona']); ?>')">
                                        <i class="fas fa-trash"></i>
                                        Eliminar
                                    </button>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
        </div>
    </div>

    <!-- Upload Modal -->
    <div class="modal" id="uploadModal">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5>
                        <i class="fas fa-upload" style="color: var(--primary-color);"></i>
                        Importar Zonas de Caça
                    </h5>
                    <button type="button" class="close" onclick="closeUploadModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <form method="POST" enctype="multipart/form-data" id="uploadForm">
                    <input type="hidden" name="action" value="upload">
                    <div class="modal-body">
                        <div class="upload-area" id="uploadArea" onclick="document.getElementById('csv_file').click()">
                            <div class="upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <h5>Arraste e solte o ficheiro CSV</h5>
                            <p>ou clique para selecionar</p>
                            <input type="file" class="d-none" id="csv_file" name="csv_file" accept=".csv" required>
                        </div>
                        <div id="fileSelected" class="file-selected" style="display: none;">
                            <i class="fas fa-file-csv"></i>
                            <span id="fileName" class="filename"></span>
                        </div>
                        <div class="csv-format-info">
                            <div class="format-badge">
                                <div class="format-badge-header">
                                    <i class="fas fa-info-circle"></i>
                                    <span>Formato CSV:</span>
                                </div>
                                <span class="format-text">Zona;NIFEntidade;NomeZona;Email;QuotaZona;MinSelo;MaxSelo</span>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="closeUploadModal()">
                            <i class="fas fa-times"></i>
                            Cancelar
                        </button>
                        <button type="submit" class="btn btn-primary" id="uploadBtn" disabled>
                            <i class="fas fa-upload"></i>
                            Importar
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Import Progress Modal -->
    <div class="modal" id="progressModal" style="display: none;">
        <div class="modal-dialog" style="max-width: 600px;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5>
                        <i class="fas fa-upload" style="color: var(--primary-color);"></i>
                        Importando Zonas de Caça
                    </h5>
                    <div class="overall-progress">
                        <div class="overall-progress-bar">
                            <div class="overall-progress-fill" id="overallProgress"></div>
                        </div>
                        <span class="overall-progress-text" id="overallProgressText">0% Concluído</span>
                    </div>
                </div>
                <div class="modal-body">
                    <div class="progress-container">
                        <div class="progress-step">
                            <div class="step-icon">
                                <i class="fas fa-file-upload"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-title">
                                    A carregar ficheiro...
                                    <span class="step-progress" id="progress1"></span>
                                </div>
                                <div class="step-status" id="step1">Aguardando...</div>
                                <div class="step-duration" id="duration1"></div>
                            </div>
                        </div>
                        <div class="progress-step">
                            <div class="step-icon">
                                <i class="fas fa-cog"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-title">
                                    A processar dados CSV...
                                    <span class="step-progress" id="progress2"></span>
                                </div>
                                <div class="step-status" id="step2">Aguardando...</div>
                                <div class="step-duration" id="duration2"></div>
                            </div>
                        </div>
                        <div class="progress-step">
                            <div class="step-icon">
                                <i class="fas fa-database"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-title">
                                    A guardar na base de dados...
                                    <span class="step-progress" id="progress3"></span>
                                </div>
                                <div class="step-status" id="step3">Aguardando...</div>
                                <div class="step-duration" id="duration3"></div>
                            </div>
                        </div>
                        <div class="progress-step">
                            <div class="step-icon">
                                <i class="fas fa-check"></i>
                            </div>
                            <div class="step-content">
                                <div class="step-title">
                                    Importação concluída
                                    <span class="step-progress" id="progress4"></span>
                                </div>
                                <div class="step-status" id="step4">Aguardando...</div>
                                <div class="step-duration" id="duration4"></div>
                            </div>
                        </div>
                    </div>
                    <div class="import-log" id="importLog">
                        <div class="log-header">Log de Importação:</div>
                        <div class="log-content" id="logContent">
                            Iniciando importação...<br>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" id="progressCloseBtn" onclick="closeProgressModal()" style="display: none;">
                        <i class="fas fa-check"></i>
                        Concluído
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Conflict Resolution Modal -->
    <div class="modal" id="conflictModal" style="display: none;">
        <div class="modal-dialog" style="max-width: 800px;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5>
                        <i class="fas fa-exclamation-triangle" style="color: #f59e0b;"></i>
                        Conflitos Encontrados na Importação
                    </h5>
                    <button type="button" class="close" onclick="closeConflictModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="conflict-summary">
                        <div class="summary-grid">
                            <div class="summary-item">
                                <div class="summary-icon bg-green">
                                    <i class="fas fa-plus"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number" id="newZonesCount">0</div>
                                    <div class="summary-label">Novas zonas</div>
                                </div>
                            </div>
                            <div class="summary-item">
                                <div class="summary-icon bg-orange">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number" id="conflictZonesCount">0</div>
                                    <div class="summary-label">Com conflitos</div>
                                </div>
                            </div>
                            <div class="summary-item">
                                <div class="summary-icon bg-blue">
                                    <i class="fas fa-check"></i>
                                </div>
                                <div class="summary-content">
                                    <div class="summary-number" id="identicalZonesCount">0</div>
                                    <div class="summary-label">Inalteradas</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="conflict-explanation">
                        <p><strong>Algumas zonas já existem na base de dados com dados diferentes.</strong></p>
                        <p>Escolha como proceder para cada zona em conflito:</p>
                    </div>
                    
                    <div class="conflicts-list" id="conflictsList">
                        <!-- Conflicts will be populated here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeConflictModal()">
                        <i class="fas fa-times"></i>
                        Cancelar
                    </button>
                    <button type="button" class="btn btn-outline-primary" onclick="selectAllConflictActions('skip')">
                        <i class="fas fa-ban"></i>
                        Ignorar Todos
                    </button>
                    <button type="button" class="btn btn-outline-primary" onclick="selectAllConflictActions('update')">
                        <i class="fas fa-sync"></i>
                        Atualizar Todos
                    </button>
                    <button type="button" class="btn btn-primary" onclick="processConflicts()">
                        <i class="fas fa-check"></i>
                        Processar Importação
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Zone Modal -->
    <div class="modal" id="editModal" style="display: none;">
        <div class="modal-dialog" style="max-width: 600px; height: 85vh; margin: 2rem auto; display: flex;">
            <div class="modal-content edit-modal" style="display: flex; flex-direction: column; height: 100%;">
                <div class="modal-header">
                    <h5>
                        <i class="fas fa-edit" style="color: var(--primary-color);"></i>
                        Editar Zona de Caça
                    </h5>
                    <button type="button" class="close" onclick="closeEditModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body" style="flex: 1; overflow-y: auto; max-height: calc(85vh - 120px);">
                    <!-- Error Display Area -->
                    <div id="editErrorAlert" class="alert alert-danger" style="display: none; margin-bottom: 1rem;">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span id="editErrorMessage"></span>
                    </div>
                    
                    <form method="POST" id="editForm">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="zone_id" id="editZoneId">
                        
                        <!-- Zone Info Section -->
                        <div class="form-section">
                            <div class="section-header">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>Informações da Zona</span>
                            </div>
                            <div class="form-field">
                                <label for="editZoneIdDisplay" class="form-label">
                                    <i class="fas fa-key"></i>
                                    ID da Zona (Firebase)
                                </label>
                                <input type="text" class="form-input" id="editZoneIdDisplay" readonly tabindex="-1" style="background-color: #f8fafc; color: #6b7280; cursor: not-allowed; user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; pointer-events: none;">
                            </div>
                            <div class="form-grid two-col">
                                <div class="form-field">
                                    <label for="editZona" class="form-label">
                                        <i class="fas fa-hashtag"></i>
                                        Zona
                                    </label>
                                    <input type="number" class="form-input" id="editZona" name="zona" required>
                                </div>
                                <div class="form-field">
                                    <label for="editNifEntidade" class="form-label">
                                        <i class="fas fa-id-card"></i>
                                        NIF Entidade
                                    </label>
                                    <input type="text" class="form-input" id="editNifEntidade" name="nifEntidade" required pattern="[0-9]*" inputmode="numeric" readonly tabindex="-1" style="background-color: #f8fafc; color: #6b7280; cursor: not-allowed; user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; pointer-events: none;">
                                </div>
                            </div>
                            <!-- Full width validation messages -->
                            <div class="validation-message" id="editZonaValidation" style="display: none;">
                                <i class="fas fa-exclamation-triangle"></i>
                                <div class="validation-content">
                                    <span class="validation-text"></span>
                                    <span class="validation-zone-name"></span>
                                </div>
                            </div>
                            <div class="form-field">
                                <label for="editNomeZona" class="form-label">
                                    <i class="fas fa-tag"></i>
                                    Nome da Zona
                                </label>
                                <input type="text" class="form-input" id="editNomeZona" name="nomeZona" required>
                            </div>
                            <div class="form-field">
                                <label for="editEmail" class="form-label">
                                    <i class="fas fa-envelope"></i>
                                    Email
                                </label>
                                <input type="email" class="form-input" id="editEmail" name="email" required>
                                <small class="form-help" id="editEmailValidation" style="display: none;"></small>
                            </div>
                        </div>

                        <!-- Quotas Section -->
                        <div class="form-section">
                            <div class="section-header">
                                <i class="fas fa-chart-bar"></i>
                                <span>Quotas e Selos</span>
                            </div>
                            <div class="form-grid three-col">
                                <div class="form-field">
                                    <label for="editQuotaZona" class="form-label">
                                        <i class="fas fa-calculator"></i>
                                        Quota
                                    </label>
                                    <input type="number" class="form-input" id="editQuotaZona" name="quotaZona" required>
                                </div>
                                <div class="form-field">
                                    <label for="editMinSelo" class="form-label">
                                        <i class="fas fa-arrow-down"></i>
                                        Selo Min
                                    </label>
                                    <input type="number" class="form-input" id="editMinSelo" name="minSelo" required>
                                </div>
                                <div class="form-field">
                                    <label for="editMaxSelo" class="form-label">
                                        <i class="fas fa-arrow-up"></i>
                                        Selo Max
                                    </label>
                                    <input type="number" class="form-input" id="editMaxSelo" name="maxSelo" required>
                                </div>
                            </div>
                        </div>

                        <!-- Status Section -->
                        <div class="form-section">
                            <div class="section-header">
                                <i class="fas fa-toggle-on"></i>
                                <span>Estado</span>
                            </div>
                            <div class="form-field">
                                <label for="editStatus" class="form-label">
                                    <i class="fas fa-flag"></i>
                                    Status da Zona
                                </label>
                                <select class="form-select" id="editStatus" name="status" required onchange="toggleRegisteredByField()">
                                    <option value="registered">✓ Registada</option>
                                    <option value="not registered">⚠ Não Registada</option>
                                    <option value="inactive">✗ Inativa</option>
                                </select>
                            </div>
                            <div id="registeredByField" style="display: none;">
                                <div class="form-grid two-col">
                                    <div class="form-field">
                                        <label for="editRegisteredFirstName" class="form-label">
                                            <i class="fas fa-user"></i>
                                            Primeiro Nome
                                        </label>
                                        <input type="text" class="form-input" id="editRegisteredFirstName" name="registeredFirstName" placeholder="Nome">
                                    </div>
                                    <div class="form-field">
                                        <label for="editRegisteredLastName" class="form-label">
                                            <i class="fas fa-user"></i>
                                            Último Nome
                                        </label>
                                        <input type="text" class="form-input" id="editRegisteredLastName" name="registeredLastName" placeholder="Apelido">
                                    </div>
                                </div>
                                <div class="form-field">
                                    <label for="editRegisteredBy" class="form-label">
                                        <i class="fas fa-envelope"></i>
                                        Email de quem registou
                                    </label>
                                    <input type="email" class="form-input" id="editRegisteredBy" name="registeredBy" placeholder="<EMAIL>">
                                    <small class="form-help" id="editRegisteredByValidation" style="display: none;"></small>
                                    <small class="form-help">Email do utilizador que registou esta zona</small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeEditModal()">
                        <i class="fas fa-times"></i>
                        Cancelar
                    </button>
                    <button type="button" class="btn btn-primary" id="editSaveBtn" onclick="validateAndSubmitEditForm();">
                        <i class="fas fa-save"></i>
                        Guardar Alterações
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Zone Modal -->
    <div class="modal" id="createModal" style="display: none;">
        <div class="modal-dialog" style="max-width: 600px; height: 85vh; margin: 2rem auto; display: flex;">
            <div class="modal-content edit-modal" style="display: flex; flex-direction: column; height: 100%;">
                <div class="modal-header">
                    <h5>
                        <i class="fas fa-plus" style="color: var(--primary-color);"></i>
                        Criar Nova Zona de Caça
                    </h5>
                    <button type="button" class="close" onclick="closeCreateModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body" style="flex: 1; overflow-y: auto; max-height: calc(85vh - 120px);">
                    <!-- Error Display Area -->
                    <div id="createErrorAlert" class="alert alert-danger" style="display: none; margin-bottom: 1rem;">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span id="createErrorMessage"></span>
                    </div>
                    
                    <form method="POST" id="createForm">
                        <input type="hidden" name="action" value="create">
                        
                        <!-- Zone Info Section -->
                        <div class="form-section">
                            <div class="section-header">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>Informações da Zona</span>
                            </div>
                            <div class="form-grid two-col">
                                <div class="form-field">
                                    <label for="createZona" class="form-label">
                                        <i class="fas fa-hashtag"></i>
                                        Zona
                                    </label>
                                    <input type="number" class="form-input" id="createZona" name="zona" required>
                                </div>
                                <div class="form-field">
                                    <label for="createNifEntidade" class="form-label">
                                        <i class="fas fa-id-card"></i>
                                        NIF Entidade
                                    </label>
                                    <input type="text" class="form-input" id="createNifEntidade" name="nifEntidade" required pattern="[0-9]*" inputmode="numeric">
                                </div>
                            </div>
                            <!-- Full width validation messages -->
                            <div class="validation-message" id="zonaValidation" style="display: none;">
                                <i class="fas fa-exclamation-triangle"></i>
                                <div class="validation-content">
                                    <span class="validation-text"></span>
                                    <span class="validation-zone-name"></span>
                                </div>
                            </div>
                            <div class="form-field">
                                <label for="createNomeZona" class="form-label">
                                    <i class="fas fa-tag"></i>
                                    Nome da Zona
                                </label>
                                <input type="text" class="form-input" id="createNomeZona" name="nomeZona" required>
                            </div>
                            <div class="form-field">
                                <label for="createEmail" class="form-label">
                                    <i class="fas fa-envelope"></i>
                                    Email
                                </label>
                                <input type="email" class="form-input" id="createEmail" name="email" required>
                                <small class="form-help" id="createEmailValidation" style="display: none;"></small>
                            </div>
                        </div>

                        <!-- Quotas Section -->
                        <div class="form-section">
                            <div class="section-header">
                                <i class="fas fa-chart-bar"></i>
                                <span>Quotas e Selos</span>
                            </div>
                            <div class="form-grid three-col">
                                <div class="form-field">
                                    <label for="createQuotaZona" class="form-label">
                                        <i class="fas fa-calculator"></i>
                                        Quota
                                    </label>
                                    <input type="number" class="form-input" id="createQuotaZona" name="quotaZona" required>
                                </div>
                                <div class="form-field">
                                    <label for="createMinSelo" class="form-label">
                                        <i class="fas fa-arrow-down"></i>
                                        Selo Min
                                    </label>
                                    <input type="number" class="form-input" id="createMinSelo" name="minSelo" required>
                                </div>
                                <div class="form-field">
                                    <label for="createMaxSelo" class="form-label">
                                        <i class="fas fa-arrow-up"></i>
                                        Selo Max
                                    </label>
                                    <input type="number" class="form-input" id="createMaxSelo" name="maxSelo" required>
                                </div>
                            </div>
                        </div>

                        <!-- Status Section -->
                        <div class="form-section">
                            <div class="section-header">
                                <i class="fas fa-toggle-on"></i>
                                <span>Estado</span>
                            </div>
                            <div class="form-field">
                                <label for="createStatus" class="form-label">
                                    <i class="fas fa-flag"></i>
                                    Status da Zona
                                </label>
                                <select class="form-select" id="createStatus" name="status" required onchange="toggleCreateRegisteredByField()">
                                    <option value="not registered">⚠ Não Registada</option>
                                    <option value="registered">✓ Registada</option>
                                    <option value="inactive">✗ Inativa</option>
                                </select>
                            </div>
                            <div id="createRegisteredByField" style="display: none;">
                                <div class="form-grid two-col">
                                    <div class="form-field">
                                        <label for="createRegisteredFirstName" class="form-label">
                                            <i class="fas fa-user"></i>
                                            Primeiro Nome
                                        </label>
                                        <input type="text" class="form-input" id="createRegisteredFirstName" name="registeredFirstName" placeholder="Nome">
                                    </div>
                                    <div class="form-field">
                                        <label for="createRegisteredLastName" class="form-label">
                                            <i class="fas fa-user"></i>
                                            Último Nome
                                        </label>
                                        <input type="text" class="form-input" id="createRegisteredLastName" name="registeredLastName" placeholder="Apelido">
                                    </div>
                                </div>
                                <div class="form-field">
                                    <label for="createRegisteredBy" class="form-label">
                                        <i class="fas fa-envelope"></i>
                                        Email de quem registou
                                    </label>
                                    <input type="email" class="form-input" id="createRegisteredBy" name="registeredBy" placeholder="<EMAIL>">
                                    <small class="form-help" id="createRegisteredByValidation" style="display: none;"></small>
                                    <small class="form-help">Email do utilizador que registou esta zona</small>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="closeCreateModal()">
                        <i class="fas fa-times"></i>
                        Cancelar
                    </button>
                    <button type="button" class="btn btn-success" id="createSaveBtn" onclick="validateAndSubmitCreateForm();">
                        <i class="fas fa-plus"></i>
                        Criar Zona
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal" id="deleteModal" style="display: none;">
        <div class="modal-dialog" style="max-width: 500px;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5>
                        <i class="fas fa-exclamation-triangle" style="color: #dc2626;"></i>
                        Confirmar Eliminação
                    </h5>
                    <button type="button" class="close" onclick="closeDeleteModal()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="delete-warning">
                        <div class="warning-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="warning-content">
                            <h6>Tem a certeza que deseja eliminar a zona?</h6>
                            <p>Esta ação irá eliminar permanentemente a zona:</p>
                            <p class="zone-name"><strong id="deleteZoneName">---</strong></p>
                            <div class="warning-note">
                                <i class="fas fa-info-circle"></i>
                                Esta ação não pode ser desfeita.
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <input type="hidden" id="deleteZoneId" value="">
                    <button type="button" class="btn btn-secondary" onclick="closeDeleteModal()">
                        <i class="fas fa-times"></i>
                        Cancelar
                    </button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn" onclick="confirmDeleteZone()">
                        <i class="fas fa-trash"></i>
                        Eliminar Zona
                    </button>
                </div>
            </div>
        </div>
    </div>

    <style>
        .conflict-summary {
            margin-bottom: 1.5rem;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
        }
        
        .summary-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1rem;
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
        }
        
        .summary-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        
        .summary-number {
            font-size: 1.5rem;
            font-weight: 600;
            color: #374151;
        }
        
        .summary-label {
            font-size: 0.875rem;
            color: #6b7280;
        }
        
        .conflict-explanation {
            margin-bottom: 1.5rem;
            padding: 1rem;
            background: #fef3cd;
            border: 1px solid #fbbf24;
            border-radius: 8px;
            text-align: center;
        }
        
        .conflict-explanation p {
            margin: 0 0 0.5rem 0;
            color: #92400e;
        }
        
        .conflicts-list {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .conflict-item {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            margin-bottom: 1rem;
            background: white;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .conflict-item.action-skip {
            border-left: 4px solid #f59e0b;
            background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
        }
        
        .conflict-item.action-update {
            border-left: 4px solid #059669;
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
        }
        
        .conflict-item::before {
            content: '';
            position: absolute;
            top: 0.5rem;
            right: 0.5rem;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            display: none;
        }
        
        .conflict-item.action-skip::before {
            display: block;
            background: #f59e0b;
            content: '🚫';
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
        }
        
        .conflict-item.action-update::before {
            display: block;
            background: #059669;
            content: '🔄';
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
        }
        
        .conflict-header {
            background: #f9fafb;
            padding: 1rem;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .conflict-zone-info {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            font-weight: 600;
            color: #374151;
        }
        
        .conflict-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .conflict-action {
            padding: 0.375rem 0.75rem;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: white;
            cursor: pointer;
            font-size: 0.875rem;
            transition: all 0.2s;
        }
        
        .conflict-action.selected {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        .conflict-action:hover:not(.selected) {
            background: #f3f4f6;
        }
        
        .conflict-details {
            padding: 1rem;
        }
        
        .changes-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .change-column {
            background: #f8fafc;
            border-radius: 6px;
            padding: 0.75rem;
        }
        
        .change-column h6 {
            margin: 0 0 0.5rem 0;
            font-size: 0.875rem;
            font-weight: 600;
            color: #374151;
        }
        
        .change-column.existing h6 {
            color: #dc2626;
        }
        
        .change-column.new h6 {
            color: #059669;
        }
        
        .change-field {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.25rem;
            font-size: 0.8rem;
        }
        
        .change-field:last-child {
            margin-bottom: 0;
        }
        
        .field-label {
            font-weight: 500;
            color: #6b7280;
        }
        
        .field-value {
            color: #374151;
            font-weight: 500;
        }
        
        .progress-container {
            margin-bottom: 2rem;
        }
        
        .progress-step {
            display: flex;
            align-items: center;
            gap: 1rem;
            padding: 1rem;
            margin-bottom: 0.5rem;
            border-radius: 8px;
            background: #f8fafc;
            border-left: 4px solid #e2e8f0;
            position: relative;
            overflow: hidden;
        }
        
        .progress-step.active {
            background: #eff6ff;
            border-left-color: var(--primary-color);
        }
        
        .progress-step.active::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 0%;
            background: linear-gradient(90deg, transparent, rgba(10, 126, 164, 0.1), transparent);
            animation: loadingBar 2s ease-in-out infinite;
        }
        
        .progress-step.completed {
            background: #f0fdf4;
            border-left-color: #10b981;
        }
        
        .progress-step.completed::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
            background: linear-gradient(90deg, transparent, rgba(16, 185, 129, 0.1), transparent);
            animation: completedSweep 0.8s ease-out;
        }
        
        .progress-step.error {
            background: #fef2f2;
            border-left-color: #ef4444;
        }
        
        .step-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e2e8f0;
            color: #64748b;
            position: relative;
            z-index: 2;
        }
        
        .progress-step.active .step-icon {
            background: var(--primary-color);
            color: white;
            position: relative;
            animation: iconPulse 2s ease-in-out infinite;
        }
        
        .progress-step.active .step-icon::before {
            content: '';
            position: absolute;
            top: -3px;
            left: -3px;
            right: -3px;
            bottom: -3px;
            border-radius: 50%;
            background: var(--primary-color);
            opacity: 0.3;
            animation: ripple 2s ease-out infinite;
            z-index: -1;
        }
        
        .progress-step.completed .step-icon {
            background: #10b981;
            color: white;
            transform: scale(1.1);
            transition: transform 0.3s ease;
        }
        
        .progress-step.error .step-icon {
            background: #ef4444;
            color: white;
            animation: shake 0.5s ease-in-out;
        }
        
        @keyframes loadingBar {
            0% { width: 0%; }
            50% { width: 100%; }
            100% { width: 0%; }
        }
        
        @keyframes completedSweep {
            0% { width: 0%; }
            100% { width: 100%; }
        }
        
        @keyframes iconPulse {
            0%, 100% { 
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(10, 126, 164, 0.4);
            }
            50% { 
                transform: scale(1.05);
                box-shadow: 0 0 0 8px rgba(10, 126, 164, 0);
            }
        }
        
        @keyframes ripple {
            0% {
                transform: scale(1);
                opacity: 0.3;
            }
            100% {
                transform: scale(1.4);
                opacity: 0;
            }
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        
        .step-content {
            flex: 1;
            position: relative;
        }
        
        .step-title {
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.25rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .step-status {
            font-size: 0.875rem;
            color: #6b7280;
        }
        
        .step-progress {
            font-size: 0.75rem;
            color: var(--primary-color);
            font-weight: 600;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .progress-step.active .step-progress {
            opacity: 1;
        }
        
        .step-progress.processing {
            animation: processingDots 1.5s infinite;
        }
        
        @keyframes processingDots {
            0%, 20% { content: 'Processando'; }
            25%, 45% { content: 'Processando.'; }
            50%, 70% { content: 'Processando..'; }
            75%, 95% { content: 'Processando...'; }
        }
        
        .step-duration {
            font-size: 0.7rem;
            color: #9ca3af;
            margin-top: 0.25rem;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .progress-step.completed .step-duration,
        .progress-step.error .step-duration {
            opacity: 1;
        }
        
        .overall-progress {
            margin-top: 1rem;
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .overall-progress-bar {
            flex: 1;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .overall-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--primary-color), #60a5fa);
            width: 0%;
            transition: width 0.5s ease;
            position: relative;
        }
        
        .overall-progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background-image: linear-gradient(
                45deg,
                rgba(255, 255, 255, 0.2) 25%,
                transparent 25%,
                transparent 50%,
                rgba(255, 255, 255, 0.2) 50%,
                rgba(255, 255, 255, 0.2) 75%,
                transparent 75%,
                transparent
            );
            background-size: 20px 20px;
            animation: move 2s linear infinite;
        }
        
        @keyframes move {
            0% { background-position: 0 0; }
            100% { background-position: 20px 20px; }
        }
        
        .overall-progress-text {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--primary-color);
            min-width: 100px;
            text-align: right;
        }
        
        .import-log {
            background: #1f2937;
            border-radius: 8px;
            padding: 1rem;
            height: 180px;
            overflow-y: auto;
            overflow-x: hidden;
            border: 1px solid #374151;
            scroll-behavior: smooth;
            position: relative;
        }
        
        .import-log .log-content {
            font-family: 'Courier New', monospace;
            line-height: 1.5;
        }
        
        .import-log .log-content .log-section-divider {
            color: #fbbf24;
            font-weight: bold;
            margin: 0.5rem 0;
        }
        
        .import-log .log-content .log-success {
            color: #10b981;
        }
        
        .import-log .log-content .log-warning {
            color: #f59e0b;
        }
        
        .import-log .log-content .log-error {
            color: #ef4444;
        }
        
        .import-log .log-content .log-info {
            color: #60a5fa;
        }
        
        .import-log::-webkit-scrollbar {
            width: 8px;
        }
        
        .import-log::-webkit-scrollbar-track {
            background: #374151;
            border-radius: 4px;
        }
        
        .import-log::-webkit-scrollbar-thumb {
            background: #6b7280;
            border-radius: 4px;
        }
        
        .import-log::-webkit-scrollbar-thumb:hover {
            background: #9ca3af;
        }
        
        .log-header {
            color: #e5e7eb;
            font-weight: 600;
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }
        
        .log-content {
            color: #9ca3af;
            font-family: 'Courier New', monospace;
            font-size: 0.8rem;
            line-height: 1.4;
        }
        
        .import-result-highlight {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border-radius: 8px;
            padding: 1rem;
            margin: 0.75rem 0 2rem 0;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            border: 2px solid #065f46;
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
            animation: resultPulse 2s ease-in-out;
        }
        
        @keyframes resultPulse {
            0% { 
                transform: scale(0.95);
                box-shadow: 0 4px 12px rgba(16, 185, 129, 0.1);
            }
            50% { 
                transform: scale(1.02);
                box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
            }
            100% { 
                transform: scale(1);
                box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
            }
        }
        
        .result-icon {
            font-size: 1.5rem;
            background: rgba(255, 255, 255, 0.2);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }
        
        .result-content {
            flex: 1;
        }
        
        .result-title {
            color: white;
            font-weight: 700;
            font-size: 0.75rem;
            letter-spacing: 0.05em;
            margin-bottom: 0.25rem;
            text-transform: uppercase;
        }
        
        .result-message {
            color: #d1fae5;
            font-weight: 600;
            font-size: 0.85rem;
            font-family: system-ui, -apple-system, sans-serif;
            line-height: 1.3;
        }

        /* Delete Modal Styles */
        .delete-warning {
            text-align: center;
            padding: 2rem 1.5rem;
            background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
            border: 2px solid #fecaca;
            border-radius: 12px;
            margin: 0;
        }

        .warning-icon {
            width: 60px;
            height: 60px;
            background: #dc2626;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin: 0 auto 1.5rem auto;
        }

        .warning-content {
            text-align: center;
        }

        .warning-content h6 {
            margin: 0 0 1rem 0;
            font-size: 1.25rem;
            font-weight: 600;
            color: #374151;
        }

        .warning-content p {
            margin: 0 0 1rem 0;
            color: #6b7280;
            line-height: 1.6;
            font-size: 0.95rem;
        }

        .warning-content p:last-child {
            margin-bottom: 0;
        }

        .warning-content .zone-name {
            margin: 0.5rem 0 1rem 0 !important;
            font-size: 1.1rem;
            color: #374151 !important;
            font-weight: 600;
        }

        .warning-content .zone-name strong {
            color: #dc2626;
            font-weight: 700;
        }

        .warning-note {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1rem;
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid #fca5a5;
            border-radius: 8px;
            font-size: 0.875rem;
            color: #dc2626;
            font-weight: 500;
            margin-top: 0.5rem;
        }

        .warning-note i {
            color: #dc2626;
        }

        /* Delete Modal Centering */
        .modal#deleteModal[style*="block"] {
            display: flex !important;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .modal#deleteModal[style*="block"] .modal-dialog {
            margin: 0 !important;
            transform: none !important;
            position: static !important;
            top: auto !important;
        }

        /* Delete Modal Animation */
        .modal#deleteModal .modal-content {
            animation: deleteModalSlide 0.3s ease-out;
        }

        @keyframes deleteModalSlide {
            from {
                transform: scale(0.9);
                opacity: 0;
            }
            to {
                transform: scale(1);
                opacity: 1;
            }
        }

        /* Button Loading State for Delete */
        .btn#confirmDeleteBtn.loading {
            position: relative;
            color: transparent;
        }

        .btn#confirmDeleteBtn.loading::after {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            top: 50%;
            left: 50%;
            margin-left: -8px;
            margin-top: -8px;
            border: 2px solid transparent;
            border-top-color: #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Toast Notification Styles */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 999999;
            max-width: 400px;
            pointer-events: none;
        }

        .toast {
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            margin-bottom: 10px;
            padding: 16px;
            display: flex;
            align-items: center;
            gap: 12px;
            border-left: 4px solid;
            transform: translateX(400px);
            opacity: 0;
            transition: all 0.3s ease;
            min-width: 300px;
            pointer-events: auto;
        }

        .toast.show {
            transform: translateX(0);
            opacity: 1;
        }

        .toast.success {
            border-left-color: #10b981;
        }

        .toast.error {
            border-left-color: #ef4444;
        }

        .toast-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            flex-shrink: 0;
        }

        .toast.success .toast-icon {
            background: #10b981;
        }

        .toast.error .toast-icon {
            background: #ef4444;
        }

        .toast-content {
            flex: 1;
        }

        .toast-title {
            font-weight: 600;
            font-size: 14px;
            color: #374151;
            margin: 0 0 2px 0;
        }

        .toast-message {
            font-size: 13px;
            color: #6b7280;
            margin: 0;
        }

        .toast-close {
            background: none;
            border: none;
            color: #9ca3af;
            cursor: pointer;
            padding: 0;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .toast-close:hover {
            background: #f3f4f6;
            color: #374151;
        }

        @keyframes toastSlideIn {
            from {
                transform: translateX(400px);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes toastSlideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(400px);
                opacity: 0;
            }
        }
    </style>
    
    <script>
        // Toast notification functions
        function showToast(title, message, type = 'success', duration = 5000) {
            const toastContainer = document.getElementById('toastContainer');
            const toastId = 'toast_' + Date.now();
            
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.id = toastId;
            
            const iconClass = type === 'success' ? 'fas fa-check' : 'fas fa-exclamation-triangle';
            
            toast.innerHTML = `
                <div class="toast-icon">
                    <i class="${iconClass}"></i>
                </div>
                <div class="toast-content">
                    <div class="toast-title">${title}</div>
                    <div class="toast-message">${message}</div>
                </div>
                <button class="toast-close" onclick="closeToast('${toastId}')">
                    <i class="fas fa-times"></i>
                </button>
            `;
            
            toastContainer.appendChild(toast);
            
            // Trigger show animation
            setTimeout(() => {
                toast.classList.add('show');
            }, 100);
            
            // Auto hide after duration
            setTimeout(() => {
                closeToast(toastId);
            }, duration);
        }
        
        function closeToast(toastId) {
            const toast = document.getElementById(toastId);
            if (toast) {
                toast.classList.remove('show');
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }
        }

        // Show success/error messages as toasts
        <?php if (!empty($success)): ?>
        document.addEventListener('DOMContentLoaded', function() {
            showToast('Sucesso', <?php echo json_encode($success); ?>, 'success', 4000);
        });
        <?php endif; ?>
        
        <?php if (!empty($error)): ?>
        document.addEventListener('DOMContentLoaded', function() {
            showToast('Erro', <?php echo json_encode($error); ?>, 'error', 6000);
        });
        <?php endif; ?>
        
        $(document).ready(function() {
            // Initialize DataTable
            $('#zones-table').DataTable({
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.13.7/i18n/pt-PT.json'
                },
                order: [[8, 'asc']], // Sort by "Registado Por" column (9th column, index 8)
                pageLength: 25
            });

            // Handle edit form submission
            $('#editForm').on('submit', function(e) {
                e.preventDefault();
                
                // Submit the form normally (non-AJAX for simplicity)
                this.submit();
            });

            // Handle form submission with progress
            $('#uploadForm').on('submit', function(e) {
                e.preventDefault();
                
                const fileInput = document.getElementById('csv_file');
                if (!fileInput.files || fileInput.files.length === 0) {
                    alert('Por favor, selecione um ficheiro CSV.');
                    return;
                }
                
                // Show progress modal
                showProgressModal();
                closeUploadModal();
                
                // Simulate progress steps
                updateProgress(1, 'active', 'A carregar ficheiro...');
                addLog('Ficheiro selecionado: ' + fileInput.files[0].name);
                
                setTimeout(() => {
                    updateProgress(1, 'completed', 'Ficheiro carregado com sucesso');
                    updateProgress(2, 'active', 'A processar dados CSV...');
                    addLog('A analisar formato do ficheiro...');
                    
                    // Submit the form
                    const formData = new FormData(this);
                    console.log('Submitting form with data:', formData.get('action'));
                    addLog('A enviar ficheiro para o servidor...');
                    
                    fetch(window.location.href, {
                        method: 'POST',
                        body: formData
                    })
                    .then(response => {
                        console.log('Response status:', response.status);
                        addLog(`Resposta do servidor: ${response.status}`);
                        
                        // Check if it's a JSON response (conflicts)
                        const contentType = response.headers.get('content-type');
                        if (contentType && contentType.includes('application/json')) {
                            return response.text().then(text => {
                                try {
                                    const data = JSON.parse(text);
                                    return {type: 'json', data};
                                } catch (e) {
                                    console.error('JSON parse error:', e);
                                    console.error('Response text:', text);
                                    addLog('❌ Erro ao processar resposta do servidor');
                                    addLog('Resposta recebida: ' + text.substring(0, 200) + '...');
                                    throw new Error('Resposta inválida do servidor: ' + e.message);
                                }
                            });
                        } else {
                            return response.text().then(html => ({type: 'html', data: html}));
                        }
                    })
                    .then(result => {
                        if (result.type === 'json') {
                            // Handle conflicts
                            console.log('Conflicts detected:', result.data);
                            updateProgress(2, 'completed', 'Dados CSV processados');
                            updateProgress(3, 'completed', 'Conflitos detectados');
                            updateProgress(4, 'error', 'Resolução necessária');
                            addLog('⚠️ Conflitos encontrados - aguardando resolução do utilizador');
                            
                            // Close progress modal and show conflict modal
                            setTimeout(() => {
                                closeProgressModal();
                                showConflictModal(result.data);
                            }, 1000);
                            
                        } else {
                            // Handle regular HTML response
                            console.log('Response HTML received, length:', result.data.length);
                            const parser = new DOMParser();
                            const doc = parser.parseFromString(result.data, 'text/html');
                            
                            updateProgress(2, 'completed', 'Dados CSV processados');
                            updateProgress(3, 'active', 'A guardar na base de dados...');
                            addLog('A guardar zonas na base de dados...');
                            
                            setTimeout(() => {
                                // Parse response for actual success/error messages
                                const bodyText = doc.body.textContent || doc.body.innerText || '';
                                console.log('Response body:', bodyText);
                                
                                // Look for PHP success message in console logs
                                const scripts = doc.querySelectorAll('script');
                                let phpMessage = '';
                                
                                scripts.forEach(script => {
                                    const content = script.textContent || script.innerHTML;
                                    if (content.includes('PHP Success:')) {
                                        const match = content.match(/PHP Success: (.+?)'/);
                                        if (match) {
                                            phpMessage = match[1];
                                        }
                                    } else if (content.includes('PHP Error:')) {
                                        const match = content.match(/PHP Error: (.+?)'/);
                                        if (match) {
                                            phpMessage = match[1];
                                        }
                                    }
                                });
                                
                                if (phpMessage && phpMessage.includes('Importação concluída')) {
                                    updateProgress(3, 'completed', 'Dados guardados com sucesso');
                                    updateProgress(4, 'completed', 'Importação concluída!');
                                    
                                    // Fetch and display detailed import information
                                    fetchImportDetails().then(details => {
                                        if (details) {
                                            addDetailedImportLogs(details);
                                        } else {
                                            // Fallback: parse basic information from message
                                            addLog('ℹ️ A criar relatório básico da importação...');
                                            const basicInfo = parseImportMessage(phpMessage);
                                            if (basicInfo) {
                                                addBasicImportLog(basicInfo);
                                            }
                                        }
                                        addImportResult(phpMessage);
                                    });
                                    
                                    document.getElementById('progressCloseBtn').innerHTML = '<i class="fas fa-sync"></i> Ver Zonas Importadas';
                                    document.getElementById('progressCloseBtn').onclick = () => {
                                        addLog('🔄 A atualizar a página para mostrar as zonas importadas...');
                                        setTimeout(() => window.location.reload(), 1000);
                                    };
                                } else if (phpMessage && (phpMessage.includes('Erro') || phpMessage.includes('erro'))) {
                                    updateProgress(3, 'error', 'Erro ao guardar dados');
                                    updateProgress(4, 'error', 'Importação falhada');
                                    addLog('❌ ' + phpMessage);
                                    addLog('Verifique os dados do ficheiro CSV');
                                    document.getElementById('progressCloseBtn').innerHTML = '<i class="fas fa-times"></i> Fechar';
                                } else if (bodyText.includes('Importação concluída') || bodyText.includes('Importadas com sucesso')) {
                                    updateProgress(3, 'completed', 'Dados guardados com sucesso');
                                    updateProgress(4, 'completed', 'Importação concluída!');
                                    
                                    // Fetch and display detailed import information
                                    fetchImportDetails().then(details => {
                                        if (details) {
                                            addDetailedImportLogs(details);
                                        } else {
                                            // Fallback: parse basic information from message
                                            addLog('ℹ️ A criar relatório básico da importação...');
                                            const basicInfo = parseImportMessage('Importação concluída com sucesso!');
                                            if (basicInfo) {
                                                addBasicImportLog(basicInfo);
                                            }
                                        }
                                        addImportResult('Importação concluída com sucesso!');
                                    });
                                    
                                    document.getElementById('progressCloseBtn').innerHTML = '<i class="fas fa-sync"></i> Ver Zonas Importadas';
                                    document.getElementById('progressCloseBtn').onclick = () => {
                                        addLog('🔄 A atualizar a página para mostrar as zonas importadas...');
                                        setTimeout(() => window.location.reload(), 1000);
                                    };
                                } else {
                                    updateProgress(3, 'error', 'Resposta inesperada do servidor');
                                    updateProgress(4, 'error', 'Estado desconhecido');
                                    addLog('⚠️ Resposta inesperada do servidor');
                                    addLog('Verifique manualmente se os dados foram importados');
                                    document.getElementById('progressCloseBtn').innerHTML = '<i class="fas fa-times"></i> Fechar';
                                }
                                
                                document.getElementById('progressCloseBtn').style.display = 'block';
                            }, 1000);
                        }
                    })
                    .catch(error => {
                        console.error('Erro:', error);
                        updateProgress(2, 'error', 'Erro ao processar ficheiro');
                        updateProgress(3, 'error', 'Erro na importação');
                        addLog('ERRO: ' + error.message);
                        document.getElementById('progressCloseBtn').style.display = 'block';
                    });
                }, 1000);
            });

            // File upload handlers
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('csv_file');
            const fileSelected = document.getElementById('fileSelected');
            const fileName = document.getElementById('fileName');
            const uploadBtn = document.getElementById('uploadBtn');

            // Drag and drop events
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    fileInput.files = files;
                    updateFileInfo(files[0]);
                }
            });

            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    updateFileInfo(e.target.files[0]);
                }
            });

            function updateFileInfo(file) {
                fileName.textContent = file.name;
                fileSelected.style.display = 'block';
                uploadBtn.disabled = false;
            }
        });

        function showUploadModal() {
            document.getElementById('uploadForm').reset();
            document.getElementById('fileSelected').style.display = 'none';
            document.getElementById('uploadBtn').disabled = true;
            document.getElementById('uploadModal').style.display = 'block';
        }

        function closeUploadModal() {
            document.getElementById('uploadModal').style.display = 'none';
        }



        function editZone(zoneId, zoneData) {
            // Populate the form with zone data
            document.getElementById('editZoneId').value = zoneId;
            document.getElementById('editZoneIdDisplay').value = zoneId;
            document.getElementById('editZona').value = zoneData.zona;
            document.getElementById('editNifEntidade').value = zoneData.nifEntidade;
            document.getElementById('editNomeZona').value = zoneData.nomeZona;
            document.getElementById('editEmail').value = zoneData.email;
            document.getElementById('editQuotaZona').value = zoneData.quotaZona;
            document.getElementById('editMinSelo').value = zoneData.minSelo;
            document.getElementById('editMaxSelo').value = zoneData.maxSelo;
            document.getElementById('editStatus').value = zoneData.status;
            
            // Populate registered fields
            const registeredBy = zoneData.registeredBy || '';
            const registeredFirstName = zoneData.registeredFirstName || '';
            const registeredLastName = zoneData.registeredLastName || '';
            
            // Handle data population intelligently
            if (registeredBy && !registeredFirstName && !registeredLastName) {
                // Check if registeredBy looks like an email or a name
                if (registeredBy.includes('@')) {
                    // It's an email, put it in the email field
                    document.getElementById('editRegisteredBy').value = registeredBy;
                    document.getElementById('editRegisteredFirstName').value = registeredFirstName;
                    document.getElementById('editRegisteredLastName').value = registeredLastName;
                } else {
                    // It looks like a name, try to split it
                    const nameParts = registeredBy.trim().split(' ');
                    const firstName = nameParts[0] || '';
                    const lastName = nameParts.slice(1).join(' ') || '';
                    

                    
                    // Use zone's email as a starting point for registered email, or keep it empty
                    const suggestedEmail = zoneData.email || '';
                    document.getElementById('editRegisteredBy').value = suggestedEmail;
                    document.getElementById('editRegisteredFirstName').value = firstName;
                    document.getElementById('editRegisteredLastName').value = lastName;
                }
            } else {
                // Use the data as provided, but ensure email field has a value
                const emailValue = registeredBy || zoneData.email || '';
                document.getElementById('editRegisteredBy').value = emailValue;
                document.getElementById('editRegisteredFirstName').value = registeredFirstName;
                document.getElementById('editRegisteredLastName').value = registeredLastName;
            }
            
            // Hide error alert and validation messages
            document.getElementById('editErrorAlert').style.display = 'none';
            document.getElementById('editZonaValidation').style.display = 'none';
            
            // Reset field borders
            const inputs = document.querySelectorAll('#editForm input, #editForm select');
            inputs.forEach(input => {
                input.style.borderColor = '#e2e8f0';
                input.style.borderWidth = '1px';
            });
            
            // Show/hide registered by field based on status
            toggleRegisteredByField();
            
            // Handle email field editability based on status
            toggleEmailEditability();
            
            // Initialize validation for edit fields
            initEditValidation();
            
            // Show the modal
            document.getElementById('editModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function toggleRegisteredByField() {
            const statusSelect = document.getElementById('editStatus');
            const registeredByField = document.getElementById('registeredByField');
            const registeredByInput = document.getElementById('editRegisteredBy');
            const registeredFirstNameInput = document.getElementById('editRegisteredFirstName');
            const registeredLastNameInput = document.getElementById('editRegisteredLastName');
            
            if (statusSelect.value === 'registered') {
                registeredByField.style.display = 'block';
                registeredByInput.required = true;
                registeredFirstNameInput.required = true;
                registeredLastNameInput.required = true;
            } else {
                registeredByField.style.display = 'none';
                registeredByInput.required = false;
                registeredFirstNameInput.required = false;
                registeredLastNameInput.required = false;
                // Clear the fields when hidden
                registeredByInput.value = '';
                registeredFirstNameInput.value = '';
                registeredLastNameInput.value = '';
            }
            
            // Also toggle email field editability
            toggleEmailEditability();
        }

        function toggleEmailEditability() {
            const statusSelect = document.getElementById('editStatus');
            const emailInput = document.getElementById('editEmail');
            
            if (statusSelect.value === 'registered') {
                // Make email non-editable like NIF field
                emailInput.readOnly = true;
                emailInput.tabIndex = -1;
                emailInput.style.cssText = 'background-color: #f8fafc; color: #6b7280; cursor: not-allowed; user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; pointer-events: none;';
            } else {
                // Make email editable
                emailInput.readOnly = false;
                emailInput.tabIndex = 0;
                emailInput.style.cssText = '';
            }
        }

        function closeEditModal() {
            document.getElementById('editModal').style.display = 'none';
            document.body.style.overflow = '';
        }

        function openCreateModal() {
            // Clear all form fields
            document.getElementById('createForm').reset();
            document.getElementById('createStatus').value = 'not registered';
            
            // Hide validation messages
            document.getElementById('zonaValidation').style.display = 'none';
            
            // Hide error alert
            document.getElementById('createErrorAlert').style.display = 'none';
            
            // Reset field borders
            const inputs = document.querySelectorAll('#createForm input, #createForm select');
            inputs.forEach(input => {
                input.style.borderColor = '#e2e8f0';
            });
            
            // Hide registered by field initially
            toggleCreateRegisteredByField();
            
            // Show the modal
            document.getElementById('createModal').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }

        function closeCreateModal() {
            document.getElementById('createModal').style.display = 'none';
            document.body.style.overflow = '';
        }

        function initEditValidation() {
            // Remove existing event listeners to avoid duplicates
            const editZonaInput = document.getElementById('editZona');
            const editNifInput = document.getElementById('editNifEntidade');
            
            if (editZonaInput) {
                // Clone and replace to remove all event listeners
                const newZonaInput = editZonaInput.cloneNode(true);
                editZonaInput.parentNode.replaceChild(newZonaInput, editZonaInput);
                
                // Add validation event listeners
                newZonaInput.addEventListener('blur', async function() {
                    if (!this.value) return;
                    
                    const currentZoneId = document.getElementById('editZoneId')?.value || '';
                    const validation = await validateZona(this.value, currentZoneId);
                    const validationEl = document.getElementById('editZonaValidation');
                    
                    if (!validation.valid) {
                        this.style.borderColor = '#dc3545';
                        this.style.borderWidth = '2px';
                        if (validationEl) {
                            const textSpan = validationEl.querySelector('.validation-text');
                            const zoneNameSpan = validationEl.querySelector('.validation-zone-name');
                            if (textSpan) {
                                textSpan.textContent = validation.message;
                            }
                            if (zoneNameSpan && validation.zoneName) {
                                zoneNameSpan.textContent = '(' + validation.zoneName + ')';
                            }
                            validationEl.style.display = 'flex';
                            // Restart animation
                            validationEl.style.animation = 'none';
                            validationEl.offsetHeight; // Trigger reflow
                            validationEl.style.animation = 'validationBlink 0.6s ease-in-out';
                        }
                    } else {
                        this.style.borderColor = '#e2e8f0';
                        this.style.borderWidth = '1px';
                        if (validationEl) {
                            validationEl.style.display = 'none';
                        }
                    }
                });
                
                newZonaInput.addEventListener('input', function() {
                    const validationEl = document.getElementById('editZonaValidation');
                    if (validationEl && validationEl.style.display === 'flex') {
                        validationEl.style.display = 'none';
                        this.style.borderColor = '#e2e8f0';
                        this.style.borderWidth = '1px';
                    }
                });
            }
            
            // NIF validation removed - multiple zones can have the same NIF
            // Only keeping number restriction for NIF field
            if (editNifInput) {
                const newNifInput = editNifInput.cloneNode(true);
                editNifInput.parentNode.replaceChild(newNifInput, editNifInput);

                // Re-apply number restriction only
                newNifInput.addEventListener('input', function(e) {
                    this.value = this.value.replace(/[^0-9]/g, '');
                });
                
                newNifInput.addEventListener('keypress', function(e) {
                    if (!/[0-9]/.test(e.key) && !['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight'].includes(e.key)) {
                        e.preventDefault();
                    }
                });
            }
        }

        function toggleCreateRegisteredByField() {
            const statusSelect = document.getElementById('createStatus');
            const registeredByField = document.getElementById('createRegisteredByField');
            const registeredByInput = document.getElementById('createRegisteredBy');
            
            if (statusSelect.value === 'registered') {
                registeredByField.style.display = 'block';
                registeredByInput.required = true;
            } else {
                registeredByField.style.display = 'none';
                registeredByInput.required = false;
                registeredByInput.value = '';
            }
            
            // Also toggle email field editability
            toggleCreateEmailEditability();
        }

        function toggleCreateEmailEditability() {
            const statusSelect = document.getElementById('createStatus');
            const emailInput = document.getElementById('createEmail');
            
            if (statusSelect.value === 'registered') {
                // Make email non-editable like NIF field
                emailInput.readOnly = true;
                emailInput.tabIndex = -1;
                emailInput.style.cssText = 'background-color: #f8fafc; color: #6b7280; cursor: not-allowed; user-select: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; pointer-events: none;';
            } else {
                // Make email editable
                emailInput.readOnly = false;
                emailInput.tabIndex = 0;
                emailInput.style.cssText = '';
            }
        }

        function showProgressModal() {
            // Reset progress modal
            document.querySelectorAll('.progress-step').forEach(step => {
                step.className = 'progress-step';
            });
            document.getElementById('logContent').innerHTML = 'Iniciando importação...<br>';
            document.getElementById('progressCloseBtn').style.display = 'none';
            
            // Show modal
            document.getElementById('progressModal').style.display = 'block';
        }

        function closeProgressModal() {
            document.getElementById('progressModal').style.display = 'none';
        }

        // Global variables for progress tracking
        let stepStartTimes = {};
        let totalSteps = 4;
        
        function updateProgress(stepNumber, status, message) {
            const step = document.querySelector(`.progress-step:nth-child(${stepNumber})`);
            const statusElement = document.getElementById(`step${stepNumber}`);
            const progressElement = document.getElementById(`progress${stepNumber}`);
            const durationElement = document.getElementById(`duration${stepNumber}`);
            
            step.className = `progress-step ${status}`;
            statusElement.textContent = message;
            
            if (status === 'active') {
                stepStartTimes[stepNumber] = Date.now();
                progressElement.textContent = 'Em Progresso...';
                progressElement.className = 'step-progress processing';
            } else if (status === 'completed') {
                const duration = stepStartTimes[stepNumber] ? 
                    ((Date.now() - stepStartTimes[stepNumber]) / 1000).toFixed(1) : '0.0';
                progressElement.textContent = '✓ Concluído';
                progressElement.className = 'step-progress';
                durationElement.textContent = `Concluído em ${duration}s`;
            } else if (status === 'error') {
                const duration = stepStartTimes[stepNumber] ? 
                    ((Date.now() - stepStartTimes[stepNumber]) / 1000).toFixed(1) : '0.0';
                progressElement.textContent = '✗ Erro';
                progressElement.className = 'step-progress';
                durationElement.textContent = `Falhou após ${duration}s`;
            }
            
            // Update overall progress
            updateOverallProgress();
        }
        
        function updateOverallProgress() {
            const completedSteps = document.querySelectorAll('.progress-step.completed').length;
            const errorSteps = document.querySelectorAll('.progress-step.error').length;
            const activeSteps = document.querySelectorAll('.progress-step.active').length;
            
            let progress = (completedSteps / totalSteps) * 100;
            
            // Add partial progress for active step (50% contribution)
            if (activeSteps > 0) {
                progress += (1 / totalSteps) * 50; // 50% of one step when active
            }
            
            // Cap at 100%
            progress = Math.min(progress, 100);
            
            // Update the progress bar
            const progressFill = document.getElementById('overallProgress');
            const progressText = document.getElementById('overallProgressText');
            
            if (progressFill && progressText) {
                progressFill.style.width = progress + '%';
                
                if (errorSteps > 0) {
                    progressText.textContent = `Passo ${completedSteps}/${totalSteps} - Erro Encontrado`;
                    progressFill.style.background = 'linear-gradient(90deg, #ef4444, #dc2626)';
                } else if (progress >= 100) {
                    progressText.textContent = 'Importação Concluída!';
                    progressFill.style.background = 'linear-gradient(90deg, #10b981, #059669)';
                } else if (activeSteps > 0) {
                    const activeStepNumber = completedSteps + 1;
                    progressText.textContent = `Passo ${activeStepNumber}/${totalSteps} - Em Progresso`;
                    progressFill.style.background = 'linear-gradient(90deg, var(--primary-color), #60a5fa)';
                } else {
                    progressText.textContent = `Passo ${completedSteps}/${totalSteps} Concluído`;
                    progressFill.style.background = 'linear-gradient(90deg, var(--primary-color), #60a5fa)';
                }
            }
        }


        function scrollLogToBottom() {
            const logContent = document.getElementById('logContent');
            
            // Multiple scroll attempts to ensure it works
            const forceScroll = () => {
                // Use a very large number to ensure we go to the absolute bottom
                logContent.scrollTop = logContent.scrollHeight + 1000;
            };
            
            // Immediate scroll
            forceScroll();
            
            // Multiple delayed scrolls with increasing delays
            setTimeout(forceScroll, 10);
            setTimeout(forceScroll, 50);
            setTimeout(forceScroll, 100);
            setTimeout(forceScroll, 200);
            setTimeout(forceScroll, 400);
            setTimeout(forceScroll, 600);
            
            // Use requestAnimationFrame for better timing
            requestAnimationFrame(() => {
                requestAnimationFrame(() => {
                    requestAnimationFrame(forceScroll);
                });
            });
        }

        function addLog(message) {
            const logContent = document.getElementById('logContent');
            const timestamp = new Date().toLocaleTimeString();
            logContent.innerHTML += `[${timestamp}] ${message}<br>`;
            scrollLogToBottom();
        }

        async function fetchImportDetails() {
            try {
                addLog('🔍 A obter detalhes da importação...');
                const response = await fetch(window.location.href, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: 'action=get_import_details'
                });
                
                const data = await response.json();
                console.log('Import details response:', data);
                
                if (data.error) {
                    addLog(`⚠️ ${data.error}`);
                    return null;
                } else {
                    addLog('✅ Detalhes da importação obtidos com sucesso');
                    return data;
                }
            } catch (error) {
                console.error('Error fetching import details:', error);
                addLog(`❌ Erro ao obter detalhes: ${error.message}`);
                return null;
            }
        }

        function parseImportMessage(message) {
            // Try to extract numbers from messages like "Importação concluída: 5 novas zonas importadas, 2 zonas ignoradas"
            const patterns = {
                imported: /(\d+)\s+novas?\s+zonas?\s+importadas?/i,
                updated: /(\d+)\s+zonas?\s+atualizadas?/i,
                skipped: /(\d+)\s+zonas?\s+ignoradas?/i
            };
            
            const result = {};
            for (const [key, pattern] of Object.entries(patterns)) {
                const match = message.match(pattern);
                if (match) {
                    result[key] = parseInt(match[1]);
                }
            }
            
            return Object.keys(result).length > 0 ? result : null;
        }

        function addBasicImportLog(info) {
            addLog('<span class="log-section-divider">═══════════════════════════════════════════════════════════</span>');
            addLog('<span class="log-info">📊 RELATÓRIO BÁSICO DE IMPORTAÇÃO</span>');
            addLog('<span class="log-section-divider">═══════════════════════════════════════════════════════════</span>');
            
            if (info.imported) {
                addLog(`<span class="log-success">✅ Novas zonas importadas: ${info.imported}</span>`);
            }
            if (info.updated) {
                addLog(`<span class="log-success">🔄 Zonas atualizadas: ${info.updated}</span>`);
            }
            if (info.skipped) {
                addLog(`<span class="log-warning">⏭️ Zonas ignoradas: ${info.skipped}</span>`);
            }
            
            addLog('<span class="log-info">ℹ️ Para detalhes completos, verifique os logs do servidor</span>');
            addLog('<span class="log-section-divider">═══════════════════════════════════════════════════════════</span>');
        }

        function addDetailedImportLogs(details) {
            const logContent = document.getElementById('logContent');
            const timestamp = new Date().toLocaleTimeString();
            
            addLog('<span class="log-section-divider">═══════════════════════════════════════════════════════════</span>');
            addLog('<span class="log-info">📊 RELATÓRIO DETALHADO DE IMPORTAÇÃO</span>');
            addLog('<span class="log-section-divider">═══════════════════════════════════════════════════════════</span>');
            
            // Summary statistics
            const totalProcessed = details.totalProcessed || 0;
            const totalSuccess = (details.imported || 0) + (details.updated || 0);
            const totalSkipped = details.skipped || 0;
            const totalErrors = (details.errors && details.errors.length) || 0;
            
            addLog(`<span class="log-info">📁 Total de registos no CSV: ${totalProcessed}</span>`);
            addLog(`<span class="log-success">✅ Processados com sucesso: ${totalSuccess}</span>`);
            addLog(`<span class="log-warning">⏭️ Ignorados: ${totalSkipped}</span>`);
            addLog(`<span class="log-error">❌ Com erros: ${totalErrors}</span>`);
            addLog('');
            
            // Detailed breakdown
            if (details.imported > 0) {
                addLog(`<span class="log-success">🆕 NOVAS ZONAS IMPORTADAS: ${details.imported}</span>`);
                if (details.importedZones && details.importedZones.length > 0) {
                    details.importedZones.forEach((zone, index) => {
                        addLog(`<span class="log-success">   ✓ ${index + 1}. ${zone}</span>`);
                    });
                }
                addLog('');
            }
            
            if (details.updated > 0) {
                addLog(`<span class="log-success">🔄 ZONAS ATUALIZADAS: ${details.updated}</span>`);
                if (details.updatedZones && details.updatedZones.length > 0) {
                    details.updatedZones.forEach((zone, index) => {
                        addLog(`<span class="log-success">   ↻ ${index + 1}. ${zone}</span>`);
                    });
                }
                addLog('');
            }
            
            if (details.skipped > 0) {
                addLog(`<span class="log-warning">⏭️ ZONAS IGNORADAS (dados inalterados): ${details.skipped}</span>`);
                if (details.skippedZones && details.skippedZones.length > 0) {
                    details.skippedZones.forEach((zone, index) => {
                        addLog(`<span class="log-warning">   ⏭ ${index + 1}. ${zone}</span>`);
                    });
                }
                addLog('');
            }
            
            if (details.errors && details.errors.length > 0) {
                addLog(`<span class="log-error">❌ ERROS ENCONTRADOS: ${details.errors.length}</span>`);
                if (details.errorDetails && details.errorDetails.length > 0) {
                    details.errorDetails.forEach((error, index) => {
                        addLog(`<span class="log-error">   ✗ ${index + 1}. Zona ${error.zone} (${error.name})</span>`);
                        addLog(`<span class="log-error">      → ${error.error}</span>`);
                    });
                } else {
                    details.errors.forEach((error, index) => {
                        addLog(`<span class="log-error">   ✗ ${index + 1}. ${error}</span>`);
                    });
                }
                addLog('');
            }
            
            // Import summary
            const successRate = totalProcessed > 0 ? ((totalSuccess / totalProcessed) * 100).toFixed(1) : 0;
            addLog(`<span class="log-info">📈 Taxa de sucesso: ${successRate}% (${totalSuccess}/${totalProcessed})</span>`);
            
            addLog('<span class="log-section-divider">═══════════════════════════════════════════════════════════</span>');
        }

        function addImportResult(message) {
            const logContent = document.getElementById('logContent');
            const timestamp = new Date().toLocaleTimeString();
            
            // Create a prominent result message
            const resultHtml = `
                <div class="import-result-highlight" id="finalResult">
                    <div class="result-icon">🎯</div>
                    <div class="result-content">
                        <div class="result-title">RESULTADO FINAL:</div>
                        <div class="result-message">${message}</div>
                    </div>
                </div>
            `;
            
            logContent.innerHTML += resultHtml;
            
            // Enhanced scroll for final result with multiple attempts
            setTimeout(() => {
                const finalResultElement = document.getElementById('finalResult');
                const logContent = document.getElementById('logContent');
                
                if (finalResultElement) {
                    // First try scrollIntoView
                    finalResultElement.scrollIntoView({ behavior: 'smooth', block: 'end' });
                    
                    // Then force scroll to absolute bottom
                    setTimeout(() => {
                        logContent.scrollTop = logContent.scrollHeight + 1000;
                    }, 200);
                    
                    // Another attempt after animation
                    setTimeout(() => {
                        logContent.scrollTop = logContent.scrollHeight + 1000;
                    }, 800);
                }
            }, 100);
            
            // Also use the standard scroll method as backup
            scrollLogToBottom();
            
            // Additional aggressive scroll after a longer delay
            setTimeout(() => {
                const logContent = document.getElementById('logContent');
                logContent.scrollTop = logContent.scrollHeight + 1000;
            }, 1000);
        }

        function deleteZone(zoneId, zoneName) {
            showDeleteModal(zoneId, zoneName);
        }

        function showDeleteModal(zoneId, zoneName) {
            document.getElementById('deleteZoneId').value = zoneId;
            document.getElementById('deleteZoneName').textContent = zoneName;
            document.getElementById('deleteModal').style.display = 'block';
        }

        function closeDeleteModal() {
            document.getElementById('deleteModal').style.display = 'none';
        }

        function confirmDeleteZone() {
            const zoneId = document.getElementById('deleteZoneId').value;
            const button = document.getElementById('confirmDeleteBtn');
            
            // Show loading state
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> A Eliminar...';
            button.disabled = true;
            
            const form = document.createElement('form');
            form.method = 'POST';
            form.innerHTML = `
                <input type="hidden" name="action" value="delete">
                <input type="hidden" name="zone_id" value="${zoneId}">
            `;
            document.body.appendChild(form);
            form.submit();
        }

        function downloadTemplate() {
            const csvContent = "Zona;NIFEntidade;NomeZona;Email;QuotaZona;MinSelo;MaxSelo\n" +
                              "2780;504722352;ZCA EXEMPLO;<EMAIL>;73;658;730";
            
            const blob = new Blob([csvContent], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'modelo_zonas_de_caca.csv';
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        }

        // Close modal when clicking outside
        window.addEventListener('click', function(e) {
            const modal = document.getElementById('uploadModal');
            if (e.target === modal) {
                closeUploadModal();
            }
            
            const conflictModal = document.getElementById('conflictModal');
            if (e.target === conflictModal) {
                closeConflictModal();
            }
            
            const deleteModal = document.getElementById('deleteModal');
            if (e.target === deleteModal) {
                closeDeleteModal();
            }
        });

        // Global variables for conflict resolution
        let pendingZones = [];
        let conflictData = null;

        function showConflictModal(data) {
            conflictData = data;
            
            // Update summary
            document.getElementById('newZonesCount').textContent = data.summary.new;
            document.getElementById('conflictZonesCount').textContent = data.summary.conflicts;
            document.getElementById('identicalZonesCount').textContent = data.summary.identical;
            
            // Populate conflicts list
            const conflictsList = document.getElementById('conflictsList');
            conflictsList.innerHTML = '';
            
            data.conflicts.forEach(conflict => {
                const conflictItem = createConflictItem(conflict);
                conflictsList.appendChild(conflictItem);
            });
            
            // Show modal
            document.getElementById('conflictModal').style.display = 'block';
        }

        function createConflictItem(conflict) {
            const item = document.createElement('div');
            item.className = 'conflict-item';
            
            const fieldLabels = {
                'nifEntidade': 'NIF',
                'nomeZona': 'Nome',
                'email': 'Email',
                'quotaZona': 'Quota',
                'minSelo': 'Selo Min',
                'maxSelo': 'Selo Max'
            };
            
            let changesHtml = '';
            Object.keys(conflict.changes).forEach(field => {
                const change = conflict.changes[field];
                changesHtml += `
                    <div class="change-field">
                        <span class="field-label">${fieldLabels[field] || field}:</span>
                        <span class="field-value">${change.old} → ${change.new}</span>
                    </div>
                `;
            });
            
            // Set initial action class
            item.className = 'conflict-item action-skip';
            
            item.innerHTML = `
                <div class="conflict-header">
                    <div class="conflict-zone-info">
                        <i class="fas fa-exclamation-triangle" style="color: #f59e0b;"></i>
                        <span>Zona ${conflict.zona} - ${conflict.existing.nomeZona}</span>
                    </div>
                    <div class="conflict-actions">
                        <button type="button" class="conflict-action selected" data-zone="${conflict.zona}" data-action="skip" onclick="selectConflictAction(this)">
                            <i class="fas fa-ban"></i> Ignorar
                        </button>
                        <button type="button" class="conflict-action" data-zone="${conflict.zona}" data-action="update" onclick="selectConflictAction(this)">
                            <i class="fas fa-sync"></i> Atualizar
                        </button>
                    </div>
                </div>
                <div class="conflict-details">
                    <div class="changes-grid">
                        <div class="change-column existing">
                            <h6><i class="fas fa-database"></i> Dados Atuais</h6>
                            ${Object.keys(conflict.changes).map(field => `
                                <div class="change-field">
                                    <span class="field-label">${fieldLabels[field] || field}:</span>
                                    <span class="field-value">${conflict.changes[field].old}</span>
                                </div>
                            `).join('')}
                        </div>
                        <div class="change-column new">
                            <h6><i class="fas fa-file-csv"></i> Dados do CSV</h6>
                            ${Object.keys(conflict.changes).map(field => `
                                <div class="change-field">
                                    <span class="field-label">${fieldLabels[field] || field}:</span>
                                    <span class="field-value">${conflict.changes[field].new}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;
            
            return item;
        }

        function selectConflictAction(button) {
            const zone = button.dataset.zone;
            const action = button.dataset.action;
            const actions = document.querySelectorAll(`[data-zone="${zone}"]`);
            
            // Update button selection
            actions.forEach(action => action.classList.remove('selected'));
            button.classList.add('selected');
            
            // Update conflict item visual state
            const conflictItem = button.closest('.conflict-item');
            conflictItem.className = `conflict-item action-${action}`;
        }

        function selectAllConflictActions(action) {
            const buttons = document.querySelectorAll(`[data-action="${action}"]`);
            buttons.forEach(button => {
                selectConflictAction(button);
            });
            
            // Update all conflict items visual state
            const conflictItems = document.querySelectorAll('.conflict-item');
            conflictItems.forEach(item => {
                item.className = `conflict-item action-${action}`;
            });
        }

        function closeConflictModal() {
            document.getElementById('conflictModal').style.display = 'none';
        }

        function processConflicts() {
            // Collect conflict resolutions
            const resolutions = {};
            const selectedActions = document.querySelectorAll('.conflict-action.selected');
            
            selectedActions.forEach(action => {
                const zone = action.dataset.zone;
                const actionType = action.dataset.action;
                resolutions[zone] = actionType;
            });
            
            console.log('Processing conflicts with resolutions:', resolutions);
            
            // Close conflict modal and show progress
            closeConflictModal();
            showProgressModal();
            
            // Update progress
            updateProgress(1, 'completed', 'Ficheiro processado');
            updateProgress(2, 'completed', 'Conflitos resolvidos');
            updateProgress(3, 'active', 'A aplicar mudanças...');
            addLog('A processar zonas com resoluções definidas...');
            
            // Send conflict resolutions to server
            fetch(window.location.href, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=resolve_conflicts&conflict_resolutions=${encodeURIComponent(JSON.stringify(resolutions))}`
            })
            .then(response => response.text())
            .then(html => {
                console.log('Conflict resolution response received');
                
                            updateProgress(3, 'completed', 'Mudanças aplicadas');
            updateProgress(4, 'completed', 'Processamento concluído!');
            
            // Parse response for success message
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const bodyText = doc.body.textContent || doc.body.innerText || '';
            
            // Fetch and display detailed import information
            fetchImportDetails().then(details => {
                if (details) {
                    addDetailedImportLogs(details);
                }
                
                if (bodyText.includes('Processamento concluído')) {
                    const match = bodyText.match(/Processamento concluído: (.+?)\./);
                    if (match) {
                        addImportResult('Processamento concluído: ' + match[1]);
                    } else {
                        addImportResult('Processamento concluído com sucesso!');
                    }
                } else {
                    addImportResult('Processamento concluído!');
                }
            });
                
                            document.getElementById('progressCloseBtn').innerHTML = '<i class="fas fa-sync"></i> Ver Zonas Processadas';
            document.getElementById('progressCloseBtn').onclick = () => {
                addLog('🔄 A atualizar a página para mostrar as zonas processadas...');
                setTimeout(() => window.location.reload(), 1000);
            };
                document.getElementById('progressCloseBtn').style.display = 'block';
            })
            .catch(error => {
                console.error('Error processing conflicts:', error);
                updateProgress(3, 'error', 'Erro ao aplicar mudanças');
                updateProgress(4, 'error', 'Processamento falhado');
                addLog('❌ Erro: ' + error.message);
                document.getElementById('progressCloseBtn').style.display = 'block';
            });
        }

        function showCreateError(message) {
            const errorAlert = document.getElementById('createErrorAlert');
            const errorMessage = document.getElementById('createErrorMessage');
            
            errorMessage.textContent = message;
            errorAlert.style.display = 'flex';
            
            // Scroll to top of modal to show error
            const modalBody = document.querySelector('#createModal .modal-body');
            modalBody.scrollTop = 0;
        }

        function hideCreateError() {
            document.getElementById('createErrorAlert').style.display = 'none';
        }

        function showEditError(message) {
            const errorAlert = document.getElementById('editErrorAlert');
            const errorMessage = document.getElementById('editErrorMessage');
            
            errorMessage.textContent = message;
            errorAlert.style.display = 'flex';
            
            // Scroll to top of modal to show error
            const modalBody = document.querySelector('#editModal .modal-body');
            modalBody.scrollTop = 0;
        }

        function hideEditError() {
            document.getElementById('editErrorAlert').style.display = 'none';
        }

        function setButtonLoading(buttonId, isLoading, loadingText, originalText) {
            const button = document.getElementById(buttonId);
            if (!button) return;
            
            if (isLoading) {
                button.classList.add('btn-loading');
                button.disabled = true;
                button.innerHTML = `<span class="loading-spinner"></span>${loadingText}`;
            } else {
                button.classList.remove('btn-loading');
                button.disabled = false;
                button.innerHTML = originalText;
            }
        }

        async function validateAndSubmitCreateForm() {
            hideCreateError();
            
            const form = document.getElementById('createForm');
            const formData = new FormData(form);
            
            // Client-side validation
            const zona = formData.get('zona');
            const nif = formData.get('nifEntidade');
            const nome = formData.get('nomeZona');
            const email = formData.get('email');
            const quota = formData.get('quotaZona');
            const minSelo = formData.get('minSelo');
            const maxSelo = formData.get('maxSelo');
            const status = formData.get('status');
            const registeredBy = formData.get('registeredBy');
            
            // Check required fields
            if (!zona || !nif || !nome || !email || !quota || !minSelo || !maxSelo || !status) {
                showCreateError('Todos os campos obrigatórios devem ser preenchidos.');
                return;
            }
            
            // Validate email format
            if (!validateEmailFormat(email)) {
                showCreateError('Formato de email inválido no campo Email.');
                return;
            }
            
            // Check if registered status requires registeredBy fields
            if (status === 'registered') {
                const firstName = formData.get('registeredFirstName');
                const lastName = formData.get('registeredLastName');
                
                if (!registeredBy) {
                    showCreateError('Email de quem registou é obrigatório quando status é "Registada".');
                    return;
                }
                if (!firstName) {
                    showCreateError('Primeiro nome é obrigatório quando status é "Registada".');
                    return;
                }
                if (!lastName) {
                    showCreateError('Último nome é obrigatório quando status é "Registada".');
                    return;
                }
            }
            
            // Validate registeredBy email format if provided
            if (registeredBy && !validateEmailFormat(registeredBy)) {
                                        showCreateError('Formato de email inválido no campo "Email de quem registou".');
                return;
            }
            
            // Show loading state
            setButtonLoading('createSaveBtn', true, 'A Criar Zona...', '<i class="fas fa-plus"></i> Criar Zona');
            
            // Validate zona and NIF for duplicates
            const zonaValidation = await validateZona(zona);
            if (!zonaValidation.valid) {
                setButtonLoading('createSaveBtn', false, '', '<i class="fas fa-plus"></i> Criar Zona');
                showCreateError(zonaValidation.message);
                return;
            }
            
            // NIF validation removed - multiple zones can have the same NIF
            
            // All validation passed, submit the form
            try {
                const response = await fetch(window.location.href, {
                    method: 'POST',
                    body: formData
                });
                
                // Check if response is successful
                if (response.ok) {
                    // Show success state briefly before reload
                    setButtonLoading('createSaveBtn', false, '', '<i class="fas fa-check"></i> Zona Criada!');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    setButtonLoading('createSaveBtn', false, '', '<i class="fas fa-plus"></i> Criar Zona');
                    showCreateError('Erro ao criar zona. Tente novamente.');
                }
            } catch (error) {
                console.error('Submit error:', error);
                setButtonLoading('createSaveBtn', false, '', '<i class="fas fa-plus"></i> Criar Zona');
                showCreateError('Erro de conexão. Verifique sua internet e tente novamente.');
            }
        }

        async function validateAndSubmitEditForm() {
            hideEditError();
            
            const form = document.getElementById('editForm');
            const formData = new FormData(form);
            
            // Client-side validation
            const zona = formData.get('zona');
            const nif = formData.get('nifEntidade');
            const nome = formData.get('nomeZona');
            const email = formData.get('email');
            const quota = formData.get('quotaZona');
            const minSelo = formData.get('minSelo');
            const maxSelo = formData.get('maxSelo');
            const status = formData.get('status');
            const registeredBy = formData.get('registeredBy');
            
            // Check required fields
            if (!zona || !nif || !nome || !email || !quota || !minSelo || !maxSelo || !status) {
                showEditError('Todos os campos obrigatórios devem ser preenchidos.');
                return;
            }
            
            // Validate email format
            if (!validateEmailFormat(email)) {
                showEditError('Formato de email inválido no campo Email.');
                return;
            }
            
            // Check if registered status requires registeredBy fields
            if (status === 'registered') {
                const firstName = formData.get('registeredFirstName');
                const lastName = formData.get('registeredLastName');
                
                if (!registeredBy) {
                    showEditError('Email de quem registou é obrigatório quando status é "Registada".');
                    return;
                }
                if (!firstName) {
                    showEditError('Primeiro nome é obrigatório quando status é "Registada".');
                    return;
                }
                if (!lastName) {
                    showEditError('Último nome é obrigatório quando status é "Registada".');
                    return;
                }
            }
            
            // Validate registeredBy email format if provided
            if (registeredBy && !validateEmailFormat(registeredBy)) {
                                        showEditError('Formato de email inválido no campo "Email de quem registou".');
                return;
            }
            
            // Show loading state
            setButtonLoading('editSaveBtn', true, 'A Guardar...', '<i class="fas fa-save"></i> Guardar Alterações');
            
            // Get current zone ID for validation
            const currentZoneId = formData.get('zone_id');
            
            // Validate zona and NIF for duplicates (excluding current zone)
            const zonaValidation = await validateZona(zona, currentZoneId);
            if (!zonaValidation.valid) {
                setButtonLoading('editSaveBtn', false, '', '<i class="fas fa-save"></i> Guardar Alterações');
                showEditError(zonaValidation.message);
                return;
            }
            
            // NIF validation removed - multiple zones can have the same NIF
            
            // All validation passed, submit the form normally (like delete does)
            // Submit the form normally to allow backend redirect with toast
            form.submit();
        }

        // Validation functions
        function validateEmailFormat(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        function restrictToNumbers(inputId) {
            const input = document.getElementById(inputId);
            if (input) {
                input.addEventListener('input', function(e) {
                    // Remove any non-numeric characters
                    this.value = this.value.replace(/[^0-9]/g, '');
                });
                
                input.addEventListener('keypress', function(e) {
                    // Prevent non-numeric key presses
                    if (!/[0-9]/.test(e.key) && !['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight'].includes(e.key)) {
                        e.preventDefault();
                    }
                });
            }
        }

        function validateEmailField(inputId, validationId) {
            const input = document.getElementById(inputId);
            const validation = document.getElementById(validationId);
            
            if (input && validation) {
                input.addEventListener('blur', function() {
                    const email = this.value.trim();
                    
                    if (email && !validateEmailFormat(email)) {
                        this.style.borderColor = '#dc3545';
                        validation.textContent = 'Formato de email inválido';
                        validation.style.color = '#dc3545';
                        validation.style.display = 'block';
                    } else {
                        this.style.borderColor = '#e2e8f0';
                        validation.style.display = 'none';
                    }
                });
                
                input.addEventListener('input', function() {
                    // Hide validation while typing
                    if (validation.style.display === 'block') {
                        validation.style.display = 'none';
                        this.style.borderColor = '#e2e8f0';
                    }
                });
            }
        }

        async function validateZona(zona, excludeZoneId = '') {
            if (!zona) return { valid: true };
            
            try {
                let body = 'action=validate&field=zona&value=' + encodeURIComponent(zona);
                if (excludeZoneId) {
                    body += '&exclude_zone_id=' + encodeURIComponent(excludeZoneId);
                }
                
                const response = await fetch(window.location.href, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: body
                });
                
                const result = await response.json();
                return result;
            } catch (error) {
                console.error('Validation error:', error);
                return { valid: true };
            }
        }

        async function validateNif(nif, excludeZoneId = '') {
            if (!nif) return { valid: true };
            
            try {
                let body = 'action=validate&field=nif&value=' + encodeURIComponent(nif);
                if (excludeZoneId) {
                    body += '&exclude_zone_id=' + encodeURIComponent(excludeZoneId);
                }
                
                const response = await fetch(window.location.href, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: body
                });
                
                const result = await response.json();
                return result;
            } catch (error) {
                console.error('Validation error:', error);
                return { valid: true };
            }
        }

        // Real-time validation
        document.addEventListener('DOMContentLoaded', function() {
            // Restrict NIF fields to numbers only
            restrictToNumbers('createNifEntidade');
            restrictToNumbers('editNifEntidade');
            
            // Setup email validation for all email fields
            validateEmailField('createEmail', 'createEmailValidation');
            validateEmailField('createRegisteredBy', 'createRegisteredByValidation');
            validateEmailField('editEmail', 'editEmailValidation');
            validateEmailField('editRegisteredBy', 'editRegisteredByValidation');
            
            // Zona validation for create modal
            const zonaInput = document.getElementById('createZona');
            if (zonaInput) {
                zonaInput.addEventListener('blur', async function() {
                    const validation = await validateZona(this.value);
                    const validationEl = document.getElementById('zonaValidation');
                    
                    if (!validation.valid) {
                        this.style.borderColor = '#dc3545';
                        this.style.borderWidth = '2px';
                        if (validationEl) {
                            const textSpan = validationEl.querySelector('.validation-text');
                            const zoneNameSpan = validationEl.querySelector('.validation-zone-name');
                            if (textSpan) {
                                textSpan.textContent = validation.message;
                            }
                            if (zoneNameSpan && validation.zoneName) {
                                zoneNameSpan.textContent = '(' + validation.zoneName + ')';
                            }
                            validationEl.style.display = 'flex';
                            // Restart animation
                            validationEl.style.animation = 'none';
                            validationEl.offsetHeight; // Trigger reflow
                            validationEl.style.animation = 'validationBlink 0.6s ease-in-out';
                        }
                    } else {
                        this.style.borderColor = '#e2e8f0';
                        this.style.borderWidth = '1px';
                        if (validationEl) {
                            validationEl.style.display = 'none';
                        }
                    }
                });
            }
            
            // NIF validation removed - multiple zones can have the same NIF
            
            // Zona validation for edit modal
            const editZonaInput = document.getElementById('editZona');
            if (editZonaInput) {
                editZonaInput.addEventListener('blur', async function() {
                    if (!this.value) return; // Skip validation if empty
                    
                    const currentZoneId = document.getElementById('editZoneId')?.value || '';
                    const validation = await validateZona(this.value, currentZoneId);
                    const validationEl = document.getElementById('editZonaValidation');
                    
                    if (!validation.valid) {
                        this.style.borderColor = '#dc3545';
                        this.style.borderWidth = '2px';
                        if (validationEl) {
                            validationEl.textContent = validation.message;
                            validationEl.style.color = '#dc3545';
                            validationEl.style.display = 'block';
                        }
                    } else {
                        this.style.borderColor = '#e2e8f0';
                        this.style.borderWidth = '1px';
                        if (validationEl) {
                            validationEl.style.display = 'none';
                        }
                    }
                });
                
                editZonaInput.addEventListener('input', function() {
                    // Hide validation while typing
                    const validationEl = document.getElementById('editZonaValidation');
                    if (validationEl && validationEl.style.display === 'block') {
                        validationEl.style.display = 'none';
                        this.style.borderColor = '#e2e8f0';
                        this.style.borderWidth = '1px';
                    }
                });
            }
            
            // NIF validation removed - multiple zones can have the same NIF
        });
    </script>

    <!-- Toast Container (placed here to ensure highest z-index) -->
    <div id="toastContainer" class="toast-container"></div>
</body>
</html> 