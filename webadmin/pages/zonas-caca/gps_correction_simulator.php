<?php
// Start output buffering to prevent headers already sent errors
ob_start();

require_once '../../includes/config.php';
require_once '../../includes/auth.php';

// Check if user is logged in and is admin
if (!is_logged_in()) {
    header('Location: ' . SITE_URL . '/pages/auth/login.php');
    exit();
}

if (!isAdmin()) {
    header('Location: ' . SITE_URL . '/pages/dashboard/');
    exit();
}

$error = '';
$success = '';
$correctionResults = [];

// Initialize Firebase
try {
    $firebase = new Firebase(FIREBASE_PROJECT_ID, FIREBASE_API_KEY);
} catch (Exception $e) {
    error_log("Firebase initialization error: " . $e->getMessage());
    $firebase = null;
}

// GPS Trajectory Correction Functions
function correctGPSTrajectory($trajectory) {
    $correctedTrajectory = [
        'original' => $trajectory,
        'corrected' => null,
        'corrections_applied' => [],
        'statistics' => [
            'original_distance' => 0,
            'corrected_distance' => 0,
            'points_removed' => 0,
            'points_interpolated' => 0,
            'max_speed_before' => 0,
            'max_speed_after' => 0,
            'avg_speed_before' => 0,
            'avg_speed_after' => 0
        ]
    ];
    
    // Get coordinates from trajectory
    $coordinates = [];
    if (isset($trajectory['coordinates']) && is_array($trajectory['coordinates'])) {
        $coordinates = $trajectory['coordinates'];
    } elseif (isset($trajectory['route']) && is_array($trajectory['route'])) {
        $coordinates = $trajectory['route'];
    } elseif (isset($trajectory['pathCoordinates']) && is_array($trajectory['pathCoordinates'])) {
        $coordinates = $trajectory['pathCoordinates'];
    }
    
    if (empty($coordinates)) {
        $correctedTrajectory['corrected'] = $trajectory;
        return $correctedTrajectory;
    }
    
    // Step 1: Normalize and validate coordinates
    $validCoordinates = normalizeCoordinates($coordinates);
    $correctedTrajectory['statistics']['original_points'] = count($coordinates);
    $correctedTrajectory['statistics']['valid_points'] = count($validCoordinates);
    
    if (count($validCoordinates) < 2) {
        $correctedTrajectory['corrected'] = $trajectory;
        return $correctedTrajectory;
    }
    
    // Step 2: Calculate original statistics
    $originalStats = calculateTrajectoryStats($validCoordinates);
    $correctedTrajectory['statistics']['original_distance'] = $originalStats['distance'];
    $correctedTrajectory['statistics']['max_speed_before'] = $originalStats['max_speed'];
    $correctedTrajectory['statistics']['avg_speed_before'] = $originalStats['avg_speed'];
    
    // Step 3: Remove GPS jumps (impossible speeds)
    $jumpCorrected = removeGPSJumps($validCoordinates);
    $correctedTrajectory['statistics']['points_removed'] = count($validCoordinates) - count($jumpCorrected['coordinates']);
    $correctedTrajectory['corrections_applied'] = array_merge($correctedTrajectory['corrections_applied'], $jumpCorrected['corrections']);
    
    // Step 4: Smooth the trajectory
    $smoothed = smoothTrajectory($jumpCorrected['coordinates']);
    $correctedTrajectory['corrections_applied'] = array_merge($correctedTrajectory['corrections_applied'], $smoothed['corrections']);
    
    // Step 5: Interpolate missing segments
    $interpolated = interpolateMissingSegments($smoothed['coordinates']);
    $correctedTrajectory['statistics']['points_interpolated'] = count($interpolated['coordinates']) - count($smoothed['coordinates']);
    $correctedTrajectory['corrections_applied'] = array_merge($correctedTrajectory['corrections_applied'], $interpolated['corrections']);
    
    // Step 6: Apply speed constraints
    $speedConstrained = applySpeedConstraints($interpolated['coordinates']);
    $correctedTrajectory['corrections_applied'] = array_merge($correctedTrajectory['corrections_applied'], $speedConstrained['corrections']);
    
    // Step 7: Calculate final statistics
    $finalStats = calculateTrajectoryStats($speedConstrained['coordinates']);
    $correctedTrajectory['statistics']['corrected_distance'] = $finalStats['distance'];
    $correctedTrajectory['statistics']['max_speed_after'] = $finalStats['max_speed'];
    $correctedTrajectory['statistics']['avg_speed_after'] = $finalStats['avg_speed'];
    
    // Create corrected trajectory
    $correctedTrajectory['corrected'] = $trajectory;
    $correctedTrajectory['corrected']['coordinates'] = $speedConstrained['coordinates'];
    $correctedTrajectory['corrected']['distance'] = number_format($finalStats['distance'], 2) . ' km';
    
    return $correctedTrajectory;
}

function normalizeCoordinates($coordinates) {
    $validCoordinates = [];
    
    foreach ($coordinates as $coord) {
        $lat = null;
        $lng = null;
        $timestamp = null;
        
        // Handle different coordinate formats
        if (is_array($coord)) {
            if (isset($coord['lat']) && isset($coord['lng'])) {
                $lat = floatval($coord['lat']);
                $lng = floatval($coord['lng']);
                $timestamp = $coord['timestamp'] ?? time();
            } elseif (isset($coord['latitude']) && isset($coord['longitude'])) {
                $lat = floatval($coord['latitude']);
                $lng = floatval($coord['longitude']);
                $timestamp = $coord['timestamp'] ?? time();
            } elseif (isset($coord[0]) && isset($coord[1])) {
                $lat = floatval($coord[0]);
                $lng = floatval($coord[1]);
                $timestamp = $coord[2] ?? time();
            }
        }
        
        // Validate coordinates (Portugal bounds)
        if ($lat !== null && $lng !== null && 
            $lat >= 36.0 && $lat <= 42.5 && 
            $lng >= -9.5 && $lng <= -6.0) {
            $validCoordinates[] = [
                'lat' => $lat,
                'lng' => $lng,
                'timestamp' => $timestamp
            ];
        }
    }
    
    return $validCoordinates;
}

function removeGPSJumps($coordinates) {
    $result = [
        'coordinates' => [],
        'corrections' => []
    ];
    
    if (count($coordinates) < 2) {
        $result['coordinates'] = $coordinates;
        return $result;
    }
    
    $cleanCoordinates = [$coordinates[0]]; // Always keep first point
    $removedJumps = 0;
    
    for ($i = 1; $i < count($coordinates); $i++) {
        $prev = $cleanCoordinates[count($cleanCoordinates) - 1];
        $curr = $coordinates[$i];
        
        $distance = calculateHaversineDistance($prev['lat'], $prev['lng'], $curr['lat'], $curr['lng']);
        $timeDiff = abs($curr['timestamp'] - $prev['timestamp']);
        
        if ($timeDiff > 0) {
            $speed = ($distance * 1000) / $timeDiff; // m/s
            $speedKmh = $speed * 3.6; // km/h
            
            // Remove points with impossible speeds (>80 km/h for hunting)
            if ($speedKmh > 80) {
                $removedJumps++;
                continue; // Skip this point
            }
            
            // Remove points with very high speeds (>25 km/h) if distance is large
            if ($speedKmh > 25 && $distance > 0.5) {
                $removedJumps++;
                continue; // Skip this point
            }
        }
        
        $cleanCoordinates[] = $curr;
    }
    
    $result['coordinates'] = $cleanCoordinates;
    
    if ($removedJumps > 0) {
        $result['corrections'][] = "Removidos {$removedJumps} saltos GPS impossíveis";
    }
    
    return $result;
}

function smoothTrajectory($coordinates) {
    $result = [
        'coordinates' => [],
        'corrections' => []
    ];
    
    if (count($coordinates) < 3) {
        $result['coordinates'] = $coordinates;
        return $result;
    }
    
    $smoothed = [];
    $smoothingApplied = 0;
    
    // Apply moving average smoothing
    for ($i = 0; $i < count($coordinates); $i++) {
        if ($i === 0 || $i === count($coordinates) - 1) {
            // Keep first and last points unchanged
            $smoothed[] = $coordinates[$i];
        } else {
            // Apply 3-point moving average
            $prev = $coordinates[$i - 1];
            $curr = $coordinates[$i];
            $next = $coordinates[$i + 1];
            
            $smoothedLat = ($prev['lat'] + $curr['lat'] + $next['lat']) / 3;
            $smoothedLng = ($prev['lng'] + $curr['lng'] + $next['lng']) / 3;
            
            // Only apply smoothing if the change is significant
            $originalDistance = calculateHaversineDistance($prev['lat'], $prev['lng'], $next['lat'], $next['lng']);
            $smoothedDistance = calculateHaversineDistance($prev['lat'], $prev['lng'], $smoothedLat, $smoothedLng) +
                              calculateHaversineDistance($smoothedLat, $smoothedLng, $next['lat'], $next['lng']);
            
            if (abs($originalDistance - $smoothedDistance) > 0.01) { // 10m threshold
                $smoothed[] = [
                    'lat' => $smoothedLat,
                    'lng' => $smoothedLng,
                    'timestamp' => $curr['timestamp']
                ];
                $smoothingApplied++;
            } else {
                $smoothed[] = $curr;
            }
        }
    }
    
    $result['coordinates'] = $smoothed;
    
    if ($smoothingApplied > 0) {
        $result['corrections'][] = "Aplicado suavização em {$smoothingApplied} pontos";
    }
    
    return $result;
}

function interpolateMissingSegments($coordinates) {
    $result = [
        'coordinates' => [],
        'corrections' => []
    ];
    
    if (count($coordinates) < 2) {
        $result['coordinates'] = $coordinates;
        return $result;
    }
    
    $interpolated = [];
    $interpolationCount = 0;
    
    for ($i = 0; $i < count($coordinates) - 1; $i++) {
        $curr = $coordinates[$i];
        $next = $coordinates[$i + 1];
        
        $interpolated[] = $curr;
        
        $distance = calculateHaversineDistance($curr['lat'], $curr['lng'], $next['lat'], $next['lng']);
        $timeDiff = abs($next['timestamp'] - $curr['timestamp']);
        
        // If gap is too large (>1km or >10 minutes), interpolate
        if ($distance > 1.0 || $timeDiff > 600) {
            $steps = max(2, min(10, ceil($distance / 0.2))); // Max 10 interpolation points
            
            for ($step = 1; $step < $steps; $step++) {
                $ratio = $step / $steps;
                
                $interpLat = $curr['lat'] + ($next['lat'] - $curr['lat']) * $ratio;
                $interpLng = $curr['lng'] + ($next['lng'] - $curr['lng']) * $ratio;
                $interpTime = $curr['timestamp'] + ($next['timestamp'] - $curr['timestamp']) * $ratio;
                
                $interpolated[] = [
                    'lat' => $interpLat,
                    'lng' => $interpLng,
                    'timestamp' => $interpTime
                ];
                
                $interpolationCount++;
            }
        }
    }
    
    $interpolated[] = $coordinates[count($coordinates) - 1]; // Add last point
    
    $result['coordinates'] = $interpolated;
    
    if ($interpolationCount > 0) {
        $result['corrections'][] = "Interpolados {$interpolationCount} pontos em segmentos grandes";
    }
    
    return $result;
}

function applySpeedConstraints($coordinates) {
    $result = [
        'coordinates' => [],
        'corrections' => []
    ];
    
    if (count($coordinates) < 2) {
        $result['coordinates'] = $coordinates;
        return $result;
    }
    
    $constrained = [$coordinates[0]]; // Keep first point
    $adjustments = 0;
    $maxAllowedSpeed = 15; // km/h - realistic for hunting
    
    for ($i = 1; $i < count($coordinates); $i++) {
        $prev = $constrained[count($constrained) - 1];
        $curr = $coordinates[$i];
        
        $distance = calculateHaversineDistance($prev['lat'], $prev['lng'], $curr['lat'], $curr['lng']);
        $timeDiff = abs($curr['timestamp'] - $prev['timestamp']);
        
        if ($timeDiff > 0) {
            $speed = ($distance * 1000) / $timeDiff; // m/s
            $speedKmh = $speed * 3.6; // km/h
            
            if ($speedKmh > $maxAllowedSpeed) {
                // Adjust timestamp to make speed realistic
                $requiredTime = ($distance * 1000) / ($maxAllowedSpeed / 3.6);
                $adjustedTimestamp = $prev['timestamp'] + $requiredTime;
                
                $constrained[] = [
                    'lat' => $curr['lat'],
                    'lng' => $curr['lng'],
                    'timestamp' => $adjustedTimestamp
                ];
                
                $adjustments++;
            } else {
                $constrained[] = $curr;
            }
        } else {
            $constrained[] = $curr;
        }
    }
    
    $result['coordinates'] = $constrained;
    
    if ($adjustments > 0) {
        $result['corrections'][] = "Ajustados {$adjustments} pontos para velocidade máxima de {$maxAllowedSpeed} km/h";
    }
    
    return $result;
}

function calculateTrajectoryStats($coordinates) {
    $stats = [
        'distance' => 0,
        'max_speed' => 0,
        'avg_speed' => 0,
        'total_time' => 0
    ];
    
    if (count($coordinates) < 2) {
        return $stats;
    }
    
    $totalDistance = 0;
    $speeds = [];
    
    for ($i = 1; $i < count($coordinates); $i++) {
        $prev = $coordinates[$i - 1];
        $curr = $coordinates[$i];
        
        $distance = calculateHaversineDistance($prev['lat'], $prev['lng'], $curr['lat'], $curr['lng']);
        $totalDistance += $distance;
        
        $timeDiff = abs($curr['timestamp'] - $prev['timestamp']);
        if ($timeDiff > 0) {
            $speed = ($distance * 1000) / $timeDiff; // m/s
            $speedKmh = $speed * 3.6; // km/h
            $speeds[] = $speedKmh;
        }
    }
    
    $stats['distance'] = $totalDistance;
    $stats['max_speed'] = count($speeds) > 0 ? max($speeds) : 0;
    $stats['avg_speed'] = count($speeds) > 0 ? array_sum($speeds) / count($speeds) : 0;
    $stats['total_time'] = count($coordinates) > 1 ? 
        abs($coordinates[count($coordinates) - 1]['timestamp'] - $coordinates[0]['timestamp']) : 0;
    
    return $stats;
}

function calculateHaversineDistance($lat1, $lng1, $lat2, $lng2) {
    $earthRadius = 6371; // Earth radius in kilometers
    
    $lat1Rad = deg2rad($lat1);
    $lat2Rad = deg2rad($lat2);
    $deltaLatRad = deg2rad($lat2 - $lat1);
    $deltaLngRad = deg2rad($lng2 - $lng1);
    
    $a = sin($deltaLatRad/2) * sin($deltaLatRad/2) +
         cos($lat1Rad) * cos($lat2Rad) *
         sin($deltaLngRad/2) * sin($deltaLngRad/2);
    $c = 2 * atan2(sqrt($a), sqrt(1-$a));
    
    return $earthRadius * $c;
}

// Handle AJAX request for trajectory correction
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'correct_trajectory') {
    header('Content-Type: application/json');
    
    $trajectoryId = $_POST['trajectory_id'] ?? '';
    
    if (empty($trajectoryId)) {
        echo json_encode(['error' => 'ID do trajeto é obrigatório']);
        exit;
    }
    
    try {
        // Fetch trajectory from Firebase
        $trajectory = $firebase->getDocument('gestorMobile_trajetos', $trajectoryId);
        
        if (!$trajectory) {
            echo json_encode(['error' => 'Trajeto não encontrado']);
            exit;
        }
        
        // Apply corrections
        $correctionResult = correctGPSTrajectory($trajectory);
        
        echo json_encode([
            'success' => true,
            'result' => $correctionResult
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['error' => 'Erro ao processar trajeto: ' . $e->getMessage()]);
    }
    
    exit;
}

// Fetch some problematic trajectories for testing
$testTrajectories = [];
if ($firebase) {
    try {
        $trajectories = $firebase->getCollection('gestorMobile_trajetos', 10);
        
        foreach ($trajectories as $id => $trajectory) {
            // Quick analysis to find problematic ones
            if (isset($trajectory['coordinates']) && is_array($trajectory['coordinates'])) {
                $coords = $trajectory['coordinates'];
                if (count($coords) > 1) {
                    $totalDistance = 0;
                    $maxSpeed = 0;
                    
                    for ($i = 1; $i < count($coords) && $i < 10; $i++) {
                        $prev = $coords[$i-1];
                        $curr = $coords[$i];
                        
                        if (isset($prev['lat']) && isset($curr['lat'])) {
                            $distance = calculateHaversineDistance($prev['lat'], $prev['lng'], $curr['lat'], $curr['lng']);
                            $totalDistance += $distance;
                            
                            $timeDiff = abs(($curr['timestamp'] ?? 0) - ($prev['timestamp'] ?? 0));
                            if ($timeDiff > 0) {
                                $speedKmh = ($distance * 1000 / $timeDiff) * 3.6;
                                $maxSpeed = max($maxSpeed, $speedKmh);
                            }
                        }
                    }
                    
                    // Include if it has problems
                    if ($totalDistance > 15 || $maxSpeed > 25) {
                        $testTrajectories[$id] = [
                            'id' => $id,
                            'data' => $trajectory,
                            'preview_distance' => $totalDistance,
                            'preview_max_speed' => $maxSpeed
                        ];
                        
                        if (count($testTrajectories) >= 5) break;
                    }
                }
            }
        }
    } catch (Exception $e) {
        $error = 'Erro ao carregar trajetos: ' . $e->getMessage();
    }
}

ob_end_clean();
?>

<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simulador de Correção GPS - ProROLA</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    
    <style>
        .main-content {
            margin-left: 250px;
            padding: 20px;
        }
        
        .trajectory-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .trajectory-header {
            background: #f8f9fa;
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
        }
        
        .before-after-container {
            display: flex;
            gap: 20px;
            padding: 20px;
        }
        
        .before-section, .after-section {
            flex: 1;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
        }
        
        .before-section {
            background: #fff5f5;
            border-color: #fecaca;
        }
        
        .after-section {
            background: #f0fdf4;
            border-color: #bbf7d0;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .stat-item {
            padding: 8px;
            background: white;
            border-radius: 4px;
            text-align: center;
            border: 1px solid #e5e7eb;
        }
        
        .stat-value {
            font-size: 1.2em;
            font-weight: bold;
            color: #1f2937;
        }
        
        .stat-label {
            font-size: 0.8em;
            color: #6b7280;
        }
        
        .corrections-list {
            margin-top: 15px;
        }
        
        .correction-item {
            background: #e0f2fe;
            border: 1px solid #81d4fa;
            border-radius: 4px;
            padding: 8px;
            margin-bottom: 5px;
            font-size: 0.9em;
        }
        
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .problem-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
        }
        
        .problem-high { background-color: #ef4444; }
        .problem-medium { background-color: #f59e0b; }
        .problem-low { background-color: #10b981; }
        
        @media (max-width: 768px) {
            .main-content {
                margin-left: 0;
                padding: 10px;
            }
            
            .before-after-container {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <?php include '../../includes/sidebar.php'; ?>
    <?php include '../../includes/topbar.php'; ?>
    
    <div class="main-content">
        <div class="wrapper">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0 text-gray-800">
                    <i class="fas fa-tools me-2"></i>
                    Simulador de Correção GPS
                </h1>
                <div>
                    <a href="scan_gps_problems.php" class="btn btn-secondary">
                        <i class="fas fa-search me-1"></i>
                        Voltar ao Scanner
                    </a>
                </div>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>
            
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-info-circle me-2"></i>
                                Como Funciona o Simulador
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-2">Este simulador aplica as seguintes correções aos trajetos GPS:</p>
                            <ul class="mb-0">
                                <li><strong>Remoção de Saltos GPS:</strong> Remove pontos com velocidades impossíveis (>80 km/h)</li>
                                <li><strong>Suavização:</strong> Aplica filtros para reduzir ruído GPS</li>
                                <li><strong>Interpolação:</strong> Preenche lacunas grandes no trajeto</li>
                                <li><strong>Limitação de Velocidade:</strong> Ajusta timestamps para velocidades realistas (≤15 km/h)</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-vial me-2"></i>
                                Trajetos de Teste
                            </h5>
                        </div>
                        <div class="card-body">
                            <?php if (empty($testTrajectories)): ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    Nenhum trajeto problemático encontrado para teste.
                                </div>
                            <?php else: ?>
                                <div id="trajectories-container">
                                    <?php foreach ($testTrajectories as $trajectory): ?>
                                        <div class="trajectory-card">
                                            <div class="trajectory-header">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <div>
                                                        <h6 class="mb-1">
                                                            <?php 
                                                            $problemLevel = $trajectory['preview_max_speed'] > 80 ? 'high' : 
                                                                          ($trajectory['preview_max_speed'] > 25 ? 'medium' : 'low');
                                                            ?>
                                                            <span class="problem-indicator problem-<?php echo $problemLevel; ?>"></span>
                                                            Trajeto: <?php echo htmlspecialchars($trajectory['id']); ?>
                                                        </h6>
                                                        <small class="text-muted">
                                                            Distância: <?php echo number_format($trajectory['preview_distance'], 2); ?> km | 
                                                            Velocidade Máxima: <?php echo number_format($trajectory['preview_max_speed'], 1); ?> km/h
                                                        </small>
                                                    </div>
                                                    <button class="btn btn-primary btn-sm" onclick="correctTrajectory('<?php echo $trajectory['id']; ?>')">
                                                        <i class="fas fa-magic me-1"></i>
                                                        Corrigir Trajeto
                                                    </button>
                                                </div>
                                            </div>
                                            
                                            <div id="correction-result-<?php echo $trajectory['id']; ?>" class="d-none">
                                                <!-- Results will be loaded here -->
                                            </div>
                                            
                                            <div id="loading-<?php echo $trajectory['id']; ?>" class="loading-spinner">
                                                <i class="fas fa-spinner fa-spin fa-2x"></i>
                                                <p class="mt-2">Aplicando correções GPS...</p>
                                            </div>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        function correctTrajectory(trajectoryId) {
            console.log('Correcting trajectory:', trajectoryId);
            
            // Show loading
            $('#loading-' + trajectoryId).show();
            $('#correction-result-' + trajectoryId).addClass('d-none');
            
            // Make AJAX request
            $.ajax({
                url: '',
                method: 'POST',
                data: {
                    action: 'correct_trajectory',
                    trajectory_id: trajectoryId
                },
                dataType: 'json',
                success: function(response) {
                    console.log('Correction response:', response);
                    
                    if (response.success) {
                        displayCorrectionResults(trajectoryId, response.result);
                    } else {
                        showError(trajectoryId, response.error || 'Erro desconhecido');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX error:', error);
                    showError(trajectoryId, 'Erro de comunicação: ' + error);
                },
                complete: function() {
                    $('#loading-' + trajectoryId).hide();
                }
            });
        }
        
        function displayCorrectionResults(trajectoryId, result) {
            const container = $('#correction-result-' + trajectoryId);
            const stats = result.statistics;
            
            const html = `
                <div class="before-after-container">
                    <div class="before-section">
                        <h6><i class="fas fa-exclamation-triangle text-danger me-2"></i>Antes da Correção</h6>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-value">${stats.original_distance.toFixed(2)}</div>
                                <div class="stat-label">Distância (km)</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${stats.max_speed_before.toFixed(1)}</div>
                                <div class="stat-label">Vel. Máx (km/h)</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${stats.avg_speed_before.toFixed(1)}</div>
                                <div class="stat-label">Vel. Média (km/h)</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${stats.valid_points}</div>
                                <div class="stat-label">Pontos GPS</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="after-section">
                        <h6><i class="fas fa-check-circle text-success me-2"></i>Após a Correção</h6>
                        <div class="stats-grid">
                            <div class="stat-item">
                                <div class="stat-value">${stats.corrected_distance.toFixed(2)}</div>
                                <div class="stat-label">Distância (km)</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${stats.max_speed_after.toFixed(1)}</div>
                                <div class="stat-label">Vel. Máx (km/h)</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${stats.avg_speed_after.toFixed(1)}</div>
                                <div class="stat-label">Vel. Média (km/h)</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-value">${stats.valid_points + stats.points_interpolated - stats.points_removed}</div>
                                <div class="stat-label">Pontos GPS</div>
                            </div>
                        </div>
                        
                        <div class="corrections-list">
                            <strong>Correções Aplicadas:</strong>
                            ${result.corrections_applied.map(correction => 
                                `<div class="correction-item"><i class="fas fa-check me-2"></i>${correction}</div>`
                            ).join('')}
                        </div>
                    </div>
                </div>
                
                <div class="mt-3 p-3 bg-light rounded">
                    <h6>Resumo das Melhorias:</h6>
                    <ul class="mb-0">
                        <li><strong>Redução de Distância:</strong> ${((stats.original_distance - stats.corrected_distance) / stats.original_distance * 100).toFixed(1)}%</li>
                        <li><strong>Redução de Velocidade Máxima:</strong> ${((stats.max_speed_before - stats.max_speed_after) / stats.max_speed_before * 100).toFixed(1)}%</li>
                        <li><strong>Pontos Removidos:</strong> ${stats.points_removed}</li>
                        <li><strong>Pontos Interpolados:</strong> ${stats.points_interpolated}</li>
                    </ul>
                </div>
            `;
            
            container.html(html);
            container.removeClass('d-none');
        }
        
        function showError(trajectoryId, error) {
            const container = $('#correction-result-' + trajectoryId);
            container.html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    ${error}
                </div>
            `);
            container.removeClass('d-none');
        }
    </script>
</body>
</html> 