<?php
require_once dirname(dirname(__DIR__)) . '/includes/config.php';
require_once dirname(dirname(__DIR__)) . '/includes/auth.php';

// Check if user is logged in and has admin role
try {
    requireAdmin();
} catch (Exception $e) {
    header('Location: ' . SITE_URL . '/pages/auth/login.php');
    exit();
}



$email = '';
$results = [];
$error = '';

// Check if email is provided via URL parameter (from the table links)
if (isset($_GET['email']) && !empty($_GET['email'])) {
    $email = trim($_GET['email']);
    
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Invalid email address provided.';
    } else {
        // Automatically check email verification status when loaded from URL
        $results = checkEmailVerificationDetailed($email, $database);
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['email'])) {
    $email = trim($_POST['email']);
    
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'Please enter a valid email address.';
    } else {
        // Check email verification status with detailed debugging
        $results = checkEmailVerificationDetailed($email, $database);
    }
}

function checkEmailVerificationDetailed($email, $firebase) {
    $results = [
        'email' => $email,
        'timestamp' => date('Y-m-d H:i:s'),
        'steps' => []
    ];
    
    try {
        $results['steps'][] = "Starting email verification check for: $email";
        
        // Step 1: Get admin token
        $adminToken = $firebase->getAdminAccessToken();
        if (!$adminToken) {
            $results['steps'][] = "❌ FAILED: Could not get admin access token";
            $results['final_status'] = 'error';
            $results['error'] = 'Admin token unavailable';
            return $results;
        }
        $results['steps'][] = "✅ Admin access token obtained";
        
        // Step 2: Check if user exists in gestoresZonaCaca collection
        try {
            $firebase->setAccessToken($adminToken);
            // Get gestor users with pagination
    $gestorUsers = [];
    $pageToken = null;
    
    do {
        $pageGestores = $firebase->listDocuments('gestoresZonaCaca', $pageToken, 1000);
        
        // Extract pagination token if present
        $pageToken = $pageGestores['_nextPageToken'] ?? null;
        unset($pageGestores['_nextPageToken']); // Remove pagination token from results
        
        // Merge results
        $gestorUsers = array_merge($gestorUsers, $pageGestores);
        
    } while ($pageToken);
            $gestorUser = null;
            
            if (is_array($gestorUsers)) {
                foreach ($gestorUsers as $userId => $userData) {
                    if (isset($userData['email']) && $userData['email'] === $email) {
                        $gestorUser = $userData;
                        $gestorUser['userId'] = $userId;
                        break;
                    }
                }
            }
            
            if ($gestorUser) {
                $results['steps'][] = "✅ User found in gestoresZonaCaca collection";
                $results['gestor_data'] = $gestorUser;
            } else {
                $results['steps'][] = "⚠️ User NOT found in gestoresZonaCaca collection";
            }
        } catch (Exception $e) {
            $results['steps'][] = "❌ Error checking gestoresZonaCaca: " . $e->getMessage();
        }
        
        // Step 3: Check Firebase Auth lookup API
        $results['steps'][] = "Checking Firebase Auth API...";
        
        $url = "https://identitytoolkit.googleapis.com/v1/accounts:lookup?key=" . FIREBASE_API_KEY;
        $data = [
            'email' => [$email]
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $adminToken
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_VERBOSE, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);
        
        $results['api_request'] = [
            'url' => $url,
            'http_code' => $httpCode,
            'curl_error' => $curlError,
            'response_length' => strlen($response)
        ];
        
        if ($curlError) {
            $results['steps'][] = "❌ cURL Error: $curlError";
            $results['final_status'] = 'error';
            $results['error'] = "Network error: $curlError";
            return $results;
        }
        
        $results['steps'][] = "HTTP Response Code: $httpCode";
        
        if ($httpCode === 200) {
            $result = json_decode($response, true);
            $results['firebase_auth_response'] = $result;
            
            if (isset($result['users']) && !empty($result['users'])) {
                $user = $result['users'][0];
                $results['firebase_user'] = $user;
                $results['steps'][] = "✅ User found in Firebase Auth";
                $results['steps'][] = "User ID: " . ($user['localId'] ?? 'N/A');
                $results['steps'][] = "Email: " . ($user['email'] ?? 'N/A');
                $results['steps'][] = "Email Verified: " . (isset($user['emailVerified']) && $user['emailVerified'] ? 'YES' : 'NO');
                $results['steps'][] = "Created At: " . ($user['createdAt'] ?? 'N/A');
                $results['steps'][] = "Last Login: " . ($user['lastLoginAt'] ?? 'N/A');
                
                // Add password information for super admins only
                if (isSuperAdmin()) {
                    if (isset($user['passwordHash'])) {
                        $results['steps'][] = "🔐 Password Hash: " . $user['passwordHash'] . " (Super Admin Only)";
                    }
                    if (isset($user['passwordUpdatedAt'])) {
                        $passwordUpdated = date('Y-m-d H:i:s', $user['passwordUpdatedAt'] / 1000);
                        $results['steps'][] = "🔐 Password Updated: " . $passwordUpdated . " (Super Admin Only)";
                    }
                } else {
                    $results['steps'][] = "🔐 Password Info: [HIDDEN - Super Admin Only]";
                }
                
                // Determine overall status based on both Firebase Auth and Gestores system
                $firebaseVerified = isset($user['emailVerified']) && $user['emailVerified'];
                $gestorVerified = isset($results['gestor_data']['verified']) && $results['gestor_data']['verified'];
                $isGestorUser = isset($results['gestor_data']['role']) && $results['gestor_data']['role'] === 'gestor_caca';
                
                $results['firebase_verified'] = $firebaseVerified;
                $results['gestor_verified'] = $gestorVerified;
                $results['is_gestor_user'] = $isGestorUser;
                
                $results['steps'][] = "📧 Firebase Auth Email Verified: " . ($firebaseVerified ? 'YES' : 'NO');
                $results['steps'][] = "🏢 Gestores System Verified: " . ($gestorVerified ? 'YES' : 'NO');
                $results['steps'][] = "👤 User Type: " . ($isGestorUser ? 'Hunt Manager (Gestor)' : 'Other');
                
                // Determine final status
                if ($isGestorUser && $gestorVerified) {
                    $results['final_status'] = 'gestor_active';
                    $results['steps'][] = "✅ FINAL RESULT: Hunt Manager - ACTIVE and can login";
                    $results['steps'][] = "ℹ️ Note: Hunt managers don't require Firebase email verification";
                } else if ($firebaseVerified) {
                    $results['final_status'] = 'verified';
                    $results['steps'][] = "✅ FINAL RESULT: Email is VERIFIED";
                } else {
                    $results['final_status'] = 'not_verified';
                    $results['steps'][] = "⚠️ FINAL RESULT: Email is NOT VERIFIED";
                }
            } else {
                $results['steps'][] = "❌ User NOT found in Firebase Auth";
                $results['final_status'] = 'not_registered';
                $results['steps'][] = "❌ FINAL RESULT: Email is NOT REGISTERED in Firebase Auth";
            }
        } else {
            $results['steps'][] = "❌ API Error - HTTP $httpCode";
            $results['api_error_response'] = $response;
            $results['final_status'] = 'error';
            $results['error'] = "Firebase API returned HTTP $httpCode";
        }
        
    } catch (Exception $e) {
        $results['steps'][] = "❌ Exception: " . $e->getMessage();
        $results['final_status'] = 'error';
        $results['error'] = $e->getMessage();
    }
    
    return $results;
}
?>

<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Verificação de Email - Diagnóstico</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .result-card {
            margin-top: 20px;
        }
        .step-item {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .step-item:last-child {
            border-bottom: none;
        }
        .status-verified {
            color: #28a745;
            font-weight: bold;
        }
        .status-gestor-active {
            color: #28a745;
            font-weight: bold;
        }
        .status-not-verified {
            color: #ffc107;
            font-weight: bold;
        }
        .status-not-registered {
            color: #dc3545;
            font-weight: bold;
        }
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        .json-data {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-search"></i> Diagnóstico de Verificação de Email</h2>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Voltar à Lista
                    </a>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Verificar Status de Email</h5>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">
                            Esta ferramenta permite verificar o status de verificação de um email específico no Firebase Auth e na base de dados Firestore.
                            <?php if (isset($_GET['email'])): ?>
                                <br><small class="text-info"><i class="fas fa-info-circle"></i> Email carregado automaticamente da lista de zonas.</small>
                            <?php endif; ?>
                        </p>
                        
                        <form method="POST">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="email" class="form-label">Email para Verificar</label>
                                        <input type="email" class="form-control" id="email" name="email" 
                                               value="<?php echo htmlspecialchars($email); ?>" 
                                               placeholder="<EMAIL>" required>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="submit" class="btn btn-primary d-block w-100">
                                            <i class="fas fa-search"></i> Verificar Email
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                        
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle"></i> <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <?php if (!empty($results)): ?>
                    <div class="result-card">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="mb-0">Resultados para: <?php echo htmlspecialchars($results['email']); ?></h5>
                                <small class="text-muted"><?php echo $results['timestamp']; ?></small>
                            </div>
                            <div class="card-body">
                                <!-- Final Status -->
                                <div class="alert alert-<?php 
                                    echo $results['final_status'] === 'verified' ? 'success' : 
                                        ($results['final_status'] === 'gestor_active' ? 'success' : 
                                        ($results['final_status'] === 'not_verified' ? 'warning' : 
                                        ($results['final_status'] === 'not_registered' ? 'danger' : 'danger'))); 
                                ?>">
                                    <h6 class="mb-0">
                                        <i class="fas fa-<?php 
                                            echo $results['final_status'] === 'verified' ? 'check-circle' : 
                                                ($results['final_status'] === 'gestor_active' ? 'user-check' : 
                                                ($results['final_status'] === 'not_verified' ? 'exclamation-circle' : 
                                                ($results['final_status'] === 'not_registered' ? 'times-circle' : 'exclamation-triangle'))); 
                                        ?>"></i>
                                        Status Final: 
                                        <span class="status-<?php echo $results['final_status']; ?>">
                                            <?php 
                                                echo $results['final_status'] === 'verified' ? 'EMAIL VERIFICADO' : 
                                                    ($results['final_status'] === 'gestor_active' ? 'GESTOR ATIVO - PODE FAZER LOGIN' : 
                                                    ($results['final_status'] === 'not_verified' ? 'EMAIL NÃO VERIFICADO' : 
                                                    ($results['final_status'] === 'not_registered' ? 'EMAIL NÃO REGISTADO' : 'ERRO'))); 
                                            ?>
                                        </span>
                                    </h6>
                                    <?php if (isset($results['error'])): ?>
                                        <small>Erro: <?php echo htmlspecialchars($results['error']); ?></small>
                                    <?php endif; ?>
                                </div>
                                
                                <!-- Detailed Status Breakdown -->
                                <?php if (isset($results['firebase_verified']) || isset($results['gestor_verified'])): ?>
                                <h6>Detalhes do Status de Verificação:</h6>
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <div class="card border-<?php echo isset($results['firebase_verified']) && $results['firebase_verified'] ? 'success' : 'warning'; ?>">
                                            <div class="card-body text-center py-2">
                                                <h6 class="card-title mb-1">
                                                    <i class="fab fa-google"></i> Firebase Auth
                                                </h6>
                                                <span class="badge bg-<?php echo isset($results['firebase_verified']) && $results['firebase_verified'] ? 'success' : 'warning'; ?>">
                                                    <?php echo isset($results['firebase_verified']) && $results['firebase_verified'] ? 'Verificado' : 'Não Verificado'; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card border-<?php echo isset($results['gestor_verified']) && $results['gestor_verified'] ? 'success' : 'danger'; ?>">
                                            <div class="card-body text-center py-2">
                                                <h6 class="card-title mb-1">
                                                    <i class="fas fa-binoculars"></i> Sistema Gestores
                                                </h6>
                                                <span class="badge bg-<?php echo isset($results['gestor_verified']) && $results['gestor_verified'] ? 'success' : 'danger'; ?>">
                                                    <?php echo isset($results['gestor_verified']) && $results['gestor_verified'] ? 'Verificado' : 'Não Verificado'; ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <?php if (isset($results['is_gestor_user']) && $results['is_gestor_user']): ?>
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>Informação:</strong> Este utilizador é um Gestor de Zona de Caça. Os gestores não necessitam de verificação de email no Firebase Auth porque:
                                    <ul class="mt-2 mb-0">
                                        <li>A sua registração é validada através do NIF</li>
                                        <li>As suas zonas são pré-registadas no sistema</li>
                                        <li>O acesso é controlado pelo sistema de gestores</li>
                                    </ul>
                                </div>
                                <?php endif; ?>
                                <?php endif; ?>
                                
                                <!-- Process Steps -->
                                <h6>Passos do Diagnóstico:</h6>
                                <div class="border rounded p-3 mb-3" style="max-height: 300px; overflow-y: auto;">
                                    <?php foreach ($results['steps'] as $step): ?>
                                        <div class="step-item">
                                            <small><?php echo htmlspecialchars($step); ?></small>
                                        </div>
                                    <?php endforeach; ?>
                                </div>
                                
                                <!-- Gestor Data -->
                                <?php if (isset($results['gestor_data'])): ?>
                                    <h6>Dados do Gestor (Firestore):</h6>
                                    <div class="json-data mb-3">
                                        <?php echo json_encode($results['gestor_data'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Firebase User Data -->
                                <?php if (isset($results['firebase_user'])): ?>
                                    <h6>Dados do Firebase Auth:</h6>
                                    <div class="json-data mb-3">
                                        <?php 
                                        $firebaseUserData = $results['firebase_user'];
                                        
                                        // If not super admin, remove sensitive password information
                                        if (!isSuperAdmin()) {
                                            if (isset($firebaseUserData['passwordHash'])) {
                                                $firebaseUserData['passwordHash'] = '[HIDDEN - Only visible to super admins]';
                                            }
                                            if (isset($firebaseUserData['salt'])) {
                                                $firebaseUserData['salt'] = '[HIDDEN - Only visible to super admins]';
                                            }
                                        }
                                        
                                        echo json_encode($firebaseUserData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); 
                                        ?>
                                    </div>
                                    
                                    <?php if (isSuperAdmin() && isset($results['firebase_user']['passwordHash'])): ?>
                                        <div class="alert alert-warning">
                                            <i class="fas fa-shield-alt"></i>
                                            <strong>Super Admin Info:</strong> Password hash is visible because you are logged in as a super admin.
                                            <br><small>Password Hash: <code><?php echo htmlspecialchars($results['firebase_user']['passwordHash']); ?></code></small>
                                            <?php if (isset($results['firebase_user']['passwordUpdatedAt'])): ?>
                                                <br><small>Password Updated: <?php echo date('Y-m-d H:i:s', $results['firebase_user']['passwordUpdatedAt'] / 1000); ?></small>
                                            <?php endif; ?>
                                            
                                            <!-- Super Admin Tools -->
                                            <div class="mt-3">
                                                <h6><i class="fas fa-tools"></i> Super Admin Tools:</h6>
                                                <div class="row g-2">
                                                    <div class="col-md-6">
                                                        <button type="button" class="btn btn-sm btn-outline-primary w-100" onclick="sendPasswordReset('<?php echo htmlspecialchars($results['email']); ?>')">
                                                            <i class="fas fa-envelope"></i> Send Password Reset Email
                                                        </button>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <button type="button" class="btn btn-sm btn-outline-info w-100" onclick="checkHashHistory('<?php echo htmlspecialchars($results['email']); ?>')">
                                                            <i class="fas fa-history"></i> Check Hash Changes
                                                        </button>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <button type="button" class="btn btn-sm btn-outline-success w-100" onclick="verifyLoginAttempts('<?php echo htmlspecialchars($results['email']); ?>')">
                                                            <i class="fas fa-sign-in-alt"></i> Verify Login Attempts
                                                        </button>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <button type="button" class="btn btn-sm btn-outline-warning w-100" onclick="testAuthentication('<?php echo htmlspecialchars($results['email']); ?>')">
                                                            <i class="fas fa-key"></i> Test Authentication
                                                        </button>
                                                    </div>
                                                </div>
                                                
                                                <!-- Quick Debug Info -->
                                                <div class="mt-3 p-2 bg-light rounded">
                                                    <small class="text-muted">
                                                        <strong>Quick Debug Info:</strong><br>
                                                        • Last Login: <?php echo isset($results['firebase_user']['lastLoginAt']) ? date('Y-m-d H:i:s', $results['firebase_user']['lastLoginAt'] / 1000) : 'Never'; ?><br>
                                                        • Account Created: <?php echo isset($results['firebase_user']['createdAt']) ? date('Y-m-d H:i:s', $results['firebase_user']['createdAt'] / 1000) : 'Unknown'; ?><br>
                                                        • Password Age: <?php 
                                                            if (isset($results['firebase_user']['passwordUpdatedAt'])) {
                                                                $passwordAge = time() - ($results['firebase_user']['passwordUpdatedAt'] / 1000);
                                                                echo floor($passwordAge / 86400) . ' days ago';
                                                            } else {
                                                                echo 'Unknown';
                                                            }
                                                        ?>
                                                    </small>
                                                </div>
                                            </div>
                                        </div>
                                    <?php elseif (!isSuperAdmin()): ?>
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle"></i>
                                            <strong>Note:</strong> Password information is hidden. Only super admins (<EMAIL>, <EMAIL>) can view sensitive authentication data.
                                        </div>
                                    <?php endif; ?>
                                <?php endif; ?>
                                
                                <!-- API Request Details -->
                                <?php if (isset($results['api_request'])): ?>
                                    <h6>Detalhes da Requisição API:</h6>
                                    <div class="json-data mb-3">
                                        <?php echo json_encode($results['api_request'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE); ?>
                                    </div>
                                <?php endif; ?>
                                
                                <!-- Error Response -->
                                <?php if (isset($results['api_error_response'])): ?>
                                    <h6>Resposta de Erro da API:</h6>
                                    <div class="json-data mb-3">
                                        <?php echo htmlspecialchars($results['api_error_response']); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Super Admin Tools Functions
        function sendPasswordReset(email) {
            if (!confirm(`Send password reset email to ${email}?`)) return;
            
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
            button.disabled = true;
            
            fetch('./send_password_reset.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email: email })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`Password reset email sent successfully to ${email}`);
                } else {
                    alert(`Error: ${data.error || 'Failed to send password reset email'}`);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Network error occurred while sending password reset email');
            })
            .finally(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            });
        }
        
        function checkHashHistory(email) {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';
            button.disabled = true;
            
            // For now, show current hash info in a modal
            const currentData = <?php echo json_encode($results['firebase_user'] ?? []); ?>;
            
            let historyInfo = `Hash History for: ${email}\n\n`;
            historyInfo += `Current Hash: ${currentData.passwordHash || 'N/A'}\n`;
            historyInfo += `Password Updated: ${currentData.passwordUpdatedAt ? new Date(currentData.passwordUpdatedAt).toLocaleString() : 'N/A'}\n`;
            historyInfo += `Account Created: ${currentData.createdAt ? new Date(parseInt(currentData.createdAt)).toLocaleString() : 'N/A'}\n`;
            historyInfo += `Last Login: ${currentData.lastLoginAt ? new Date(parseInt(currentData.lastLoginAt)).toLocaleString() : 'Never'}\n\n`;
            historyInfo += `Note: Full hash history would require additional logging implementation.`;
            
            alert(historyInfo);
            
            button.innerHTML = originalText;
            button.disabled = false;
        }
        
        function verifyLoginAttempts(email) {
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';
            button.disabled = true;
            
            const currentData = <?php echo json_encode($results['firebase_user'] ?? []); ?>;
            
            let loginInfo = `Login Attempts for: ${email}\n\n`;
            loginInfo += `Last Successful Login: ${currentData.lastLoginAt ? new Date(parseInt(currentData.lastLoginAt)).toLocaleString() : 'Never'}\n`;
            loginInfo += `Account Created: ${currentData.createdAt ? new Date(parseInt(currentData.createdAt)).toLocaleString() : 'N/A'}\n`;
            loginInfo += `Email Verified: ${currentData.emailVerified ? 'Yes' : 'No'}\n`;
            
            if (currentData.lastLoginAt) {
                const lastLogin = new Date(parseInt(currentData.lastLoginAt));
                const now = new Date();
                const daysSince = Math.floor((now - lastLogin) / (1000 * 60 * 60 * 24));
                loginInfo += `Days Since Last Login: ${daysSince}\n`;
            }
            
            loginInfo += `\nNote: Detailed login attempt logs would require additional Firebase Analytics setup.`;
            
            alert(loginInfo);
            
            button.innerHTML = originalText;
            button.disabled = false;
        }
        
        function testAuthentication(email) {
            const password = prompt(`Enter password to test authentication for ${email}:`);
            if (!password) return;
            
            const button = event.target;
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
            button.disabled = true;
            
            fetch('test_authentication.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email: email, password: password })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`✅ Authentication SUCCESSFUL for ${email}\n\nUser can login with this password.`);
                } else {
                    alert(`❌ Authentication FAILED for ${email}\n\nError: ${data.error || 'Invalid credentials'}`);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Network error occurred while testing authentication');
            })
            .finally(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            });
        }
    </script>
</body>
</html> 