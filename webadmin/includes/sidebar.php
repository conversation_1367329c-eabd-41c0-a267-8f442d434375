<?php
// Ensure this file is included, not accessed directly
if (!defined('SITE_NAME')) {
    exit('No direct script access allowed');
}
?>
<aside class="sidebar" id="sidebar">
    <div class="sidebar-header">
        <!-- Sidebar Toggle Button -->
        <button class="sidebar-toggle" id="sidebarToggle" onclick="toggleSidebar()">
            <i class="fas fa-bars"></i>
        </button>
        
        <div class="logo-container">
            <img src="<?php echo SITE_URL; ?>/assets/img/prorola-logo.svg" alt="ProRola Logo" class="sidebar-logo">
        </div>
        <div class="user-info">
            <div class="user-name-container">
                <span class="user-name"><?php echo htmlspecialchars($_SESSION['user']['name'] ?? 'Administrador'); ?></span>
                <button onclick="editProfile()" class="settings-button" title="Editar Perfil">
                    <i class="fas fa-cog"></i>
                </button>
            </div>
            <div class="user-role">Administrador</div>
        </div>
    </div>
    <nav class="sidebar-nav">
        <ul>
            <li><a href="<?php echo SITE_URL; ?>/pages/dashboard/" <?php echo basename(dirname($_SERVER['PHP_SELF'])) == 'dashboard' ? 'class="active"' : ''; ?> data-tooltip="Painel de Controlo"><i class="fas fa-chart-line"></i><span class="menu-text">Painel de Controlo</span></a></li>
            <li><a href="<?php echo SITE_URL; ?>/pages/reports/" <?php echo basename(dirname($_SERVER['PHP_SELF'])) == 'reports' ? 'class="active"' : ''; ?> data-tooltip="Relatórios"><i class="fas fa-file-alt"></i><span class="menu-text">Relatórios</span></a></li>
            <!-- Monitorização temporarily hidden - may be needed later -->
            <!-- <li><a href="<?php echo SITE_URL; ?>/pages/monitoring/" <?php echo basename(dirname($_SERVER['PHP_SELF'])) == 'monitoring' ? 'class="active"' : ''; ?> data-tooltip="Monitorização"><i class="fas fa-route"></i><span class="menu-text">Monitorização</span></a></li> -->
            <li><a href="<?php echo SITE_URL; ?>/pages/map/" <?php echo basename(dirname($_SERVER['PHP_SELF'])) == 'map' ? 'class="active"' : ''; ?> data-tooltip="Mapa"><i class="fas fa-map-marked-alt"></i><span class="menu-text">Mapa</span></a></li>
            <li><a href="<?php echo SITE_URL; ?>/pages/contacts/" <?php echo basename(dirname($_SERVER['PHP_SELF'])) == 'contacts' ? 'class="active"' : ''; ?> data-tooltip="Contactos"><i class="fas fa-dove"></i><span class="menu-text">Contactos</span></a></li>
            <li><a href="<?php echo SITE_URL; ?>/pages/zonas-caca/" <?php echo basename(dirname($_SERVER['PHP_SELF'])) == 'zonas-caca' && basename($_SERVER['PHP_SELF']) != 'trajetos.php' ? 'class="active"' : ''; ?> data-tooltip="Zonas de Caça"><i class="fas fa-map-marker-alt"></i><span class="menu-text">Zonas de Caça</span></a></li>
            <li><a href="<?php echo SITE_URL; ?>/pages/zonas-caca/trajetos.php" <?php echo basename($_SERVER['PHP_SELF']) == 'trajetos.php' ? 'class="active"' : ''; ?> data-tooltip="Trajetos"><i class="fas fa-route"></i><span class="menu-text">Trajetos</span></a></li>
            
            <?php if (isAdmin()): ?>
            <li class="has-submenu">
                <a href="#" class="menu-toggle <?php echo (basename($_SERVER['PHP_SELF']) == 'administrators.php' || basename($_SERVER['PHP_SELF']) == 'tecnicos_prorola.php' || basename($_SERVER['PHP_SELF']) == 'colaboradores.php' || basename($_SERVER['PHP_SELF']) == 'zonas_de_caca.php') ? 'active' : ''; ?>" data-tooltip="Utilizadores">
                    <i class="fas fa-user-friends"></i><span class="menu-text">Utilizadores</span>
                    <i class="fas fa-chevron-down submenu-icon"></i>
                </a>
                <ul class="submenu">
                    <li>
                        <a href="<?php echo SITE_URL; ?>/pages/users/administrators.php" <?php echo basename($_SERVER['PHP_SELF']) == 'administrators.php' ? 'class="active"' : ''; ?>>
                            <i class="fas fa-user-shield"></i>Administradores
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo SITE_URL; ?>/pages/users/colaboradores.php" <?php echo basename($_SERVER['PHP_SELF']) == 'colaboradores.php' ? 'class="active"' : ''; ?>>
                            <i class="fas fa-users"></i>Colaboradores
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo SITE_URL; ?>/pages/users/tecnicos_prorola.php" <?php echo basename($_SERVER['PHP_SELF']) == 'tecnicos_prorola.php' ? 'class="active"' : ''; ?>>
                            <i class="fas fa-user-cog"></i>Técnicos ProROLA
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo SITE_URL; ?>/pages/users/zonas_de_caca.php" <?php echo basename($_SERVER['PHP_SELF']) == 'zonas_de_caca.php' ? 'class="active"' : ''; ?>>
                            <i class="fas fa-binoculars"></i>Zona de Caça
                        </a>
                    </li>
                    <?php if (isSuperAdmin()): ?>
                    <li>
                        <a href="<?php echo SITE_URL; ?>/pages/users/update_single_email.php" <?php echo basename($_SERVER['PHP_SELF']) == 'update_single_email.php' ? 'class="active"' : ''; ?>>
                            <i class="fas fa-envelope-open-text"></i>Atualizar Email
                        </a>
                    </li>
                    <?php endif; ?>
                </ul>
            </li>
            <?php endif; ?>
            
            <?php if (isSuperAdmin()): ?>
            <li class="has-submenu">
                <a href="#" class="menu-toggle <?php echo (basename(dirname($_SERVER['PHP_SELF'])) == 'system') ? 'active' : ''; ?>" data-tooltip="Sistema">
                    <i class="fas fa-cogs"></i><span class="menu-text">Sistema</span>
                    <i class="fas fa-chevron-down submenu-icon"></i>
                </a>
                <ul class="submenu">
                    <li>
                        <a href="<?php echo SITE_URL; ?>/pages/system/" <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' && basename(dirname($_SERVER['PHP_SELF'])) == 'system' ? 'class="active"' : ''; ?>>
                            <i class="fas fa-server"></i>Sistema
                        </a>
                    </li>
                    <li>
                        <a href="<?php echo SITE_URL; ?>/pages/system/messages.php" <?php echo basename($_SERVER['PHP_SELF']) == 'messages.php' ? 'class="active"' : ''; ?>>
                            <i class="fas fa-bullhorn"></i>Mensagens do Sistema
                        </a>
                    </li>
                </ul>
            </li>
            <?php endif; ?>
            
            <li><a href="#" onclick="showLogoutModal(); return false;" data-tooltip="Terminar Sessão"><i class="fas fa-power-off"></i><span class="menu-text">Terminar Sessão</span></a></li>
        </ul>
    </nav>
    
    <!-- Version Footer -->
    <div class="sidebar-footer">
        <div class="version-info">
            <div class="app-name">ProROLA Admin</div>
            <div class="version-text">v1.1 © 2025</div>
        </div>
    </div>
</aside>

<style>
.has-submenu > a {
    display: flex !important;
    align-items: center;
    gap: 8px;
}

.submenu-icon {
    transition: transform 0.3s ease;
    margin-left: auto;
}

.has-submenu.open .submenu-icon {
    transform: rotate(180deg);
}

.submenu {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.05) 100%);
    border-radius: 8px;
    margin-top: 0.5rem;
    backdrop-filter: blur(5px);
}

.has-submenu.open .submenu {
    max-height: 500px;
    padding: 0.5rem 0;
}

.submenu a {
    padding: 0.625rem 0.5rem 0.625rem 0.75rem !important;
    font-size: 0.8125rem !important;
    margin: 0 0.25rem 0.25rem !important;
    border-radius: 8px !important;
    font-weight: 400 !important;
    position: relative;
    white-space: normal;
    overflow: hidden;
    line-height: 1.3;
    word-wrap: break-word;
}

.submenu a::before {
    content: '';
    position: absolute;
    left: 0.5rem;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 4px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    transition: all 0.3s ease;
}

.submenu a:hover::before,
.submenu a.active::before {
    background: white;
    width: 6px;
    height: 6px;
}

.submenu a:hover {
    background: rgba(255, 255, 255, 0.1) !important;
    transform: translateX(2px) !important;
}

.submenu a.active {
    background: rgba(255, 255, 255, 0.2) !important;
    font-weight: 500 !important;
}

.submenu i {
    width: 1em !important;
    margin-right: 0.5rem !important;
    opacity: 0.8;
    font-size: 1rem !important;
}

/* Fix icon alignment for all menu items */
.sidebar-nav a {
    display: flex !important;
    align-items: center;
    gap: 8px;
}

.sidebar-nav i:not(.submenu-icon) {
    width: 1.2em;
    text-align: center;
}

.user-info {
    padding: 0;
    margin: 0;
}

.user-name-container {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 0.25rem;
    padding: 0.25rem 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    height: 32px;
    box-sizing: border-box;
}

.user-name {
    color: #fff;
    font-size: 0.875rem;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    line-height: 1;
    display: flex;
    align-items: center;
    margin: 0;
    padding: 0;
    transform: translateY(-1px);
}

.settings-button {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    padding: 0;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.75rem;
    border-radius: 6px;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(5px);
    flex-shrink: 0;
    line-height: 1;
    box-sizing: border-box;
}

.settings-button:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: rotate(45deg) scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.user-role {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.75rem;
    font-weight: 400;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
    padding: 0.7rem 1rem;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo-container {
    margin-bottom: 1.5rem;
}

.sidebar-logo {
    width: 120px;
    height: auto;
    /* SVG optimization */
    image-rendering: auto;
    image-rendering: -webkit-optimize-contrast;
    shape-rendering: geometricPrecision;
    /* Hardware acceleration */
    transform: translateZ(0);
    will-change: transform;
    /* Anti-aliasing */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Remove any filters that might blur */
    filter: none;
    /* Ensure crisp edges for SVG */
    backface-visibility: hidden;
}

.user-info {
    text-align: center;
}

/* Sidebar Toggle Button */
.sidebar-toggle {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1001;
    backdrop-filter: blur(10px);
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.05);
}

.sidebar-toggle i {
    transition: transform 0.3s ease;
}

.sidebar.collapsed .sidebar-toggle {
    position: static;
    margin: 0 auto 0.75rem auto;
    display: block;
}

.sidebar.collapsed .sidebar-header {
    padding: 0.75rem 0.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.sidebar.collapsed .sidebar-toggle i {
    transform: rotate(180deg);
}

/* Sidebar transition */
.sidebar {
    transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Collapsed sidebar styles */
.sidebar.collapsed {
    width: 60px;
    overflow: visible;
}

.sidebar.collapsed .logo-container {
    width: 40px;
    height: 40px;
    margin: 0;
}

.sidebar.collapsed .sidebar-logo {
    width: 40px;
    height: 40px;
}

.sidebar.collapsed .user-info {
    display: none;
}

.sidebar.collapsed .sidebar-nav a {
    padding: 0.875rem 0.5rem;
    justify-content: center;
    position: relative;
}

.sidebar.collapsed .sidebar-nav a .menu-text {
    display: none;
}

.sidebar.collapsed .sidebar-nav a i:not(.submenu-icon) {
    margin-right: 0;
    font-size: 1.25rem;
}

.sidebar.collapsed .submenu-icon {
    display: none;
}

.sidebar.collapsed .has-submenu .submenu {
    display: none;
}

/* Tooltip for collapsed sidebar */
.sidebar.collapsed .sidebar-nav a::after {
    content: attr(data-tooltip);
    position: absolute;
    left: 70px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1002;
    pointer-events: none;
}

.sidebar.collapsed .sidebar-nav a:hover::after {
    opacity: 1;
    visibility: visible;
    left: 65px;
}

/* Mobile adjustments for toggle */
@media (max-width: 768px) {
    .sidebar-toggle {
        width: 28px;
        height: 28px;
        font-size: 0.875rem;
        top: 0.5rem;
        right: 0.5rem;
    }
}

@media (max-width: 576px) {
    .sidebar-toggle {
        display: none; /* Hide toggle on very small screens */
    }
}

/* Sidebar Navigation */
.sidebar-nav {
    padding-bottom: 80px; /* Add space for footer */
    overflow-y: auto;
    flex: 1;
}

/* Sidebar Footer */
.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1rem;
    background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.1) 100%);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    z-index: 10;
}

.version-info {
    text-align: center;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.version-info:hover {
    opacity: 1;
}

.app-name {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.75rem;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
    margin-bottom: 0.125rem;
    letter-spacing: 0.25px;
}

.version-text {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.625rem;
    font-weight: 400;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    letter-spacing: 0.15px;
}

/* Collapsed sidebar navigation adjustments */
.sidebar.collapsed .sidebar-nav {
    padding-bottom: 60px; /* Reduced space for smaller footer */
}

/* Collapsed sidebar footer adjustments */
.sidebar.collapsed .sidebar-footer {
    padding: 0.5rem 0.25rem;
}

.sidebar.collapsed .app-name {
    font-size: 0.625rem;
    margin-bottom: 0.125rem;
    line-height: 1.2;
}

.sidebar.collapsed .version-text {
    font-size: 0.5rem;
    line-height: 1.2;
}

/* Auto-collapse notification */
.auto-collapse-notification {
    position: fixed;
    top: 80px;
    right: 20px;
    background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    z-index: 10000;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    max-width: 380px;
    font-size: 0.875rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    animation: slideInRight 0.3s ease;
}

/* Auto-expand notification styling */
.auto-collapse-notification.auto-expand {
    background: linear-gradient(135deg, #059669 0%, #10b981 100%);
    border: 1px solid rgba(255, 255, 255, 0.25);
}

.auto-collapse-notification i:first-child {
    font-size: 1.125rem;
    opacity: 0.9;
}

.auto-collapse-notification .close-notification {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    margin-left: auto;
}

.auto-collapse-notification .close-notification:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}
</style>

<script>
// Sidebar toggle functionality
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const content = document.querySelector('.content');
    const header = document.querySelector('.header');
    
    sidebar.classList.toggle('collapsed');
    
    if (sidebar.classList.contains('collapsed')) {
        if (content) content.classList.add('sidebar-collapsed');
        if (header) header.classList.add('sidebar-collapsed');
        localStorage.setItem('sidebarCollapsed', 'true');
        // Clear auto-collapsed flag since user manually collapsed
        localStorage.removeItem('autoCollapsed');
    } else {
        if (content) content.classList.remove('sidebar-collapsed');
        if (header) header.classList.remove('sidebar-collapsed');
        localStorage.setItem('sidebarCollapsed', 'false');
        // Clear auto-collapsed flag and set manual expansion flag
        localStorage.removeItem('autoCollapsed');
        localStorage.setItem('manuallyExpanded', 'true');
        
        // Set a timestamp to prevent auto-collapse for a period
        localStorage.setItem('manualExpandTime', Date.now().toString());
    }
}

// Auto-collapse sidebar when horizontal scrollbar is needed
let autoCollapseTimeout;
let lastActionTime = 0;
const MIN_ACTION_INTERVAL = 1000; // Minimum 1 second between auto actions

function checkHorizontalScrollbar() {
    const content = document.querySelector('.content');
    const sidebar = document.getElementById('sidebar');
    
    if (!content || !sidebar) return;
    
    // Clear any pending timeout to debounce
    if (autoCollapseTimeout) {
        clearTimeout(autoCollapseTimeout);
    }
    
    autoCollapseTimeout = setTimeout(() => {
        // Prevent rapid toggling by enforcing minimum interval
        const now = Date.now();
        if (now - lastActionTime < MIN_ACTION_INTERVAL) {
            return;
        }
        
        // Get current window width
        const windowWidth = window.innerWidth;
        
        // Check if content has horizontal scrollbar with a larger buffer zone
        const contentOverflow = content.scrollWidth - content.clientWidth;
        const hasHorizontalScroll = contentOverflow > 10; // Increased buffer to 10px
        
        // Also check for tables with horizontal scroll
        const tables = content.querySelectorAll('.zones-table-body, .table-responsive');
        let tableHasScroll = false;
        
        tables.forEach(table => {
            const tableOverflow = table.scrollWidth - table.clientWidth;
            if (tableOverflow > 10) { // Increased buffer to 10px
                tableHasScroll = true;
            }
        });
        
        const needsCollapse = hasHorizontalScroll || tableHasScroll;
        const isCurrentlyCollapsed = sidebar.classList.contains('collapsed');
        const wasAutoCollapsed = localStorage.getItem('autoCollapsed') === 'true';
        
        // More robust state change detection
        const currentState = isCurrentlyCollapsed ? 'collapsed' : 'expanded';
        const shouldBeState = needsCollapse ? 'collapsed' : 'expanded';
        
        // Skip if no actual change is needed
        if (currentState === shouldBeState) {
            return;
        }
        
        // Very minimal loop prevention - only for extreme cases
        if (!needsCollapse && isCurrentlyCollapsed && wasAutoCollapsed) {
            // Only prevent expansion if we just collapsed very recently (within 500ms)
            // This prevents immediate back-and-forth toggling
            if (now - lastActionTime < 500) {
                return;
            }
        }
        
        if (needsCollapse && !isCurrentlyCollapsed) {
            // Check if user manually expanded recently
            const manualExpandTime = localStorage.getItem('manualExpandTime');
            const wasManuallyExpanded = localStorage.getItem('manuallyExpanded') === 'true';
            
            if (wasManuallyExpanded && manualExpandTime) {
                const timeSinceManualExpand = now - parseInt(manualExpandTime);
                // Don't auto-collapse for 10 seconds after manual expansion
                if (timeSinceManualExpand < 10000) {
                    return;
                }
                // After 10 seconds, clear the manual expansion flag
                localStorage.removeItem('manuallyExpanded');
                localStorage.removeItem('manualExpandTime');
            }
            
            // Auto-collapse sidebar
            sidebar.classList.add('collapsed');
            if (content) content.classList.add('sidebar-collapsed');
            const header = document.querySelector('.header');
            if (header) header.classList.add('sidebar-collapsed');
            
            // Mark as auto-collapsed (don't save to user preference)
            localStorage.setItem('autoCollapsed', 'true');
            lastActionTime = now;
            
            // Show a subtle notification
            showAutoCollapseNotification();
            
        } else if (!needsCollapse && isCurrentlyCollapsed && wasAutoCollapsed) {
            // Auto-expand sidebar if it was auto-collapsed and no longer needs to be
            const userPreference = localStorage.getItem('sidebarCollapsed');
            if (userPreference !== 'true') {
                // Auto-expand since there's no horizontal scroll needed
                sidebar.classList.remove('collapsed');
                if (content) content.classList.remove('sidebar-collapsed');
                const header = document.querySelector('.header');
                if (header) header.classList.remove('sidebar-collapsed');
                
                localStorage.removeItem('autoCollapsed');
                lastActionTime = now;
                
                // Show expansion notification
                showAutoExpandNotification();
            }
        }
    }, 500); // Increased debounce time to 500ms
}

// Show notification when sidebar auto-collapses
function showAutoCollapseNotification() {
    // Remove any existing notifications first
    const existingNotifications = document.querySelectorAll('.auto-collapse-notification');
    existingNotifications.forEach(notif => notif.remove());
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'auto-collapse-notification';
    notification.innerHTML = `
        <i class="fas fa-info-circle"></i>
        <span>Sidebar minimizado automaticamente para melhor visualização</span>
        <button onclick="this.parentElement.remove()" class="close-notification">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Show notification when sidebar auto-expands
function showAutoExpandNotification() {
    // Remove any existing notifications first
    const existingNotifications = document.querySelectorAll('.auto-collapse-notification');
    existingNotifications.forEach(notif => notif.remove());
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = 'auto-collapse-notification auto-expand';
    notification.innerHTML = `
        <i class="fas fa-expand-arrows-alt"></i>
        <span>Sidebar expandido automaticamente - espaço suficiente disponível</span>
        <button onclick="this.parentElement.remove()" class="close-notification">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 4 seconds (shorter for expand notification)
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 4000);
}

document.addEventListener('DOMContentLoaded', function() {
    // Restore sidebar state from localStorage
    const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    if (isCollapsed) {
        const sidebar = document.getElementById('sidebar');
        const content = document.querySelector('.content');
        const header = document.querySelector('.header');
        
        sidebar.classList.add('collapsed');
        if (content) content.classList.add('sidebar-collapsed');
        if (header) header.classList.add('sidebar-collapsed');
    }
    
    const menuToggles = document.querySelectorAll('.menu-toggle');
    
    menuToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            const parent = this.parentElement;
            parent.classList.toggle('open');
        });
    });
    
    // Ensure the submenu is open if an item within it is active on page load
    const activeSubmenuItem = document.querySelector('.submenu .active');
    if (activeSubmenuItem) {
        const submenuParent = activeSubmenuItem.closest('.has-submenu');
        if (submenuParent) {
            submenuParent.classList.add('open');
        }
    }

    // Also, ensure the main "Utilizadores" link itself is marked active if a sub-item is active,
    // and trigger the visual "open" state for the parent <li>.
    // The PHP already adds 'active' to the menu-toggle link if a child is active.
    // The script above handles opening it.
    
    // Auto-collapse functionality
    const content = document.querySelector('.content');
    
    // Check for horizontal scrollbar on initial load
    setTimeout(checkHorizontalScrollbar, 500);
    
    // Monitor for changes that might require horizontal scrolling
    const observer = new ResizeObserver(() => {
        checkHorizontalScrollbar();
    });
    
    if (content) {
        observer.observe(content);
    }
    
    // Also check when window is resized with longer delay
    window.addEventListener('resize', () => {
        checkHorizontalScrollbar(); // No additional timeout since function has its own debounce
    });
    
    // Check when DataTables are redrawn (if using DataTables)
    if (typeof $ !== 'undefined' && $.fn.DataTable) {
        $(document).on('draw.dt', function() {
            setTimeout(checkHorizontalScrollbar, 200);
        });
    }
});
</script> 