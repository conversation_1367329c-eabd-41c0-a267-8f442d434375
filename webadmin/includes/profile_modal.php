<?php
// Ensure this file is included, not accessed directly
if (!defined('SITE_NAME')) {
    exit('No direct script access allowed');
}
?>

<!-- Profile Edit Modal -->
<div id="profileEditModal" class="profile-modal-overlay" style="display: none;">
    <div class="profile-modal-dialog">
        <div class="profile-modal-content">
            <div class="profile-modal-header">
                <h5 class="profile-modal-title">
                    <i class="fas fa-user-edit"></i>
                    Editar Perfil
                </h5>
                <button type="button" class="profile-modal-close" onclick="closeProfileEditModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="profile-modal-body">
                <form id="profileEditForm" method="POST" action="<?php echo SITE_URL; ?>/pages/users/save_profile.php">
                    <input type="hidden" id="profileEditUserId" name="id" value="">
                    
                    <div class="profile-form-group">
                        <label for="profileEditName">Nome:</label>
                        <input type="text" id="profileEditName" name="name" class="profile-form-control" required>
                    </div>

                    <div class="profile-form-group">
                        <label for="profileEditEmail">Email:</label>
                        <input type="email" id="profileEditEmail" name="email" class="profile-form-control" readonly>
                        <small class="profile-help-text">
                            O email não pode ser alterado
                        </small>
                    </div>

                    <div class="profile-form-group">
                        <label for="profileEditCurrentPassword">Palavra-passe Atual:</label>
                        <input type="password" id="profileEditCurrentPassword" name="current_password" class="profile-form-control">
                        <small class="profile-help-text">
                            Obrigatório para alterar a palavra-passe
                        </small>
                    </div>

                    <div class="profile-form-group">
                        <label for="profileEditPassword">Nova Palavra-passe:</label>
                        <input type="password" id="profileEditPassword" name="password" class="profile-form-control">
                        <small class="profile-help-text">
                            Deixe em branco para manter a palavra-passe atual
                        </small>
                    </div>

                    <div class="profile-form-group">
                        <label for="profileEditConfirmPassword">Confirmar Nova Palavra-passe:</label>
                        <input type="password" id="profileEditConfirmPassword" name="confirm_password" class="profile-form-control">
                        <div class="profile-password-requirements">
                            A palavra-passe deve ter pelo menos 6 caracteres e ser igual à confirmação
                        </div>
                    </div>
                </form>
            </div>
            <div class="profile-modal-footer">
                <button type="button" class="profile-btn profile-btn-secondary" onclick="closeProfileEditModal()">
                    <i class="fas fa-times"></i>
                    Cancelar
                </button>
                <button type="button" class="profile-btn profile-btn-primary" onclick="saveProfileEdit()">
                    <i class="fas fa-save"></i> Guardar
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* Isolated modal styling with specific selectors */
.profile-modal-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    background: rgba(0, 0, 0, 0.5) !important;
    z-index: 9999 !important;
    display: none !important;
    align-items: center !important;
    justify-content: center !important;
}

.profile-modal-overlay.show {
    display: flex !important;
}

.profile-modal-dialog {
    background: white !important;
    border-radius: 8px !important;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5) !important;
    width: 90% !important;
    max-width: 500px !important;
    margin: 0 auto !important;
    position: relative !important;
}

.profile-modal-content {
    border-radius: 8px !important;
    overflow: hidden !important;
}

.profile-modal-header {
    padding: 0.7rem 1.5rem !important;
    border-bottom: 1px solid #e5e7eb !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%) !important;
}

.profile-modal-title {
    margin: 0 !important;
    font-size: 1.25rem !important;
    font-weight: 600 !important;
    color: white !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.75rem !important;
}

.profile-modal-title i {
    font-size: 1.125rem !important;
    color: rgba(255, 255, 255, 0.9) !important;
}

.profile-modal-body {
    padding: 2rem 1.5rem !important;
}

.profile-form-group {
    margin-bottom: 1rem !important;
}

.profile-form-group label {
    display: block !important;
    margin-bottom: 0.5rem !important;
    font-weight: 500 !important;
    color: #374151 !important;
}

.profile-form-control {
    width: 100% !important;
    padding: 0.625rem !important;
    border: 1px solid #d1d5db !important;
    border-radius: 6px !important;
    font-size: 0.875rem !important;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out !important;
    box-sizing: border-box !important;
}

.profile-form-control:focus {
    border-color: #0a7ea4 !important;
    outline: 0 !important;
    box-shadow: 0 0 0 0.2rem rgba(10, 126, 164, 0.25) !important;
}

.profile-form-control[readonly] {
    background-color: #f9fafb !important;
    opacity: 1 !important;
}

.profile-form-control[disabled] {
    background-color: #f3f4f6 !important;
    color: #6b7280 !important;
    cursor: not-allowed !important;
    opacity: 0.8 !important;
}

.profile-help-text {
    display: block !important;
    margin-top: 0.25rem !important;
    font-size: 0.75rem !important;
    color: #6b7280 !important;
}

.profile-password-requirements {
    margin-top: 0.25rem !important;
    font-size: 0.75rem !important;
    color: #6b7280 !important;
}

.profile-modal-footer {
    padding: 1.5rem !important;
    border-top: 1px solid #e5e7eb !important;
    background-color: #f8f9fa !important;
    display: flex !important;
    justify-content: flex-end !important;
    gap: 0.75rem !important;
}

.profile-modal-close {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    font-size: 1rem !important;
    font-weight: 500 !important;
    color: white !important;
    cursor: pointer !important;
    padding: 0.5rem !important;
    width: 36px !important;
    height: 36px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-radius: 6px !important;
    transition: all 0.2s ease !important;
}

.profile-modal-close:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    transform: scale(1.05) !important;
}

.profile-btn {
    display: inline-flex !important;
    align-items: center !important;
    padding: 0.625rem 1rem !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    text-align: center !important;
    text-decoration: none !important;
    border-radius: 8px !important;
    transition: all 0.2s !important;
    cursor: pointer !important;
    gap: 0.5rem !important;
    border: none !important;
}

.profile-btn-primary {
    background-color: #0a7ea4 !important;
    color: white !important;
}

.profile-btn-primary:hover {
    background-color: #096d8c !important;
    color: white !important;
}

.profile-btn-secondary {
    background-color: #6b7280 !important;
    color: white !important;
}

.profile-btn-secondary:hover {
    background-color: #374151 !important;
    color: white !important;
}

.profile-alert {
    margin-bottom: 1rem !important;
    padding: 1rem !important;
    border-radius: 0.375rem !important;
    font-size: 0.875rem !important;
    display: flex !important;
    align-items: center !important;
    gap: 0.5rem !important;
}

.profile-alert-success {
    background-color: #DEF7EC !important;
    border: 1px solid #31C48D !important;
    color: #03543F !important;
}

.profile-alert-danger {
    background-color: #FDE8E8 !important;
    border: 1px solid #F98080 !important;
    color: #9B1C1C !important;
}
</style>

<script>
// Isolated profile editing functions to prevent conflicts
(function() {
    'use strict';
    
    const siteUrl = '<?php echo SITE_URL; ?>';
    
    // Cache for updated profile data
    window.cachedProfileData = window.cachedProfileData || {
        name: '<?php echo htmlspecialchars($_SESSION['user']['name']); ?>'
    };

    // Make editProfile globally available immediately (not waiting for DOM)
    window.editProfile = function() {
        try {
            // Get current user's data (use cached data if available)
            document.getElementById('profileEditUserId').value = '<?php echo $_SESSION['user']['id']; ?>';
            document.getElementById('profileEditName').value = window.cachedProfileData.name;
            document.getElementById('profileEditEmail').value = '<?php echo htmlspecialchars($_SESSION['user']['email']); ?>';
            
            // Reset password fields
            document.getElementById('profileEditCurrentPassword').value = '';
            document.getElementById('profileEditPassword').value = '';
            document.getElementById('profileEditConfirmPassword').value = '';
            
            // Show the modal
            const modal = document.getElementById('profileEditModal');
            if (modal) {
                modal.classList.add('show');
            }
        } catch (error) {
            console.error('Error opening profile modal:', error);
        }
    };
    
    window.closeProfileEditModal = function() {
        try {
            const modal = document.getElementById('profileEditModal');
            if (modal) {
                modal.classList.remove('show');
            }
            
            // Clear any alert messages
            const alerts = document.querySelectorAll('#profileEditForm .profile-alert');
            alerts.forEach(alert => alert.remove());
        } catch (error) {
            console.error('Error closing profile modal:', error);
        }
    };
    
    window.saveProfileEdit = function() {
        try {
            const form = document.getElementById('profileEditForm');
            const formData = new FormData(form);
            
            // Validate password fields if changing password
            const currentPassword = document.getElementById('profileEditCurrentPassword').value;
            const newPassword = document.getElementById('profileEditPassword').value;
            const confirmPassword = document.getElementById('profileEditConfirmPassword').value;
            
            // If any password field is filled, all must be validated
            if (currentPassword || newPassword || confirmPassword) {
                if (!currentPassword) {
                    showProfileAlert('A palavra-passe atual é obrigatória para alterar a palavra-passe', 'danger');
                    return;
                }
                if (!newPassword) {
                    showProfileAlert('A nova palavra-passe é obrigatória', 'danger');
                    return;
                }
                if (!confirmPassword) {
                    showProfileAlert('A confirmação da palavra-passe é obrigatória', 'danger');
                    return;
                }
                if (newPassword.length < 6) {
                    showProfileAlert('A nova palavra-passe deve ter pelo menos 6 caracteres', 'danger');
                    return;
                }
                if (newPassword !== confirmPassword) {
                    showProfileAlert('As palavras-passe não coincidem', 'danger');
                    return;
                }
            }
            
            // Show loading state
            const submitButton = document.querySelector('#profileEditModal .profile-btn-primary');
            const originalText = submitButton.innerHTML;
            submitButton.disabled = true;
            submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> A guardar...';
            
            // Update profile
                                    fetch(`${siteUrl}/pages/users/save_profile.php`, {
                method: 'POST',
                body: formData,
                credentials: 'include',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json'
                }
            })
            .then(async response => {
                const text = await response.text();
                
                let data;
                try {
                    data = JSON.parse(text);
                } catch (e) {
                    // Try to handle JSON-like response with single quotes
                    if (text.includes("'success':") || text.includes("'error':")) {
                        try {
                            const fixedText = text.replace(/'/g, '"');
                            data = JSON.parse(fixedText);
                        } catch (e2) {
                            throw new Error('Resposta inválida do servidor');
                        }
                    } else {
                        throw new Error('Resposta inválida do servidor');
                    }
                }
                
                if (!response.ok) {
                    if (response.status === 401) {
                        window.location.href = `${siteUrl}/pages/auth/login.php?expired=1`;
                        return;
                    }
                    throw new Error(data.error || `Erro ${response.status}`);
                }
                
                return data;
            })
            .then(data => {
                if (data && data.success) {
                    showProfileAlert('Perfil atualizado com sucesso', 'success');
                    
                    // Update the sidebar with new name if it changed
                    const newName = document.getElementById('profileEditName').value;
                    const sidebarName = document.querySelector('.user-name');
                    if (sidebarName) {
                        sidebarName.textContent = newName;
                    }
                    
                    // Update cached profile data for next time modal opens
                    window.cachedProfileData.name = newName;
                    
                    // Clear password fields
                    document.getElementById('profileEditCurrentPassword').value = '';
                    document.getElementById('profileEditPassword').value = '';
                    document.getElementById('profileEditConfirmPassword').value = '';
                    
                    setTimeout(() => {
                        window.closeProfileEditModal();
                    }, 1500);
                } else {
                    showProfileAlert(data?.error || 'Erro ao atualizar perfil', 'danger');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showProfileAlert(error.message || 'Erro ao atualizar perfil', 'danger');
            })
            .finally(() => {
                // Reset button state
                submitButton.disabled = false;
                submitButton.innerHTML = originalText;
            });
        } catch (error) {
            console.error('Error saving profile:', error);
        }
    };
    
    function showProfileAlert(message, type) {
        try {
            // Remove any existing alerts
            const existingAlerts = document.querySelectorAll('#profileEditForm .profile-alert');
            existingAlerts.forEach(alert => alert.remove());
            
            const alertDiv = document.createElement('div');
            alertDiv.className = `profile-alert profile-alert-${type}`;
            
            const icon = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle';
            alertDiv.innerHTML = `<i class="fas ${icon}"></i> ${message}`;
            
            const form = document.getElementById('profileEditForm');
            if (form) {
                form.insertBefore(alertDiv, form.firstChild);
                
                setTimeout(() => {
                    alertDiv.remove();
                }, 5000);
            }
        } catch (error) {
            console.error('Error showing alert:', error);
        }
    }
    
    // Initialize event listeners when DOM is ready or immediately if already loaded
    function initializeEventListeners() {
        // Close modal when clicking outside (only for this specific modal)
        document.addEventListener('click', function(event) {
            const modal = document.getElementById('profileEditModal');
            if (modal && event.target === modal) {
                window.closeProfileEditModal();
            }
        });
        
        // Close modal with Escape key (only when this modal is open)
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                const modal = document.getElementById('profileEditModal');
                if (modal && modal.classList.contains('show')) {
                    window.closeProfileEditModal();
                }
            }
        });
    }
    
    // Initialize immediately if DOM is ready, otherwise wait for it
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeEventListeners);
    } else {
        initializeEventListeners();
    }
})();

// Fallback: Ensure editProfile is always available globally
if (typeof window.editProfile === 'undefined') {
    window.editProfile = function() {
        alert('Profile editing is not available. Please refresh the page.');
    };
}
</script> 