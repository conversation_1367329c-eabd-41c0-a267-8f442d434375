<?php
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/auth.php';

// Check if user is logged in
if (!is_logged_in()) {
    header('Location: ' . SITE_URL . '/pages/auth/login.php');
    exit();
}
?>
<nav class="topbar navbar navbar-expand-lg">
    <div class="container-fluid">
        <!-- Mobile menu toggle -->
        <button class="btn btn-link d-lg-none me-3" id="sidebarToggle">
            <i class="fas fa-bars"></i>
        </button>
        
        <!-- Page title -->
        <h1 class="h4 mb-0">
            <?php
            $page = isset($_GET['page']) ? $_GET['page'] : 'dashboard';
            switch ($page) {
                case 'dashboard':
                    echo 'Dashboard';
                    break;
                case 'reports':
                    echo 'Relatórios';
                    break;
                case 'users':
                    echo 'Usuários';
                    break;
                case 'parceiros':
                    echo 'Parceiros';
                    break;
                case 'map':
                    echo 'Mapa';
                    break;
                case 'statistics':
                    echo 'Estatísticas';
                    break;
                case 'settings':
                    echo 'Configurações';
                    break;
                default:
                    echo 'Dashboard';
            }
            ?>
        </h1>
        
        <!-- Right-aligned items -->
        <div class="ms-auto d-flex align-items-center">
            <!-- Notifications -->
            <div class="dropdown me-3">
                <button class="btn btn-link position-relative" type="button" id="notificationsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-bell"></i>
                    <?php if (isset($_SESSION['notification_count']) && $_SESSION['notification_count'] > 0): ?>
                    <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                        <?php echo $_SESSION['notification_count']; ?>
                    </span>
                    <?php endif; ?>
                </button>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="notificationsDropdown">
                    <li><h6 class="dropdown-header">Notificações</h6></li>
                    <?php if (isset($_SESSION['notifications']) && !empty($_SESSION['notifications'])): ?>
                        <?php foreach ($_SESSION['notifications'] as $notification): ?>
                        <li>
                            <a class="dropdown-item" href="#">
                                <small class="text-muted"><?php echo $notification['time']; ?></small>
                                <p class="mb-0"><?php echo $notification['message']; ?></p>
                            </a>
                        </li>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <li><span class="dropdown-item">Nenhuma notificação</span></li>
                    <?php endif; ?>
                </ul>
            </div>
            
            <!-- User menu -->
            <div class="dropdown">
                <button class="btn btn-link d-flex align-items-center" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <span class="me-2"><?php echo $_SESSION['user']['email']; ?></span>
                    <i class="fas fa-user-circle fa-lg"></i>
                </button>
                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                    <li><h6 class="dropdown-header">Menu do Usuário</h6></li>
                    <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/pages/profile/"><i class="fas fa-user me-2"></i>Perfil</a></li>
                    <li><a class="dropdown-item" href="<?php echo SITE_URL; ?>/pages/settings/"><i class="fas fa-cog me-2"></i>Configurações</a></li>
                    <li><hr class="dropdown-divider"></li>
                    <li><a class="dropdown-item text-danger" href="<?php echo SITE_URL; ?>/pages/auth/logout.php"><i class="fas fa-sign-out-alt me-2"></i>Sair</a></li>
                </ul>
            </div>
        </div>
    </div>
</nav>

<!-- Alert container for notifications -->
<div id="alertContainer" class="container-fluid mt-3"></div> 