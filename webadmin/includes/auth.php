<?php
require_once __DIR__ . '/config.php';

/**
 * Check if user is authenticated
 */
function isAuthenticated() {
    return isset($_SESSION['user']) && !empty($_SESSION['user']);
}

/**
 * Check if user is logged in
 */
function is_logged_in() {
    return isset($_SESSION['user']) && isset($_SESSION['user']['auth_token']) && !is_token_expired();
}

/**
 * Check if the authentication token is expired
 */
function is_token_expired() {
    // If no token expiry time is set, consider it expired
    if (!isset($_SESSION['user']['token_expires'])) {
        return true;
    }
    
    // Firebase tokens typically expire after 1 hour
    // Check if the token is expired (with a 5-minute buffer)
    return $_SESSION['user']['token_expires'] < (time() - 300);
}

/**
 * Attempt to login user
 */
function login($email = null, $password = null) {
    global $database;
    
    if (!$email || !$password) {
        if (!isset($_POST['email']) || !isset($_POST['password'])) {
            return false;
        }
        $email = $_POST['email'];
        $password = $_POST['password'];
    }
    
    error_log("Attempting login for email: $email");
    
    try {
        // Sign in with Firebase Authentication
        $authResult = $database->signInWithEmailAndPassword($email, $password);
        
        if (isset($authResult['idToken'])) {
            // Authentication successful, now get user data from Firestore
            $userId = $authResult['localId'];
            
            error_log("Firebase auth successful for user ID: $userId");
            
            // Set the access token for subsequent Firestore requests
            $database->setAccessToken($authResult['idToken']);
            
            // Try to get user data from Firestore
            try {
                $userData = $database->getDocument('users', $userId);
                error_log("Retrieved user data from users collection: " . print_r($userData, true));
            } catch (Exception $e) {
                error_log("User document not found in users collection, checking gestoresZonaCaca collection");
                
                // Try to get user data from gestoresZonaCaca collection
                try {
                    $userData = $database->getDocument('gestoresZonaCaca', $userId);
                    error_log("Retrieved user data from gestoresZonaCaca collection: " . print_r($userData, true));
                } catch (Exception $e2) {
                    error_log("User document not found in gestoresZonaCaca collection either");
                    
                    // If user document doesn't exist in either collection, only create default users for specific cases
                if ($email === '<EMAIL>' || $email === '<EMAIL>') {
                    // Create a default admin user
                    $userData = [
                        'email' => $email,
                        'name' => 'Administrador',
                        'role' => 'administrador',
                        'created_at' => time(),
                        'updated_at' => time()
                    ];
                    error_log("Creating admin user: " . print_r($userData, true));
                
                // Create the user document in Firestore
                $database->setDocument('users', $userId, $userData);
                    } else {
                        // For other users, don't automatically create a document
                        // This prevents hunt managers from getting wrong roles
                        error_log("User not found in any collection and not admin - login failed");
                        throw new Exception("User account not found. Please contact support.");
                    }
                }
            }
            
            // Check if user is a technician and needs verification
            if (isset($userData['role']) && $userData['role'] === 'tecnico_prorola' && !($userData['verified'] ?? false)) {
                throw new Exception('A sua conta de técnico ainda não foi aprovada por um responsável ProROLA. Por favor, aguarde a aprovação antes de tentar fazer login.');
            }

            // Update lastActivity in database
            $currentTime = time();
            try {
                $database->setDocument('users', $userId, ['lastActivity' => $currentTime], true);
                error_log("Updated lastActivity in database for user: $userId");
            } catch (Exception $e) {
                error_log("Failed to update lastActivity in database: " . $e->getMessage());
            }
            
            // Store user data in session
            $_SESSION['user'] = [
                'id' => $userId,
                'email' => $email,
                'name' => $userData['name'] ?? explode('@', $email)[0],
                'role' => $userData['role'] ?? 'utilizador_movel',
                'verified' => $userData['verified'] ?? true,
                'auth_token' => $authResult['idToken'],
                'refresh_token' => $authResult['refreshToken'],
                'token_expires' => time() + intval($authResult['expiresIn']),
                'last_activity' => $currentTime
            ];
            
            error_log("Session data set: " . print_r($_SESSION['user'], true));
            
            // Double check role is set correctly
            if (($email === '<EMAIL>' || $email === '<EMAIL>') && $_SESSION['user']['role'] !== 'administrador') {
                $_SESSION['user']['role'] = 'administrador';
                error_log("Forced admin role for " . $email);
            }
            
            error_log("Login successful for user: $email with ID: $userId and role: " . $_SESSION['user']['role']);
            return true;
        }
        
        error_log("Login failed - no idToken in authResult");
        return false;
    } catch (Exception $e) {
        error_log("Login error: " . $e->getMessage());
        return false;
    }
}

/**
 * Logout user
 */
function logout() {
    // Clear all session variables
    $_SESSION = array();
    
    // Destroy the session
    session_destroy();
    
    // Redirect to login page
    header('Location: ' . SITE_URL . '/pages/auth/login.php');
    exit();
}

/**
 * Check if session is valid
 */
function checkSession() {
    // Check if user is logged in
    if (!isAuthenticated()) {
        header('Location: ' . SITE_URL . '/pages/auth/login.php');
        exit();
    }
    
    // Update last activity time
    $_SESSION['user']['last_activity'] = time();
    
    // Check session timeout (30 minutes of inactivity)
    if (time() - $_SESSION['user']['last_activity'] > 1800) {
        logout();
    }
}

/**
 * Check if user is admin (Administrador)
 */
function isAdmin() {
    return isset($_SESSION['user']) && $_SESSION['user']['role'] === 'administrador';
}

/**
 * Check if user is superadmin (admin@prorola.<NAME_EMAIL>)
 */
function isSuperAdmin() {
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'administrador') {
        return false;
    }
    
    $superAdminEmails = ['<EMAIL>', '<EMAIL>'];
    return in_array($_SESSION['user']['email'], $superAdminEmails);
}

/**
 * Check if user is technician (Técnico)
 */
function isTecnico() {
    return isset($_SESSION['user']) && $_SESSION['user']['role'] === 'tecnico';
}

/**
 * Check if user is mobile user (Utilizador Móvel)
 */
function isUtilizadorMovel() {
    return isset($_SESSION['user']) && $_SESSION['user']['role'] === 'utilizador_movel';
}

/**
 * Check if user is parceiro
 */
function isParceiro() {
    return isset($_SESSION['user']) && $_SESSION['user']['role'] === 'parceiro';
}

/**
 * Check if user is gestor de caça
 */
function isGestorCaca() {
    return isset($_SESSION['user']) && $_SESSION['user']['role'] === 'gestor_caca';
}

/**
 * Enhanced authentication check for webadmin only
 */
function isWebAdminUser() {
    return isAuthenticated() && 
           isset($_SESSION['user']['role']) && 
           in_array($_SESSION['user']['role'], ['administrador', 'tecnico_prorola', 'colaborador']);
}

/**
 * Enhanced authentication check for gestores only
 */
function isGestorUser() {
    return isAuthenticated() && 
           isset($_SESSION['user']['role']) && 
           $_SESSION['user']['role'] === 'gestor_caca';
}

/**
 * Require admin role
 */
function requireAdmin() {
    // Check if user is logged in
    if (!is_logged_in()) {
        // Token expired or user not logged in, redirect to login
        header('Location: ' . SITE_URL . '/pages/auth/login.php?expired=1');
        exit();
    }
    
    // Check if user is admin
    if ($_SESSION['user']['role'] !== 'administrador') {
        header('Location: ' . SITE_URL . '/pages/error/unauthorized.php');
        exit();
    }
    
    // Set the access token for Firebase requests
    global $database;
    $database->setAccessToken($_SESSION['user']['auth_token']);
    
    // Update last activity time stamp
    $_SESSION['last_activity'] = time();
}

/**
 * Require technician role
 */
function requireTecnico() {
    // Check if user is logged in
    if (!is_logged_in()) {
        header('Location: ' . SITE_URL . '/pages/auth/login.php?expired=1');
        exit();
    }
    
    // Check if user is tecnico or admin
    if ($_SESSION['user']['role'] !== 'tecnico' && $_SESSION['user']['role'] !== 'administrador') {
        header('Location: ' . SITE_URL . '/pages/error/unauthorized.php');
        exit();
    }
    
    // Set the access token for Firebase requests
    global $database;
    $database->setAccessToken($_SESSION['user']['auth_token']);
    
    // Update last activity time stamp
    $_SESSION['last_activity'] = time();
}

/**
 * Require parceiro role
 */
function requireParceiro() {
    checkSession();
    if (!isParceiro() && !isAdmin()) {
        header('Location: ' . SITE_URL . '/pages/error/unauthorized.php');
        exit();
    }
}

/**
 * Register a new user
 */
function registerUser($email, $password, $role = 'user', $name = '') {
    global $database;
    
    try {
        // Create user in Firebase Authentication
        $authResult = $database->createUser($email, $password);
        
        if (!isset($authResult['localId'])) {
            throw new Exception("Failed to create user in Firebase Authentication");
        }
        
        $userId = $authResult['localId'];
        
        // Create user data for Firestore
        $userData = [
            'email' => $email,
            'role' => $role,
            'name' => $name,
            'created_at' => time(),
            'updated_at' => time()
        ];
        
        // Create the user document in Firestore
        $database->setDocument('users', $userId, $userData);
        
        return $userId;
    } catch (Exception $e) {
        error_log('User registration error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Update user role
 */
function updateUserRole($userId, $newRole) {
    global $database;
    
    try {
        if ($newRole !== 'admin' && $newRole !== 'parceiro' && $newRole !== 'user') {
            throw new Exception('Invalid role');
        }
        
        // Update the user document in Firestore
        $database->setDocument('users', $userId, [
            'role' => $newRole,
            'updated_at' => time()
        ], true); // Merge with existing document
        
        return true;
    } catch (Exception $e) {
        error_log('Role update error: ' . $e->getMessage());
        return false;
    }
}

/**
 * Get current user data
 */
function get_user() {
    return is_logged_in() ? $_SESSION['user'] : null;
} 