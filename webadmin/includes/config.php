<?php
// Set separate session name for webadmin
ini_set('session.name', 'WEBADMIN_SESSION');
session_name('WEBADMIN_SESSION');

// Only start session if one hasn't been started yet
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define base paths
$scriptPath = dirname(__FILE__);
define('BASE_PATH', dirname($scriptPath));
define('INCLUDES_PATH', $scriptPath);

// Site configuration
define('SITE_NAME', 'ProROLA');
define('SITE_URL', 'https://prorola.app/webadmin');

// Add path constants for common directories
define('ASSETS_PATH', BASE_PATH . '/assets');
define('PAGES_PATH', BASE_PATH . '/pages');
define('UPLOADS_PATH', BASE_PATH . '/uploads');

// Function to get relative path from current script to base path
function get_relative_path($from, $to) {
    $from = str_replace('\\', '/', $from);
    $to = str_replace('\\', '/', $to);

    $from = explode('/', $from);
    $to = explode('/', $to);

    $relPath = '';

    // Find how far the path is different
    $i = 0;
    while(isset($from[$i]) && isset($to[$i])) {
        if($from[$i] != $to[$i]) break;
        $i++;
    }

    // Add .. for each directory we need to go up
    for($j = $i; $j < count($from) - 1; $j++) {
        $relPath .= '../';
    }

    // Add the path to the target
    for($j = $i; $j < count($to); $j++) {
        $relPath .= $to[$j] . '/';
    }

    return rtrim($relPath, '/');
}

// Function to include a file relative to the base path
function include_file($path) {
    $fullPath = BASE_PATH . '/' . ltrim($path, '/');
    if (file_exists($fullPath)) {
        return require_once $fullPath;
    }
    throw new Exception("File not found: $path");
}

// Firebase configuration
define('FIREBASE_PROJECT_ID', 'prorola-a2f66');
define('FIREBASE_API_KEY', 'AIzaSyAIOTTQiP22chvCkD8q4EwTBnXVNAIa5is');

/**
 * Firebase REST API Helper Class
 */
class Firebase {
    private $projectId;
    private $apiKey;
    private $accessToken;

    /**
     * Constructor
     */
    public function __construct($projectId, $apiKey) {
        $this->projectId = $projectId;
        $this->apiKey = $apiKey;

        // Log the initialization
        error_log("Firebase initialized with project ID: $projectId");

        // Check if we have a session with a token
        if (isset($_SESSION['user']) && isset($_SESSION['user']['auth_token'])) {
            $this->setAccessToken($_SESSION['user']['auth_token']);
            error_log("Using auth token from session");
        }
    }

    /**
     * Set the access token for authenticated requests
     */
    public function setAccessToken($token) {
        $this->accessToken = $token;
    }

    /**
     * Make HTTP request to Firebase Authentication
     */
    private function authRequest($endpoint, $data) {
        $url = "https://identitytoolkit.googleapis.com/v1/accounts:" . $endpoint . "?key=" . $this->apiKey;

        error_log("Firebase Auth request to: $endpoint");
        error_log("Request data: " . json_encode($data));

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);

        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);

        error_log("Auth response status: $status");
        error_log("Auth response: $response");
        if ($error) {
            error_log("Auth cURL error: $error");
        }

        curl_close($ch);

        if ($status >= 400) {
            $errorData = json_decode($response, true);
            $errorMessage = isset($errorData['error']['message']) ? $errorData['error']['message'] : 'Unknown error';
            throw new Exception("Firebase Auth request failed (HTTP $status): $response", $status);
        }

        $result = json_decode($response, true);
        if (!$result) {
            throw new Exception("Failed to decode Firebase Auth response");
        }

        // If this was a sign-in or sign-up request, store the ID token
        if (isset($result['idToken'])) {
            $this->setAccessToken($result['idToken']);
        }

        return $result;
    }

    /**
     * Make HTTP request to Firestore
     */
    private function firestoreRequest($collection, $document = null, $method = 'GET', $data = null) {
        if (!$this->accessToken) {
            error_log("No access token available. Please authenticate first.");
            throw new Exception("No access token available. Please authenticate first.");
        }

        // Check if token needs refresh (if less than 5 minutes remaining)
        if (isset($_SESSION['user']['token_expires']) && $_SESSION['user']['token_expires'] - time() < 300) {
            $this->refreshToken();
        }

        $baseUrl = "https://firestore.googleapis.com/v1/projects/{$this->projectId}/databases/(default)/documents";
        $url = $baseUrl . '/' . $collection;
        if ($document) {
            $url .= '/' . $document;
        }

        error_log("Firestore request: $method $url");
        error_log("Using access token: " . substr($this->accessToken, 0, 10) . "...");
        if ($data !== null) {
            error_log("Request data: " . json_encode($data));
        }

        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Accept: application/json',
            'Authorization: Bearer ' . $this->accessToken
        ]);

        if ($data !== null && $method !== 'DELETE') {
            // Convert PHP array to Firestore document format
            $firestoreData = [
                'fields' => $this->convertToFirestoreFields($data)
            ];
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($firestoreData));
        }

        // Add verbose debugging for DELETE requests
        if ($method === 'DELETE') {
            $verbose = fopen('php://temp', 'w+');
            curl_setopt($ch, CURLOPT_VERBOSE, true);
            curl_setopt($ch, CURLOPT_STDERR, $verbose);
        }

        $response = curl_exec($ch);
        $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);

        // Get verbose debug information for DELETE requests
        if ($method === 'DELETE') {
            rewind($verbose);
            $verboseLog = stream_get_contents($verbose);
            error_log("DELETE request - CURL verbose log: " . $verboseLog);
        }

        error_log("Firestore response status: $status");
        error_log("Firestore response: $response");
        if ($error) {
            error_log("Firestore cURL error: $error");
        }

        curl_close($ch);

        // Handle authentication errors (401)
        if ($status == 401) {
            error_log("Authentication error (401). Attempting to refresh token...");

            if ($this->refreshToken()) {
                // Retry the request with the new token
                return $this->firestoreRequest($collection, $document, $method, $data);
            } else {
                throw new Exception("Authentication failed and token refresh failed");
            }
        }

        // For DELETE requests, 200 or 204 are success
        if ($method === 'DELETE' && ($status == 200 || $status == 204)) {
            return true;
        }

        // For other requests, check for errors
        if ($status >= 400) {
            throw new Exception("Firestore request failed (HTTP $status): $response");
        }

        // For GET requests, parse the response
        if ($method === 'GET') {
            if (empty($response)) {
                throw new Exception("Empty response from Firestore");
            }

            $result = json_decode($response, true);
            if (!$result) {
                throw new Exception("Failed to decode Firestore response");
            }

            // If this is a document get, convert the Firestore format to PHP array
            if ($document) {
                if (isset($result['fields'])) {
                    return $this->convertFromFirestoreFields($result['fields']);
                } else {
                    throw new Exception("Invalid document format in response");
                }
            }

            // If this is a collection list, parse the documents
            if (isset($result['documents'])) {
                $documents = [];
                foreach ($result['documents'] as $doc) {
                    $parsedDoc = $this->parseDocument($doc);
                    if ($parsedDoc && isset($parsedDoc['id'])) {
                        // Use the Firebase document ID as the array key
                        $documents[$parsedDoc['id']] = $parsedDoc;
                    }
                }
                return $documents;
            }
        }

        // For other requests, return the response
        return json_decode($response, true);
    }

    /**
     * Convert PHP value to Firestore field format
     */
    private function convertToFirestoreValue($value) {
        if (is_null($value)) {
            return ['nullValue' => null];
        } elseif (is_bool($value)) {
            return ['booleanValue' => $value];
        } elseif (is_int($value)) {
            return ['integerValue' => (string)$value];
        } elseif (is_float($value)) {
            return ['doubleValue' => $value];
        } elseif (is_string($value)) {
            return ['stringValue' => $value];
        } elseif (is_array($value)) {
            if (array_keys($value) === range(0, count($value) - 1)) {
                // Sequential array becomes arrayValue
                return ['arrayValue' => [
                    'values' => array_map([$this, 'convertToFirestoreValue'], $value)
                ]];
            } else {
                // Associative array becomes mapValue
                return ['mapValue' => [
                    'fields' => $this->convertToFirestoreFields($value)
                ]];
            }
        }
        throw new Exception("Unsupported value type: " . gettype($value));
    }

    /**
     * Convert PHP array to Firestore fields format
     */
    private function convertToFirestoreFields($data) {
        $fields = [];
        foreach ($data as $key => $value) {
            $fields[$key] = $this->convertToFirestoreValue($value);
        }
        return $fields;
    }

    /**
     * Convert Firestore field format to PHP value
     */
    private function convertFromFirestoreValue($field) {
        $type = key($field);
        $value = current($field);

        switch ($type) {
            case 'nullValue':
                return null;
            case 'booleanValue':
                return (bool)$value;
            case 'integerValue':
                return (int)$value;
            case 'doubleValue':
                return (float)$value;
            case 'stringValue':
                return (string)$value;
            case 'timestampValue':
                return strtotime($value);
            case 'arrayValue':
                return array_map(
                    [$this, 'convertFromFirestoreValue'],
                    isset($value['values']) ? $value['values'] : []
                );
            case 'mapValue':
                return $this->convertFromFirestoreFields(
                    isset($value['fields']) ? $value['fields'] : []
                );
            case 'geoPointValue':
                return [
                    'latitude' => $value['latitude'] ?? 0,
                    'longitude' => $value['longitude'] ?? 0
                ];
            case 'referenceValue':
                $parts = explode('/', $value);
                return end($parts);
            default:
                error_log("Unknown Firestore field type: $type with value: " . json_encode($value));
                return null;
        }
    }

    /**
     * Convert Firestore fields format to PHP array
     */
    private function convertFromFirestoreFields($fields) {
        $data = [];
        foreach ($fields as $key => $field) {
            $data[$key] = $this->convertFromFirestoreValue($field);
        }
        return $data;
    }

    /**
     * Sign in with email and password
     */
    public function signInWithEmailAndPassword($email, $password) {
        return $this->authRequest('signInWithPassword', [
            'email' => $email,
            'password' => $password,
            'returnSecureToken' => true
        ]);
    }

    /**
     * Create a new user in Firebase Authentication
     */
    public function createUser($email, $password) {
        return $this->authRequest('signUp', [
            'email' => $email,
            'password' => $password,
            'returnSecureToken' => true
        ]);
    }

    /**
     * Update a user's password in Firebase Authentication
     */
    public function updatePassword($idToken, $password) {
        return $this->authRequest('update', [
            'idToken' => $idToken,
            'password' => $password,
            'returnSecureToken' => true
        ]);
    }

    /**
     * Update a user's email in Firebase Authentication
     */
    public function updateEmail($idToken, $email) {
        return $this->authRequest('update', [
            'idToken' => $idToken,
            'email' => $email,
            'returnSecureToken' => true
        ]);
    }

    /**
     * Create document in Firestore with specific ID
     */
    public function createDocument($collection, $document, $data) {
        return $this->firestoreRequest($collection, $document, 'POST', $data);
    }

    /**
     * Get document from Firestore
     */
    public function getDocument($collection, $document) {
        return $this->firestoreRequest($collection, $document);
    }

    /**
     * Set/update a document in a collection.
     * For merging, it sends a PATCH request with only the new data.
     * For non-merging (create/replace), it also uses PATCH, which acts like a PUT.
     */
    public function setDocument($collection, $document, $data, $merge = false) {
        // When merging, we simply send a PATCH request with the new data.
        // We do NOT get the old document and merge in PHP, as that is inefficient
        // and can corrupt complex data structures, which was the cause of the 500 error.
        // The PATCH verb tells Firestore to merge the fields on the server side.
        
        $result = $this->firestoreRequest($collection, $document, 'PATCH', $data);

        // A successful PATCH returns the updated document. A failed one throws an exception.
        return $result !== null;
    }

    /**
     * Delete a document from a collection
     */
    public function deleteDocument($collection, $document) {
        try {
            // Log the current user role and ID for debugging
            error_log("DELETE ATTEMPT - User info: " . json_encode([
                'user_id' => $_SESSION['user']['id'] ?? 'not set',
                'user_role' => $_SESSION['user']['role'] ?? 'not set',
                'collection' => $collection,
                'document_id' => $document
            ]));

            // Use admin service account token for deletion to avoid permission issues
            $originalToken = $this->accessToken;
            try {
                $adminToken = $this->getAdminAccessToken();
                $this->setAccessToken($adminToken);
                error_log("Using admin service account token for deletion");
            } catch (Exception $e) {
                error_log("Failed to get admin token, falling back to user token: " . $e->getMessage());
                
                // Fallback to user token if admin token fails
                if (!$this->accessToken && isset($_SESSION['user']['auth_token'])) {
                    $this->setAccessToken($_SESSION['user']['auth_token']);
                    error_log("Using auth token from session for deletion");
                }

                if (!$this->accessToken) {
                    throw new Exception("No access token available. Please authenticate first.");
                }

                // Check if token needs refresh
                if (isset($_SESSION['user']['token_expires']) && $_SESSION['user']['token_expires'] - time() < 300) {
                    $this->refreshToken();
                }
            }

            // For reports collection, try to get the document first to check ownership
            if ($collection === 'reports') {
                try {
                    $docData = $this->getDocument($collection, $document);

                    // If the document exists and has a user_id, check if it matches the current user
                    if ($docData && isset($docData['user_id']) && isset($_SESSION['user']['id']) &&
                        $docData['user_id'] !== $_SESSION['user']['id'] &&
                        ($_SESSION['user']['role'] ?? '') !== 'admin') {

                        // If the current user is not the owner and not an admin, update the user_id
                        error_log("User is not the owner of the document. Updating ownership before deletion.");
                        $docData['user_id'] = $_SESSION['user']['id'];
                        $this->setDocument($collection, $document, $docData);
                    }
                } catch (Exception $e) {
                    error_log("Error getting document before deletion: " . $e->getMessage());
                    // Continue with deletion attempt even if we couldn't get the document
                }
            }

            $result = $this->firestoreRequest($collection, $document, 'DELETE');
            
            // Restore original token after deletion
            if ($originalToken) {
                $this->setAccessToken($originalToken);
            }
            
            return $result;
        } catch (Exception $e) {
            error_log("Error deleting document: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * List documents in a collection
     */
    public function listDocuments($collection, $pageToken = null, $pageSize = 1000) {
        try {
            if (!$this->accessToken) {
                error_log("No access token available for listDocuments");
                throw new Exception("No access token available. Please authenticate first.");
            }

            $baseUrl = "https://firestore.googleapis.com/v1/projects/{$this->projectId}/databases/(default)/documents";
            $url = $baseUrl . '/' . $collection;

            // Add pagination parameters
            $queryParams = [];
            if ($pageSize > 0) {
                $queryParams['pageSize'] = $pageSize;
            }
            if ($pageToken) {
                $queryParams['pageToken'] = $pageToken;
            }

            if (!empty($queryParams)) {
                $url .= '?' . http_build_query($queryParams);
            }

            error_log("Listing documents from: $url");
            error_log("Using access token: " . substr($this->accessToken, 0, 10) . "...");

            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Accept: application/json',
                'Authorization: Bearer ' . $this->accessToken
            ]);

            $response = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);

            error_log("List documents response status: $status");
            if ($error) {
                error_log("List documents cURL error: $error");
            }

            curl_close($ch);

            // Handle authentication errors (401)
            if ($status == 401) {
                error_log("Authentication token expired or invalid");
                // Redirect to login page with expired parameter
                header('Location: ' . SITE_URL . '/pages/auth/login.php?expired=1');
                exit();
            }

            if ($status >= 400) {
                throw new Exception("List documents request failed (HTTP $status): $response");
            }

            $result = json_decode($response, true);
            if (!$result || !isset($result['documents'])) {
                error_log("No documents found or invalid response format");
                return [];
            }

            $documents = [];
            foreach ($result['documents'] as $doc) {
                $parsedDoc = $this->parseDocument($doc);
                if ($parsedDoc && isset($parsedDoc['id'])) {
                    // Use the Firebase document ID as the array key
                    $documents[$parsedDoc['id']] = $parsedDoc;
                }
            }

            // Include nextPageToken if present
            if (isset($result['nextPageToken'])) {
                $documents['_nextPageToken'] = $result['nextPageToken'];
            }

            return $documents;
        } catch (Exception $e) {
            error_log("Error listing documents: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Parse a Firestore document
     */
    private function parseDocument($doc) {
        if (!isset($doc['fields'])) {
            return null;
        }

        $data = $this->convertFromFirestoreFields($doc['fields']);

        // Extract document ID from the name field
        if (isset($doc['name'])) {
            $nameParts = explode('/', $doc['name']);
            $data['id'] = end($nameParts);
        }

        // Add metadata
        if (isset($doc['createTime'])) {
            $createTime = strtotime($doc['createTime']);
            $data['created_at'] = $createTime ?: time();
        }

        // Override with document's createdAt field if it exists (this is the actual creation time)
        if (isset($data['createdAt'])) {
            // Handle both timestamp and date objects
            if (is_array($data['createdAt']) && isset($data['createdAt']['_seconds'])) {
                // Firestore timestamp object
                $data['created_at'] = $data['createdAt']['_seconds'];
            } elseif (is_numeric($data['createdAt'])) {
                // Unix timestamp
                $data['created_at'] = $data['createdAt'];
            } elseif (is_string($data['createdAt'])) {
                // ISO string or other date format
                $timestamp = strtotime($data['createdAt']);
                if ($timestamp !== false) {
                    $data['created_at'] = $timestamp;
                }
            }
        }

        if (isset($doc['updateTime'])) {
            $updateTime = strtotime($doc['updateTime']);
            $data['updated_at'] = $updateTime ?: time();
        }

        return $data;
    }

    /**
     * Debug method to check if token is set
     */
    public function hasValidToken() {
        if (!$this->accessToken) {
            return false;
        }
        return true;
    }

    /**
     * Refresh the authentication token
     */
    private function refreshToken() {
        try {
            // Get refresh token from session
            if (!isset($_SESSION['user']['refresh_token'])) {
                throw new Exception("No refresh token available");
            }

            $url = "https://securetoken.googleapis.com/v1/token?key=" . $this->apiKey;
            $data = [
                'grant_type' => 'refresh_token',
                'refresh_token' => $_SESSION['user']['refresh_token']
            ];

            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json'
            ]);

            $response = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($status == 200) {
                $result = json_decode($response, true);
                if (isset($result['id_token']) && isset($result['expires_in'])) {
                    // Update session with new token
                    $_SESSION['user']['auth_token'] = $result['id_token'];
                    $_SESSION['user']['token_expires'] = time() + intval($result['expires_in']);
                    $this->accessToken = $result['id_token'];

                    // Update refresh token if provided
                    if (isset($result['refresh_token'])) {
                        $_SESSION['user']['refresh_token'] = $result['refresh_token'];
                    }

                    error_log("Token refreshed successfully");
                    return true;
                }
            }

            throw new Exception("Failed to refresh token: $response");
        } catch (Exception $e) {
            error_log("Token refresh error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get user data from Firebase Auth
     */
    public function getUser($uid) {
        try {
            $data = [
                'localId' => [$uid]
            ];

            $result = $this->authRequest('lookup', $data);

            if (isset($result['users']) && count($result['users']) > 0) {
                return $result['users'][0];
            } else {
                throw new Exception("User not found");
            }
        } catch (Exception $e) {
            error_log("Error getting user: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get a user by email from Firebase Authentication
     */
    public function getUserByEmail($email) {
        try {
            $data = [
                'email' => $email,
                'returnSecureToken' => true
            ];

            // Use the getAccountInfo endpoint to get user by email
            $result = $this->authRequest('signInWithPassword', $data);

            if (isset($result['localId'])) {
                // Now get the full user details
                return $this->getUser($result['localId']);
            } else {
                throw new Exception("User not found with email: $email");
            }
        } catch (Exception $e) {
            error_log("Error getting user by email: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Get user email from Firebase Auth UID
     */
    public function getFirebaseAuthEmail($uid) {
        try {
            // Check if the UID is a valid Firebase Auth UID (28 characters)
            if (strlen($uid) != 28 && preg_match('/([a-zA-Z0-9]{28})/', $uid, $matches)) {
                $uid = $matches[1];
                error_log("Extracted Firebase Auth UID from input: $uid");
            }

            $userData = $this->getUser($uid);
            error_log("Got user data for $uid: " . json_encode($userData));

            if ($userData && isset($userData['email'])) {
                error_log("Found email for user $uid: " . $userData['email']);
                return $userData['email'];
            }

            error_log("No email found for user $uid");
            return null;
        } catch (Exception $e) {
            error_log("Error getting Firebase Auth email: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Create Firebase Auth user if missing (for users that exist in Firestore but not in Firebase Auth)
     */
    public function createMissingFirebaseAuthUser($uid, $email, $name = null) {
        try {
            $adminToken = $this->getAdminAccessToken();
            if (!$adminToken) {
                error_log("Failed to get admin access token for creating Firebase Auth user");
                return false;
            }

            $url = "https://identitytoolkit.googleapis.com/v1/accounts:signUp?key=" . $this->apiKey;

            $data = [
                'localId' => $uid,
                'email' => $email,
                'emailVerified' => true, // Since they already exist in Firestore, consider them verified
                'disabled' => false
            ];

            if ($name) {
                $data['displayName'] = $name;
            }

            error_log("Creating missing Firebase Auth user: $uid ($email)");

            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $adminToken
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);

            $response = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);

            if (curl_error($ch)) {
                error_log("Firebase Auth user creation cURL error: " . curl_error($ch));
                curl_close($ch);
                return false;
            }

            curl_close($ch);

            if ($status >= 400) {
                error_log("Firebase Auth user creation error: HTTP $status - $response");
                return false;
            }

            error_log("Successfully created Firebase Auth user: $uid");
            return true;

        } catch (Exception $e) {
            error_log("Error creating Firebase Auth user: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get multiple users from Firebase Authentication with pagination to avoid rate limits
     */
    public function getUsers($uids) {
        try {
            if (empty($uids)) {
                return [];
            }

            $allUsers = [];
            $uniqueUids = array_values(array_unique($uids));
            
            // Get admin access token for Firebase Auth API
            $adminToken = $this->getAdminAccessToken();
            if (!$adminToken) {
                error_log("Failed to get admin access token for Firebase Auth API");
                return [];
            }
            
            $batchSize = 100; // Limit batch size to avoid rate limiting
            
            // Process UIDs in batches
            $batches = array_chunk($uniqueUids, $batchSize);
            
            foreach ($batches as $batch) {
                // Use the correct Firebase Admin SDK REST API endpoint
                $url = "https://identitytoolkit.googleapis.com/v1/projects/{$this->projectId}/accounts:lookup";

                $data = [
                    'localId' => $batch // Note: localId not localIds for this endpoint
                ];



                $ch = curl_init($url);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
                curl_setopt($ch, CURLOPT_HTTPHEADER, [
                    'Content-Type: application/json',
                    'Authorization: Bearer ' . $adminToken
                ]);
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);

                $response = curl_exec($ch);
                $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);

                if (curl_error($ch)) {
                    curl_close($ch);
                    continue; // Skip this batch and continue with next
                }

                curl_close($ch);

                if ($status >= 400) {
                    // Try individual user lookups instead of batch
                    foreach ($batch as $uid) {
                        $individualUrl = "https://identitytoolkit.googleapis.com/v1/projects/{$this->projectId}/accounts:lookup";
                        $individualData = ['localId' => [$uid]];
                        
                        $ch3 = curl_init($individualUrl);
                        curl_setopt($ch3, CURLOPT_RETURNTRANSFER, true);
                        curl_setopt($ch3, CURLOPT_POST, true);
                        curl_setopt($ch3, CURLOPT_POSTFIELDS, json_encode($individualData));
                        curl_setopt($ch3, CURLOPT_HTTPHEADER, [
                            'Content-Type: application/json',
                            'Authorization: Bearer ' . $adminToken
                        ]);
                        curl_setopt($ch3, CURLOPT_TIMEOUT, 30);
                        curl_setopt($ch3, CURLOPT_CONNECTTIMEOUT, 10);

                        $individualResponse = curl_exec($ch3);
                        $individualStatus = curl_getinfo($ch3, CURLINFO_HTTP_CODE);
                        curl_close($ch3);
                        
                        if ($individualStatus == 200) {
                            $individualResult = json_decode($individualResponse, true);
                            if (isset($individualResult['users'])) {
                                foreach ($individualResult['users'] as $user) {
                                    if (isset($user['localId'])) {
                                        $allUsers[$user['localId']] = $user;
                                    }
                                }
                            }
                        }
                        
                        // Small delay between individual requests
                        usleep(50000); // 50ms
                    }
                    
                    continue; // Skip to next batch
                }

                // If batch request succeeded, process normally

                $result = json_decode($response, true);
                if (isset($result['users'])) {
                    // Index users by their UID and return all Firebase Auth data
                    foreach ($result['users'] as $user) {
                        if (isset($user['localId'])) {
                            $allUsers[$user['localId']] = $user;
                        }
                    }
                }

                // Add a small delay between batches to respect rate limits
                if (count($batches) > 1) {
                    usleep(200000); // 200ms delay between batches
                }
            }

            return $allUsers;
            
        } catch (Exception $e) {
            error_log("Error getting users data: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get reference to a document in Firestore
     */
    public function getReference($collection, $document) {
        return "projects/{$this->projectId}/databases/(default)/documents/{$collection}/{$document}";
    }

    private function getDocumentPath($collection, $document) {
        return "projects/{$this->projectId}/databases/(default)/documents/{$collection}/{$document}";
    }

    /**
     * Debug function to test Firebase Authentication API
     */
    public function debugFirebaseAuth() {
        try {
            // Test with a direct API call to get all users
            $url = "https://identitytoolkit.googleapis.com/v1/accounts:lookup?key=" . $this->apiKey;

            $data = [
                'idToken' => $this->accessToken
            ];

            error_log("DEBUG: Testing Firebase Auth API with direct call");

            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json'
            ]);

            $response = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);

            error_log("DEBUG: Firebase Auth API response status: $status");
            error_log("DEBUG: Firebase Auth API response: $response");

            if ($error) {
                error_log("DEBUG: Firebase Auth API cURL error: $error");
            }

            curl_close($ch);

            return [
                'status' => $status,
                'response' => $response
            ];
        } catch (Exception $e) {
            error_log("DEBUG: Error testing Firebase Auth API: " . $e->getMessage());
            return [
                'status' => 500,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get Firestore security rules
     */
    public function getFirestoreRules() {
        try {
            if (!$this->accessToken) {
                if (isset($_SESSION['user']['auth_token'])) {
                    $this->setAccessToken($_SESSION['user']['auth_token']);
                } else {
                    throw new Exception("No access token available. Please authenticate first.");
                }
            }

            $url = "https://firebaserules.googleapis.com/v1/projects/{$this->projectId}/rulesets";

            error_log("Attempting to get Firestore rules: $url");

            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $this->accessToken,
                'Content-Type: application/json'
            ]);

            $response = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);

            curl_close($ch);

            error_log("Firestore rules response: HTTP $status - " . substr($response, 0, 200) . "...");

            if ($status >= 400) {
                error_log("Error getting Firestore rules: HTTP $status - $response");
                return null;
            }

            return json_decode($response, true);
        } catch (Exception $e) {
            error_log("Error getting Firestore rules: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Create a custom token for a user
     */
    public function createCustomToken($uid) {
        try {
            // For security reasons, this should be done on a server with Firebase Admin SDK
            // This is a simplified version for demonstration purposes

            // In a real implementation, you would use the Firebase Admin SDK
            // to create a custom token with the appropriate claims

            // For now, we'll use a workaround by creating a temporary user in Firebase Auth
            // and then signing in with that user

            error_log("Attempting to create custom token for user: $uid");

            // Generate a random password
            $tempPassword = bin2hex(random_bytes(8));

            // Try to find if this user already exists in Firebase Auth
            $existingUser = null;
            try {
                $existingUser = $this->getUser($uid);
            } catch (Exception $e) {
                error_log("User not found in Firebase Auth, will create: " . $e->getMessage());
            }

            if (!$existingUser) {
                // Create a new user in Firebase Auth
                $email = "user_$<EMAIL>";
                $this->createUser($email, $tempPassword);
                error_log("Created temporary user in Firebase Auth: $email");
            }

            // Return the UID as the custom token (this is a simplification)
            return $uid;
        } catch (Exception $e) {
            error_log("Error creating custom token: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Exchange a custom token for an ID token
     */
    public function exchangeCustomTokenForIdToken($customToken) {
        try {
            // In a real implementation, you would use the Firebase Auth REST API
            // to exchange the custom token for an ID token

            // For now, we'll use a workaround by signing in with email/password
            // for the temporary user we created

            $email = "user_$<EMAIL>";
            $tempPassword = "password"; // This should be stored securely

            error_log("Attempting to exchange custom token for ID token: $customToken");

            // Sign in with the temporary user
            $authResult = $this->signInWithEmailAndPassword($email, $tempPassword);

            if (isset($authResult['idToken'])) {
                error_log("Successfully exchanged custom token for ID token");
                return $authResult;
            } else {
                error_log("Failed to exchange custom token for ID token");
                return null;
            }
        } catch (Exception $e) {
            error_log("Error exchanging custom token: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Update Firestore security rules
     */
    public function updateFirestoreRules($rules) {
        try {
            if (!$this->accessToken) {
                if (isset($_SESSION['user']['auth_token'])) {
                    $this->setAccessToken($_SESSION['user']['auth_token']);
                } else {
                    throw new Exception("No access token available. Please authenticate first.");
                }
            }

            // First, we need to create a ruleset
            $url = "https://firebaserules.googleapis.com/v1/projects/{$this->projectId}/rulesets";

            error_log("Creating new ruleset: $url");

            // Format the rules for the API
            $rulesetData = [
                "source" => [
                    "files" => [
                        [
                            "name" => "firestore.rules",
                            "content" => json_encode($rules)
                        ]
                    ]
                ]
            ];

            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($rulesetData));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $this->accessToken,
                'Content-Type: application/json'
            ]);

            $response = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);

            curl_close($ch);

            error_log("Ruleset creation response: HTTP $status - " . substr($response, 0, 200) . "...");

            if ($status >= 400) {
                error_log("Error creating ruleset: HTTP $status - $response");
                throw new Exception("Failed to create ruleset: HTTP $status - $response");
            }

            $rulesetResponse = json_decode($response, true);
            $rulesetName = $rulesetResponse['name'];

            // Now, release the ruleset
            $releaseUrl = "https://firebaserules.googleapis.com/v1/projects/{$this->projectId}/releases/firestore.rules";

            error_log("Releasing ruleset: $releaseUrl");

            $releaseData = [
                "ruleset" => $rulesetName
            ];

            $ch = curl_init($releaseUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PATCH");
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($releaseData));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $this->accessToken,
                'Content-Type: application/json'
            ]);

            $response = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);

            curl_close($ch);

            error_log("Release response: HTTP $status - " . substr($response, 0, 200) . "...");

            if ($status >= 400) {
                error_log("Error releasing ruleset: HTTP $status - $response");
                throw new Exception("Failed to release ruleset: HTTP $status - $response");
            }

            return json_decode($response, true);
        } catch (Exception $e) {
            error_log("Error updating Firestore rules: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete a file from Firebase Storage using REST API
     */
    private function deleteStorageFile($filePath) {
        try {
            // Use admin token for Storage operations
            $adminToken = $this->getAdminAccessToken();
            if (!$adminToken) {
                throw new Exception("No admin access token available for storage deletion.");
            }

            // URL encode the file path for Firebase Storage REST API
            $encodedPath = urlencode($filePath);
            $storageBucket = $this->projectId . '.firebasestorage.app'; // Use consistent bucket format
            $url = "https://firebasestorage.googleapis.com/v0/b/{$storageBucket}/o/{$encodedPath}";

            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $adminToken,
                'Content-Type: application/json'
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);

            $response = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($status === 204) {
                return true;
            } else {
                error_log("Storage delete failed - Status: $status, Response: $response");
                return false;
            }

        } catch (Exception $e) {
            error_log("Storage file deletion error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Extract file path from Firebase Storage URL
     */
    public function extractStoragePathFromUrl($url) {
        try {
            // Firebase Storage URLs have format:
            // https://firebasestorage.googleapis.com/v0/b/bucket/o/path%2Fto%2Ffile?alt=media&token=...
            $matches = [];
            if (preg_match('/\/o\/(.*?)\?/', $url, $matches)) {
                // Decode the URL-encoded path
                return urldecode($matches[1]);
            } else {
                throw new Exception("Could not parse Firebase Storage URL: $url");
            }
        } catch (Exception $e) {
            error_log("Error extracting storage path from URL: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Delete all images associated with a report
     */
    public function deleteReportImages($images) {
        $deletedCount = 0;
        $errors = [];
        $skippedCount = 0;

        if (!$images || !is_array($images)) {
            return ['deleted' => 0, 'errors' => [], 'skipped' => 0];
        }

        foreach ($images as $imageUrl) {
            try {
                // Skip local file URLs (they're not in Firebase Storage)
                if (strpos($imageUrl, 'file://') === 0 || strpos($imageUrl, '/data/user/') !== false) {
                    error_log("Skipping local file URL: $imageUrl");
                    $skippedCount++;
                    continue;
                }

                // Skip non-Firebase Storage URLs
                if (strpos($imageUrl, 'firebasestorage.googleapis.com') === false) {
                    error_log("Skipping non-Firebase Storage URL: $imageUrl");
                    $skippedCount++;
                    continue;
                }

                $filePath = $this->extractStoragePathFromUrl($imageUrl);
                $this->deleteStorageFile($filePath);
                $deletedCount++;
                error_log("Successfully deleted image: $imageUrl");
            } catch (Exception $e) {
                $errorMsg = "Failed to delete image $imageUrl: " . $e->getMessage();
                $errors[] = $errorMsg;
                error_log($errorMsg);
            }
        }

        error_log("Image deletion summary: deleted=$deletedCount, errors=" . count($errors) . ", skipped=$skippedCount");

        return [
            'deleted' => $deletedCount,
            'errors' => $errors,
            'skipped' => $skippedCount
        ];
    }

    /**
     * Delete a specific user's profile image from Firebase Storage
     */
    public function deleteUserProfileImage($userId) {
        try {
            // Use admin token for Storage operations (like deleteAuthUser does)
            $adminToken = $this->getAdminAccessToken();
            if (!$adminToken) {
                throw new Exception("No admin access token available.");
            }

            $deletedCount = 0;
            $errors = [];

            // List all files in the user's profile_images directory
            $url = "https://firebasestorage.googleapis.com/v0/b/" . $this->projectId . ".firebasestorage.app/o?prefix=profile_images/$userId/";

            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Accept: application/json',
                'Authorization: Bearer ' . $adminToken
            ]);

            $response = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($status === 200) {
                $data = json_decode($response, true);

                if (isset($data['items']) && is_array($data['items'])) {
                    foreach ($data['items'] as $item) {
                        if (isset($item['name'])) {
                            $filePath = $item['name'];
                            if ($this->deleteStorageFile($filePath)) {
                                $deletedCount++;
                            } else {
                                $errors[] = "Failed to delete: $filePath";
                            }
                        }
                    }
                }
            } else if ($status === 404) {
                // No profile images directory found - this is normal for users without profile images
            } else {
                error_log("Failed to list profile images - HTTP $status: $response");
            }

            return [
                'deleted' => $deletedCount,
                'errors' => $errors
            ];

        } catch (Exception $e) {
            error_log("Profile image deletion error: " . $e->getMessage());
            return [
                'deleted' => 0,
                'errors' => [$e->getMessage()]
            ];
        }
    }

    /**
     * Delete all profile images from Firebase Storage
     */
    public function deleteAllProfileImages() {
        $deletedCount = 0;
        $errors = [];

        try {
            if (!$this->accessToken) {
                throw new Exception("No access token available. Please authenticate first.");
            }

            // List all files in the profile_images directory
            $url = "https://firebasestorage.googleapis.com/v0/b/" . $this->projectId . ".firebasestorage.app/o?prefix=profile_images/";

            error_log("Listing profile images: $url");

            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Authorization: Bearer ' . $this->accessToken
            ]);
            curl_setopt($ch, CURLOPT_TIMEOUT, 30); // Add timeout
            curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10); // Add connection timeout

            $response = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);

            if ($curlError) {
                throw new Exception("CURL error: $curlError");
            }

            if ($status >= 400) {
                if ($status == 404) {
                    error_log("No profile_images directory found (404), nothing to delete");
                    return ['deleted' => 0, 'errors' => []];
                }
                throw new Exception("Failed to list profile images: HTTP $status - $response");
            }

            if (empty($response)) {
                error_log("Empty response when listing profile images");
                return ['deleted' => 0, 'errors' => []];
            }

            $data = json_decode($response, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception("Invalid JSON response: " . json_last_error_msg());
            }

            if (isset($data['items']) && is_array($data['items'])) {
                error_log("Found " . count($data['items']) . " profile images to delete");

                foreach ($data['items'] as $item) {
                    try {
                        if (!isset($item['name'])) {
                            continue;
                        }

                        $filePath = $item['name'];
                        $this->deleteStorageFile($filePath);
                        $deletedCount++;
                        error_log("Successfully deleted profile image: $filePath");
                    } catch (Exception $e) {
                        $errorMsg = "Failed to delete profile image {$item['name']}: " . $e->getMessage();
                        $errors[] = $errorMsg;
                        error_log($errorMsg);
                    }
                }
            } else {
                error_log("No profile images found to delete (no items array)");
            }

        } catch (Exception $e) {
            $errorMsg = "Error listing profile images: " . $e->getMessage();
            $errors[] = $errorMsg;
            error_log($errorMsg);
        }

        error_log("Profile images deletion summary: deleted=$deletedCount, errors=" . count($errors));

        return [
            'deleted' => $deletedCount,
            'errors' => $errors
        ];
    }

    /**
     * Delete Firebase Authentication user
     */
    public function deleteAuthUser($uid) {
        try {
            // Get admin access token for elevated permissions
            $adminToken = $this->getAdminAccessToken();

            // Use Firebase Admin API to delete user
            $url = "https://identitytoolkit.googleapis.com/v1/projects/{$this->projectId}/accounts:delete";

            $data = [
                'localId' => $uid
            ];

            $ch = curl_init($url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $adminToken
            ]);

            $response = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);

            curl_close($ch);

            if ($error) {
                throw new Exception("cURL error: $error");
            }

            if ($status >= 400) {
                $errorData = json_decode($response, true);
                $errorMessage = isset($errorData['error']['message']) ? $errorData['error']['message'] : 'Unknown error';
                throw new Exception("Failed to delete Firebase Auth user (HTTP $status): $errorMessage");
            }

            return true;

        } catch (Exception $e) {
            throw $e;
        }
    }

    /**
     * Get admin access token using service account for elevated permissions
     */
    public function getAdminAccessToken() {
        try {
            // Try to load service account credentials from environment variables first
            $serviceAccount = null;

            // Check if environment variables are set
            $envProjectId = $_ENV['FIREBASE_PROJECT_ID'] ?? getenv('FIREBASE_PROJECT_ID');
            $envPrivateKey = $_ENV['FIREBASE_PRIVATE_KEY'] ?? getenv('FIREBASE_PRIVATE_KEY');
            $envClientEmail = $_ENV['FIREBASE_CLIENT_EMAIL'] ?? getenv('FIREBASE_CLIENT_EMAIL');

            if ($envProjectId && $envPrivateKey && $envClientEmail) {
                // Use environment variables (most secure)
                $serviceAccount = [
                    'type' => 'service_account',
                    'project_id' => $envProjectId,
                    'private_key_id' => $_ENV['FIREBASE_PRIVATE_KEY_ID'] ?? getenv('FIREBASE_PRIVATE_KEY_ID'),
                    'private_key' => str_replace('\\n', "\n", $envPrivateKey), // Handle escaped newlines
                    'client_email' => $envClientEmail,
                    'client_id' => $_ENV['FIREBASE_CLIENT_ID'] ?? getenv('FIREBASE_CLIENT_ID'),
                    'auth_uri' => $_ENV['FIREBASE_AUTH_URI'] ?? getenv('FIREBASE_AUTH_URI') ?? 'https://accounts.google.com/o/oauth2/auth',
                    'token_uri' => $_ENV['FIREBASE_TOKEN_URI'] ?? getenv('FIREBASE_TOKEN_URI') ?? 'https://oauth2.googleapis.com/token',
                    'auth_provider_x509_cert_url' => $_ENV['FIREBASE_AUTH_PROVIDER_CERT_URL'] ?? getenv('FIREBASE_AUTH_PROVIDER_CERT_URL') ?? 'https://www.googleapis.com/oauth2/v1/certs',
                    'client_x509_cert_url' => $_ENV['FIREBASE_CLIENT_CERT_URL'] ?? getenv('FIREBASE_CLIENT_CERT_URL'),
                    'universe_domain' => $_ENV['FIREBASE_UNIVERSE_DOMAIN'] ?? getenv('FIREBASE_UNIVERSE_DOMAIN') ?? 'googleapis.com'
                ];

                error_log("Using Firebase credentials from environment variables");
                         } else {
                 // Fallback to JSON file for development/testing
                 $serviceAccountPath = dirname(__DIR__) . '/config/prorola-a2f66-firebase-adminsdk-fbsvc-634f31a42f.json';

                 // Also try the old location as fallback
                 if (!file_exists($serviceAccountPath)) {
            $serviceAccountPath = __DIR__ . '/prorola-a2f66-firebase-adminsdk-fbsvc-634f31a42f.json';
                 }

            if (!file_exists($serviceAccountPath)) {
                     throw new Exception("Neither environment variables nor service account file found. Please set FIREBASE_PROJECT_ID, FIREBASE_PRIVATE_KEY, and FIREBASE_CLIENT_EMAIL environment variables, or place the service account file at: " . dirname(__DIR__) . '/config/prorola-a2f66-firebase-adminsdk-fbsvc-634f31a42f.json');
            }

            $serviceAccount = json_decode(file_get_contents($serviceAccountPath), true);
            if (!$serviceAccount) {
                throw new Exception("Failed to parse service account file");
                }

                error_log("Using Firebase credentials from JSON file: " . $serviceAccountPath);
            }

            // Create JWT token for service account authentication
            $now = time();
            $header = [
                'alg' => 'RS256',
                'typ' => 'JWT'
            ];

            $payload = [
                'iss' => $serviceAccount['client_email'],
                'scope' => 'https://www.googleapis.com/auth/firebase https://www.googleapis.com/auth/identitytoolkit https://www.googleapis.com/auth/cloud-platform',
                'aud' => 'https://oauth2.googleapis.com/token',
                'iat' => $now,
                'exp' => $now + 3600 // 1 hour
            ];

            // Simple JWT creation (for production, consider using a proper JWT library)
            $headerEncoded = base64url_encode(json_encode($header));
            $payloadEncoded = base64url_encode(json_encode($payload));
            $signature = '';

            // Create signature using RS256
            $data = $headerEncoded . '.' . $payloadEncoded;
            openssl_sign($data, $signature, $serviceAccount['private_key'], OPENSSL_ALGO_SHA256);
            $signatureEncoded = base64url_encode($signature);

            $jwt = $data . '.' . $signatureEncoded;

            // Exchange JWT for access token
            $tokenUrl = 'https://oauth2.googleapis.com/token';
            $postData = [
                'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
                'assertion' => $jwt
            ];

            $ch = curl_init($tokenUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/x-www-form-urlencoded'
            ]);

            $response = curl_exec($ch);
            $status = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);

            if ($status !== 200) {
                throw new Exception("Failed to get admin access token: HTTP $status - $response");
            }

            $tokenData = json_decode($response, true);
            if (!isset($tokenData['access_token'])) {
                throw new Exception("No access token in response: $response");
            }

            return $tokenData['access_token'];

        } catch (Exception $e) {
            error_log("Error getting admin access token: " . $e->getMessage());
            throw $e;
        }
    }
}

// Initialize Firebase
$database = new Firebase(FIREBASE_PROJECT_ID, FIREBASE_API_KEY);

// Application settings
define('ADMIN_EMAIL', '<EMAIL>');

// Time settings
date_default_timezone_set('Europe/Lisbon');

// Utility functions
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

function format_date($timestamp) {
    return date('d/m/Y H:i', $timestamp);
}

// Error handler
function custom_error_handler($errno, $errstr, $errfile, $errline) {
    $error_message = date('Y-m-d H:i:s') . " Error [$errno]: $errstr in $errfile on line $errline\n";
    error_log($error_message); // This will log to PHP's default error log

    if (ini_get('display_errors')) {
        printf("<pre>%s</pre>", htmlspecialchars($error_message));
    }

    return true;
}

set_error_handler('custom_error_handler');

/**
 * Geocode coordinates to address with permanent caching
 */
function geocode_coordinates($latitude, $longitude) {
    // Create cache directory if it doesn't exist
    $cacheDir = __DIR__ . '/../cache/geocoding';
    if (!file_exists($cacheDir)) {
        mkdir($cacheDir, 0755, true);
    }

    // Create cache key from coordinates
    $cacheKey = md5($latitude . '_' . $longitude);
    $cacheFile = $cacheDir . '/' . $cacheKey . '.json';

    // Check if we have a cached result
    if (file_exists($cacheFile)) {
        $cachedData = file_get_contents($cacheFile);
        $geocodeData = json_decode($cachedData, true);

        // Use cached data if it exists (no expiration)
        if ($geocodeData) {
            return $geocodeData;
        }
    }

    // No valid cache, make API request
    try {
        $geocodeUrl = "https://nominatim.openstreetmap.org/reverse?format=json&lat=$latitude&lon=$longitude&zoom=18&addressdetails=1";
        $ch = curl_init($geocodeUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_USERAGENT, 'ProROLA Admin');
        $response = curl_exec($ch);
        curl_close($ch);

        $geocodeData = json_decode($response, true);

        // Cache the result permanently
        if ($geocodeData) {
            file_put_contents($cacheFile, json_encode($geocodeData));
        }

        return $geocodeData;
    } catch (Exception $e) {
        error_log("Geocoding error: " . $e->getMessage());
        return null;
    }
}

/**
 * Get short location name from coordinates
 */
function get_short_location($latitude, $longitude) {
    $geocodeData = geocode_coordinates($latitude, $longitude);

    if ($geocodeData && isset($geocodeData['address'])) {
        // Get city/town/village
        if (!empty($geocodeData['address']['city'])) {
            return $geocodeData['address']['city'];
        } elseif (!empty($geocodeData['address']['town'])) {
            return $geocodeData['address']['town'];
        } elseif (!empty($geocodeData['address']['village'])) {
            return $geocodeData['address']['village'];
        } elseif (!empty($geocodeData['address']['municipality'])) {
            return $geocodeData['address']['municipality'];
        } elseif (!empty($geocodeData['address']['county'])) {
            return $geocodeData['address']['county'];
        }
    }

    // If we have a display name, extract just the main part (before any parentheses or commas)
    if ($geocodeData && isset($geocodeData['display_name'])) {
        // For complex Portuguese location names, extract just the first part
        if (strpos($geocodeData['display_name'], ',') !== false) {
            $parts = explode(',', $geocodeData['display_name']);
            if (!empty($parts[0])) {
                // Remove anything in parentheses
                $mainPart = preg_replace('/\s*\([^)]*\)/', '', $parts[0]);
                // Also remove anything after "e" or "e " (for Portuguese locations that list multiple places)
                $mainPart = preg_replace('/\s+e\s+.*$/', '', $mainPart);
                return trim($mainPart);
            }
        }

        // For locations without commas, just remove parentheses
        $mainPart = preg_replace('/\s*\([^)]*\)/', '', $geocodeData['display_name']);
        $mainPart = preg_replace('/\s+e\s+.*$/', '', $mainPart);
        return trim($mainPart);
    }

    // Fallback to coordinates in WGS84 format
    return number_format($latitude, 8, '.', '') . ", " . number_format($longitude, 8, '.', '');
}

/**
 * Get detailed location from coordinates
 */
function get_detailed_location($latitude, $longitude) {
    $geocodeData = geocode_coordinates($latitude, $longitude);

    if ($geocodeData && isset($geocodeData['address'])) {
        // Create a shorter address format
        $addressParts = [];

        // Add road/street if available
        if (!empty($geocodeData['address']['road'])) {
            $addressParts[] = $geocodeData['address']['road'];
        }

        // Add city/town/village
        if (!empty($geocodeData['address']['city'])) {
            $addressParts[] = $geocodeData['address']['city'];
        } elseif (!empty($geocodeData['address']['town'])) {
            $addressParts[] = $geocodeData['address']['town'];
        } elseif (!empty($geocodeData['address']['village'])) {
            $addressParts[] = $geocodeData['address']['village'];
        }

        // Add municipality if available and different from city
        if (!empty($geocodeData['address']['municipality']) &&
            !in_array($geocodeData['address']['municipality'], $addressParts)) {
            $addressParts[] = $geocodeData['address']['municipality'];
        }

        // Combine parts into a short address
        if (!empty($addressParts)) {
            return implode(', ', $addressParts);
        }
    }

    // Fallback to coordinates (WGS84 format)
    return "Lat: " . number_format($latitude, 8, '.', '') . ", Long: " . number_format($longitude, 8, '.', '');
}

/**
 * Get full location details from coordinates
 */
function get_full_location($latitude, $longitude) {
    $geocodeData = geocode_coordinates($latitude, $longitude);

    if ($geocodeData && isset($geocodeData['address'])) {
        // Create a full address format
        $addressParts = [];

        // Add road/street if available
        if (!empty($geocodeData['address']['road'])) {
            $addressParts[] = $geocodeData['address']['road'];
        }

        // Add suburb/neighborhood if available
        if (!empty($geocodeData['address']['suburb'])) {
            $addressParts[] = $geocodeData['address']['suburb'];
        }

        // Add city/town/village
        if (!empty($geocodeData['address']['city'])) {
            $addressParts[] = $geocodeData['address']['city'];
        } elseif (!empty($geocodeData['address']['town'])) {
            $addressParts[] = $geocodeData['address']['town'];
        } elseif (!empty($geocodeData['address']['village'])) {
            $addressParts[] = $geocodeData['address']['village'];
        }

        // Add municipality if available and different from city
        if (!empty($geocodeData['address']['municipality']) &&
            !in_array($geocodeData['address']['municipality'], $addressParts)) {
            $addressParts[] = $geocodeData['address']['municipality'];
        }

        // Add county/district
        if (!empty($geocodeData['address']['county'])) {
            $addressParts[] = $geocodeData['address']['county'];
        }

        // Add state/province
        if (!empty($geocodeData['address']['state'])) {
            $addressParts[] = $geocodeData['address']['state'];
        }

        // Add country
        if (!empty($geocodeData['address']['country'])) {
            $addressParts[] = $geocodeData['address']['country'];
        }

        // Combine parts into a full address
        if (!empty($addressParts)) {
            return implode(', ', $addressParts);
        }
    }

    // Fallback to coordinates
    return "Lat: $latitude, Long: $longitude";
}

/**
 * Base64 URL encode function for JWT
 */
function base64url_encode($data) {
    return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
}

// Initialize Firebase database instance
global $database;
$database = new Firebase(FIREBASE_PROJECT_ID, FIREBASE_API_KEY);