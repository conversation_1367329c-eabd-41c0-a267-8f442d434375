<?php
// Ensure this file is included, not accessed directly
if (!defined('SITE_NAME')) {
    exit('No direct script access allowed');
}
?>

<!-- Logout Confirmation Modal -->
<div class="modal" id="logoutModal" style="display: none !important; visibility: hidden !important; opacity: 0 !important;">
    <div class="modal-dialog">
        <div class="modal-content logout-modal">
            <div class="modal-header">
                <div class="modal-title-container">
                    <div class="modal-icon">
                        <i class="fas fa-sign-out-alt"></i>
                    </div>
                    <h5 class="modal-title">Terminar Sessão</h5>
                </div>
                <button type="button" class="close" onclick="closeLogoutModal()">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="logout-message">
                    <p>Tem a certeza de que pretende terminar a sessão?</p>
                    <p class="logout-subtitle">Será redirecionado para a página de login.</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeLogoutModal()">
                    <i class="fas fa-times"></i>
                    Cancelar
                </button>
                <button type="button" class="btn btn-danger" onclick="confirmLogout()">
                    <i class="fas fa-sign-out-alt"></i>
                    Terminar Sessão
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* Logout Modal Styling */
.logout-modal {
    border-radius: 16px;
    border: none;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
}

.logout-modal .modal-header {
    background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
    color: white;
    border-bottom: none;
    padding: 1.5rem 2rem;
    position: relative;
    display: flex;
    align-items: center;
}

.logout-modal .modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.modal-title-container {
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    z-index: 1;
    width: 100%;
}

.logout-modal .modal-icon {
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.logout-modal .modal-icon i {
    font-size: 1.5rem;
    color: white;
}

.logout-modal .modal-title {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    flex: 1;
    text-align: center;
    margin-right: 48px; /* Compensate for close button width */
}

.logout-modal .close {
    position: absolute;
    top: 1rem;
    right: 1.5rem;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    z-index: 2;
}

.logout-modal .close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.logout-modal .modal-body {
    padding: 2rem;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.logout-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    width: 100%;
}

.logout-message p {
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
    color: #374151;
    font-weight: 500;
    white-space: nowrap;
    text-align: center;
}

.logout-subtitle {
    font-size: 0.95rem !important;
    color: #6b7280 !important;
    font-weight: 400 !important;
    white-space: nowrap;
    text-align: center;
}

.logout-modal .modal-footer {
    padding: 1.5rem 2rem;
    background: #f8fafc;
    border-top: 1px solid #e5e7eb;
    display: flex;
    gap: 1rem;
    justify-content: center;
    align-items: center;
}

.logout-modal .btn {
    padding: 0.75rem 1.5rem;
    border-radius: 10px;
    font-weight: 600;
    font-size: 0.95rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    min-width: 140px;
    justify-content: center;
}

.logout-modal .btn-secondary {
    background: #6b7280;
    color: white;
    box-shadow: 0 4px 12px rgba(107, 114, 128, 0.3);
}

.logout-modal .btn-secondary:hover {
    background: #4b5563;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(107, 114, 128, 0.4);
}

.logout-modal .btn-danger {
    background: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.logout-modal .btn-danger:hover {
    background: linear-gradient(135deg, #b91c1c 0%, #dc2626 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220, 38, 38, 0.4);
}

/* Modal backdrop - Override any existing modal styles */
#logoutModal.modal {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.5) !important;
    backdrop-filter: blur(4px);
    z-index: -1 !important; /* Hidden by default */
    display: none !important; /* Completely hidden by default */
    align-items: center !important;
    justify-content: center !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    margin: 0 !important;
    padding: 0 !important;
    pointer-events: none !important; /* Don't block clicks when hidden */
}

#logoutModal.modal[style*="flex"],
#logoutModal.modal[style*="block"],
#logoutModal.modal.show {
    display: flex !important; /* Show as flex when active */
    opacity: 1 !important;
    visibility: visible !important;
    z-index: 9999 !important; /* Bring to front when shown */
    pointer-events: auto !important; /* Enable clicks when shown */
}

#logoutModal .modal-dialog {
    position: relative !important;
    margin: 0 !important;
    max-width: 520px !important;
    width: 95% !important;
    min-width: 400px !important;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

#logoutModal.modal[style*="block"] .modal-dialog {
    transform: scale(1) !important;
}

/* Override Bootstrap modal styles completely */
#logoutModal.modal.show,
#logoutModal.modal[style*="block"] {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

#logoutModal .modal-dialog {
    position: static !important;
    display: block !important;
    margin: 0 auto !important;
    vertical-align: middle !important;
}

/* Ensure modal is always centered */
#logoutModal {
    text-align: center;
}

#logoutModal .modal-dialog {
    display: inline-block;
    text-align: left;
    vertical-align: middle;
}

#logoutModal:before {
    content: '';
    display: inline-block;
    height: 100%;
    vertical-align: middle;
    margin-right: -0.25em;
}

/* Responsive design */
@media (max-width: 480px) {
    .logout-modal .modal-header {
        padding: 1.25rem 1.5rem;
    }
    
    .logout-modal .modal-body {
        padding: 1.5rem;
    }
    
    .logout-modal .modal-footer {
        padding: 1.25rem 1.5rem;
        flex-direction: column;
    }
    
    .logout-modal .btn {
        width: 100%;
        min-width: auto;
    }
    
    .modal-title-container {
        gap: 0.75rem;
    }
    
    .logout-modal .modal-icon {
        width: 40px;
        height: 40px;
    }
    
    .logout-modal .modal-icon i {
        font-size: 1.25rem;
    }
    
    .logout-modal .modal-title {
        font-size: 1.25rem;
    }
    
    #logoutModal .modal-dialog {
        width: 95% !important;
        margin: 1rem !important;
        min-width: 320px !important;
    }
    
    .logout-message p,
    .logout-subtitle {
        white-space: normal !important;
        word-wrap: break-word;
    }
}
</style>

<script>
function showLogoutModal() {
    const modal = document.getElementById('logoutModal');
    modal.classList.add('show');
    modal.style.display = 'flex';
    modal.style.visibility = 'visible';
    modal.style.opacity = '1';
    modal.style.zIndex = '9999';
    modal.style.pointerEvents = 'auto';
    
    // Prevent body scroll
    document.body.style.overflow = 'hidden';
    
    // Focus trap
    const focusableElements = modal.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];
    
    if (firstElement) {
        firstElement.focus();
    }
    
    // Handle escape key
    function handleEscape(e) {
        if (e.key === 'Escape') {
            closeLogoutModal();
        }
    }
    
    document.addEventListener('keydown', handleEscape);
    modal.setAttribute('data-escape-handler', 'true');
}

function closeLogoutModal() {
    const modal = document.getElementById('logoutModal');
    modal.classList.remove('show');
    modal.style.display = 'none';
    modal.style.visibility = 'hidden';
    modal.style.opacity = '0';
    modal.style.zIndex = '-1';
    modal.style.pointerEvents = 'none';
    
    // Restore body scroll
    document.body.style.overflow = '';
    
    // Remove escape key handler
    if (modal.getAttribute('data-escape-handler')) {
        document.removeEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeLogoutModal();
            }
        });
        modal.removeAttribute('data-escape-handler');
    }
}

function confirmLogout() {
    // Add loading state to button
    const logoutBtn = document.querySelector('.btn-danger');
    const originalContent = logoutBtn.innerHTML;
    logoutBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> A terminar...';
    logoutBtn.disabled = true;
    
    // Redirect to logout
    setTimeout(() => {
        window.location.href = '<?php echo SITE_URL; ?>/pages/auth/logout.php';
    }, 500);
}

// Close modal when clicking outside
document.addEventListener('click', function(e) {
    const modal = document.getElementById('logoutModal');
    if (e.target === modal) {
        closeLogoutModal();
    }
});
</script> 