<?php
// Ensure this file is included, not accessed directly
if (!defined('SITE_NAME')) {
    exit('No direct script access allowed');
}
?>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<header class="header">
    <div class="header-content">
        <h1 class="page-title">
            <?php
            $current_page = basename(dirname($_SERVER['PHP_SELF']));
            $current_file = basename($_SERVER['PHP_SELF']);
            
            switch ($current_page) {
                case 'dashboard':
                    echo '<i class="fas fa-chart-line"></i> Painel de Controlo';
                    break;
                case 'reports':
                    echo '<i class="fas fa-file-alt"></i> Relatórios';
                    break;
                case 'map':
                    echo '<i class="fas fa-map-marked-alt"></i> Mapa';
                    break;
                case 'zonas-caca':
                    echo '<i class="fas fa-map-marker-alt"></i> <PERSON><PERSON><PERSON>';
                    break;
                case 'system':
                    switch ($current_file) {
                        case 'messages.php':
                            echo '<i class="fas fa-bullhorn"></i> Mensagens do Sistema';
                            break;
                        default:
                            echo '<i class="fas fa-cogs"></i> Sistema';
                    }
                    break;
                case 'users':
                    switch ($current_file) {
                        case 'administrators.php':
                            echo '<i class="fas fa-users"></i> Utilizadores - <i class="fas fa-user-shield"></i> Administradores';
                            break;
                        case 'tecnicos_prorola.php':
                            echo '<i class="fas fa-users"></i> Utilizadores - <i class="fas fa-user-cog"></i> Técnicos ProROLA';
                            break;
                        case 'colaboradores.php':
                            echo '<i class="fas fa-users"></i> Utilizadores - <i class="fas fa-users"></i> Colaboradores';
                            break;
                        case 'zonas_de_caca.php':
                            echo '<i class="fas fa-users"></i> Utilizadores - <i class="fas fa-binoculars"></i> Zona de Caça';
                            break;
                        default:
                            echo '<i class="fas fa-users"></i> Utilizadores';
                    }
                    break;
                default:
                    echo SITE_NAME;
            }
            ?>
        </h1>
    </div>
</header>

<?php
// Include the profile modal
require_once __DIR__ . '/profile_modal.php';

// Include the logout modal
require_once __DIR__ . '/logout_modal.php';
?> 