/* Global styles */
:root {
    --primary-color: #0a7ea4;
    --secondary-color: #687076;
    --accent-color: #0a7ea4;
    --text-color: #11181C;
    --light-gray: #f5f6fa;
    --border-color: #ddd;
    --background-color: #fff;
    --icon-color: #687076;
    --icon-selected: #0a7ea4;
    --danger-color: #dc2626;
    --success-color: #16a34a;
}

body {
    margin: 0;
    padding: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    background-color: var(--light-gray);
    color: var(--text-color);
}

/* Layout */
.header {
    background-color: var(--background-color);
    padding: 0.5rem 1.25rem;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    position: fixed;
    top: 0;
    right: 0;
    left: 220px;
    z-index: 1000;
    height: 40px;
    display: flex;
    align-items: center;
    transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    -moz-transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    -o-transition: left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.header.sidebar-collapsed {
    left: 60px !important;
}

.header-content {
    width: 100%;
}

.page-title {
    margin: 0;
    font-size: 1.25rem;
    color: var(--text-color);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.page-title i {
    color: var(--icon-selected);
    font-size: 1.125rem;
}

.sidebar {
    width: 220px;
    background: linear-gradient(180deg, var(--primary-color) 0%, #07a0c1 100%);
    color: white;
    position: fixed;
    top: 0;
    left: 0;
    bottom: 0;
    z-index: 1000;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
    transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    -moz-transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    -o-transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar::-webkit-scrollbar {
    width: 4px;
}

.sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

.sidebar-header {
    padding: 1rem 1rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
    backdrop-filter: blur(10px);
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.15);
    position: relative;
}

.sidebar-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 20px;
    right: 20px;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
}

.logo-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 0.75rem;
    background: white;
    border-radius: 50%;
    width: 100px;
    height: 100px;
    margin: 0 auto 0.75rem;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.logo-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.sidebar-logo {
    width: 100%;
    height: 100%;
    object-fit: contain;
    display: block;
}

.sidebar-header h2 {
    margin: 0;
    font-size: 1.25rem;
    color: white;
    font-weight: 600;
}

.user-name {
    font-size: 0.875rem;
    font-weight: 500;
    margin-top: 0.25rem;
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.sidebar-nav {
    padding: 1rem 0;
}

.sidebar-nav ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.sidebar-nav li {
    margin: 0 0.75rem 0.25rem;
    padding: 0;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    padding: 0.875rem 1rem;
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 12px;
    position: relative;
    overflow: hidden;
}

.sidebar-nav a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.sidebar-nav a i {
    margin-right: 0.875rem;
    font-size: 1.125rem;
    width: 20px;
    text-align: center;
    opacity: 0.9;
    transition: all 0.3s ease;
    z-index: 1;
    position: relative;
}

.sidebar-nav a:hover {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    transform: translateX(4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.sidebar-nav a:hover::before {
    opacity: 1;
}

.sidebar-nav a:hover i {
    opacity: 1;
    transform: scale(1.1);
}

.sidebar-nav a.active {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%);
    color: white;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.sidebar-nav a.active i {
    opacity: 1;
    color: white;
}

.content {
    margin-left: 220px;
    padding: 75px 20px 20px;
    transition: margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-transition: margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    -moz-transition: margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    -o-transition: margin-left 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.content.sidebar-collapsed {
    margin-left: 60px !important;
}

/* Main content layout */
.main-content {
    margin-left: 220px;
    padding-top: 60px;
}

.container-fluid {
    padding: 30px 20px;
}

/* Components */
.card {
    background: var(--background-color);
    border-radius: 12px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    margin-bottom: 20px;
    border: 1px solid var(--border-color);
}

.card-header {
    padding: 1rem 1.25rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h2 {
    margin: 0;
    font-size: 1.125rem;
    color: var(--text-color);
    font-weight: 500;
}

.card-body {
    padding: 1.25rem;
}

/* DataTables styling */
.table-responsive {
    margin: 0;
    padding: 0;
}

.table {
    width: 100%;
    margin-bottom: 0;
    background-color: var(--background-color);
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
    text-align: left;
    font-size: 0.875rem;
}

.table th {
    background-color: var(--light-gray);
    font-weight: 500;
    white-space: nowrap;
    color: var(--secondary-color);
}

.table tbody tr:hover {
    background-color: rgba(10, 126, 164, 0.05);
}

.dataTables_wrapper {
    font-size: 0.875rem;
}

.dataTables_filter,
.dataTables_length {
    margin-bottom: 1rem;
}

.dataTables_filter input,
.dataTables_length select {
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    margin-left: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-color);
}

.dataTables_filter input:focus,
.dataTables_length select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(10, 126, 164, 0.1);
}

.dataTables_paginate {
    margin-top: 1rem;
    display: flex;
    justify-content: flex-end;
    gap: 0.375rem;
}

.dataTables_paginate .paginate_button {
    padding: 0.5rem 0.75rem;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    background: var(--background-color);
    font-size: 0.875rem;
    color: var(--text-color) !important;
}

.dataTables_paginate .paginate_button.current {
    background-color: var(--primary-color);
    color: white !important;
    border-color: var(--primary-color);
}

.dataTables_paginate .paginate_button:hover:not(.current) {
    background-color: rgba(10, 126, 164, 0.1);
    border-color: var(--primary-color);
}

.btn {
    display: inline-flex;
    align-items: center;
    padding: 0.625rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.2s;
    cursor: pointer;
    gap: 0.5rem;
    border: none;
}

.btn i {
    font-size: 1rem;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: #096d8c;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.813rem;
    border-radius: 6px;
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background-color: #b91c1c;
}

.btn-success {
    background-color: var(--success-color);
    color: white;
}

.btn-success:hover {
    background-color: #15803d;
}

.alert {
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 8px;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.alert i {
    font-size: 1.125rem;
}

.alert-danger {
    background-color: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
}

.alert-success {
    background-color: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
}

/* Status badges */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 9999px;
    gap: 0.25rem;
}

.badge-danger {
    background-color: #fee2e2;
    color: #991b1b;
}

.badge-success {
    background-color: #dcfce7;
    color: #166534;
}

.badge-warning {
    background-color: #fef3c7;
    color: #92400e;
}

.badge-info {
    background-color: #dbeafe;
    color: #1e40af;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        width: 200px;
    }
    
    .content {
        margin-left: 200px;
    }
    
    .header {
        left: 200px;
    }
}

@media (max-width: 576px) {
    .sidebar {
        width: 100%;
        position: relative;
        height: auto;
    }
    
    .content {
        margin-left: 0;
        padding-top: 20px;
    }
    
    .header {
        left: 0;
        position: relative;
        height: auto;
    }
}

/* Force hardware acceleration for Chrome */
.sidebar, .header, .content {
    -webkit-transform: translateZ(0);
    -moz-transform: translateZ(0);
    -ms-transform: translateZ(0);
    -o-transform: translateZ(0);
    transform: translateZ(0);
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    -ms-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-perspective: 1000;
    -moz-perspective: 1000;
    -ms-perspective: 1000;
    perspective: 1000;
}

/* Collapsed Sidebar Styles */
.sidebar-toggle {
    position: absolute;
    top: 0.75rem;
    right: 0.75rem;
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1001;
    backdrop-filter: blur(10px);
}

.sidebar-toggle:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.5);
    transform: scale(1.05);
}

.sidebar-toggle i {
    transition: transform 0.3s ease;
}

.sidebar.collapsed {
    width: 60px !important;
    overflow: visible;
}

.sidebar.collapsed .sidebar-toggle {
    position: static;
    margin: 0 auto 0.75rem auto;
    display: block;
}

.sidebar.collapsed .sidebar-header {
    padding: 0.75rem 0.5rem;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.sidebar.collapsed .sidebar-toggle i {
    transform: rotate(180deg);
}

.sidebar.collapsed .logo-container {
    width: 40px;
    height: 40px;
    margin: 0;
}

.sidebar.collapsed .sidebar-logo {
    width: 40px;
    height: 40px;
}

.sidebar.collapsed .user-info,
.sidebar.collapsed .user-name,
.sidebar.collapsed .sidebar-header h2 {
    display: none;
}

.sidebar.collapsed .sidebar-nav a {
    padding: 0.875rem 0.5rem;
    justify-content: center;
    position: relative;
}

.sidebar.collapsed .sidebar-nav a .menu-text {
    display: none;
}

.sidebar.collapsed .sidebar-nav a i:not(.submenu-icon) {
    margin-right: 0;
    font-size: 1.25rem;
}

.sidebar.collapsed .submenu-icon {
    display: none;
}

.sidebar.collapsed .has-submenu .submenu {
    display: none;
}

/* Tooltip for collapsed sidebar */
.sidebar.collapsed .sidebar-nav a::after {
    content: attr(data-tooltip);
    position: absolute;
    left: 70px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1002;
    pointer-events: none;
}

.sidebar.collapsed .sidebar-nav a:hover::after {
    opacity: 1;
    visibility: visible;
    left: 65px;
}

/* Auto-collapse notification styles */
.auto-collapse-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #0a7ea4 0%, #0891b2 100%);
    color: white;
    padding: 1rem 1.25rem;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(10, 126, 164, 0.3);
    z-index: 10000;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    max-width: 400px;
    animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.auto-collapse-notification.auto-expand {
    background: linear-gradient(135deg, #16a34a 0%, #22c55e 100%);
    box-shadow: 0 4px 20px rgba(34, 197, 94, 0.3);
}

.auto-collapse-notification i {
    font-size: 1.125rem;
    opacity: 0.9;
}

.auto-collapse-notification .close-notification {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 24px;
    height: 24px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    transition: all 0.2s ease;
    margin-left: auto;
    flex-shrink: 0;
}

.auto-collapse-notification .close-notification:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Email Verification Blinking Animation */
.blinking-text {
    animation: blink 2s ease-in-out infinite;
    font-weight: 500 !important;
}

@keyframes blink {
    0%, 85% {
        opacity: 1;
    }
    90%, 95% {
        opacity: 0.4;
    }
    100% {
        opacity: 1;
    }
}

/* Email Verification Card Styling */
.email-verification-sent {
    border-left: 4px solid #0a7ea4 !important;
}

.email-verification-sent .info-icon {
    animation: pulse 3s ease-in-out infinite;
}

@keyframes pulse {
    0%, 90% {
        transform: scale(1);
    }
    95% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.info-text-secondary {
    margin-top: 0.5rem !important;
    padding: 0.5rem 0.75rem !important;
    background: rgba(10, 126, 164, 0.05) !important;
    border-radius: 6px !important;
    font-size: 0.85rem !important;
    color: #6b7280 !important;
    line-height: 1.4 !important;
    border: 1px solid rgba(10, 126, 164, 0.1) !important;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1050;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal-dialog {
    position: relative;
    width: auto;
    max-width: 500px;
    margin: 1.75rem auto;
    pointer-events: none;
}

.modal-content {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 12px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    outline: 0;
    animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.5rem 1.5rem 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.modal-header h5 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.modal-header .close {
    background: none;
    border: none;
    font-size: 1.25rem;
    cursor: pointer;
    color: #6b7280;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
}

.modal-header .close:hover {
    background-color: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    transform: scale(1.1);
}

.modal-body {
    position: relative;
    flex: 1 1 auto;
    padding: 1.5rem;
}

.modal-body p {
    margin-bottom: 1rem;
    line-height: 1.5;
    color: #374151;
}

.modal-body p:last-child {
    margin-bottom: 0;
}

.modal-footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 0.75rem;
    padding: 1rem 1.5rem 1.5rem;
    border-top: 1px solid rgba(0, 0, 0, 0.125);
    border-bottom-left-radius: 12px;
    border-bottom-right-radius: 12px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.modal-footer .btn {
    margin: 0;
}

/* Responsive modal */
@media (max-width: 576px) {
    .modal-dialog {
        max-width: none;
        margin: 1rem;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

/* Secondary button variant */
.btn-secondary {
    background-color: #6b7280;
    border-color: #6b7280;
    color: white;
}

.btn-secondary:hover {
    background-color: #4b5563;
    border-color: #4b5563;
    color: white;
}

/* Info button variant */
.btn-info {
    background-color: #06b6d4;
    border-color: #06b6d4;
    color: white;
}

.btn-info:hover {
    background-color: #0891b2;
    border-color: #0891b2;
    color: white;
}

/* Disabled button styling */
.btn:disabled {
    opacity: 0.7 !important;
    cursor: not-allowed !important;
    pointer-events: none;
}

.btn-danger:disabled {
    background-color: #dc2626 !important;
    border-color: #dc2626 !important;
}

/* Spinner animation for loading states */
.fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
} 