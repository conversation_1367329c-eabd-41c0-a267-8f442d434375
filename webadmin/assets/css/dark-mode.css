.dark-mode {
    --bg-primary: #1a1d20;
    --bg-secondary: #2d3238;
    --text-primary: #e9ecef;
    --text-secondary: #adb5bd;
    --border-color: #495057;
    --accent-color: #17a2b8;
}

.dark-mode .card,
.dark-mode .navbar,
.dark-mode .sidebar,
.dark-mode .modal-content {
    background-color: var(--bg-primary);
    border-color: var(--border-color);
}

.dark-mode .table {
    color: var(--text-primary);
}

.dark-mode .table td,
.dark-mode .table th {
    border-color: var(--border-color);
}

.dark-mode .form-control {
    background-color: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

.dark-mode .form-control:focus {
    background-color: var(--bg-secondary);
    border-color: var(--accent-color);
    color: var(--text-primary);
}

.dark-mode .modal-header,
.dark-mode .modal-footer {
    border-color: var(--border-color);
}

.dark-mode .dropdown-menu {
    background-color: var(--bg-primary);
    border-color: var(--border-color);
}

.dark-mode .dropdown-item {
    color: var(--text-primary);
}

.dark-mode .dropdown-item:hover {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
}

/* DataTables Dark Mode */
.dark-mode .dataTables_wrapper .dataTables_length,
.dark-mode .dataTables_wrapper .dataTables_filter,
.dark-mode .dataTables_wrapper .dataTables_info,
.dark-mode .dataTables_wrapper .dataTables_processing,
.dark-mode .dataTables_wrapper .dataTables_paginate {
    color: var(--text-primary);
}

.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button {
    color: var(--text-primary) !important;
}

.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background: var(--accent-color);
    border-color: var(--accent-color);
    color: white !important;
}

/* Chart.js Dark Mode */
.dark-mode .chartjs-render-monitor {
    filter: invert(1) hue-rotate(180deg);
} 