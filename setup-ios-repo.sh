#!/bin/bash

# Complete setup script for ProROLA iOS repository
# This script will clean the target repository and set up the optimized iOS structure

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Function to print colored output
print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ] || [ ! -f "app.json" ]; then
    print_error "This script must be run from the RolaApp source directory"
    print_error "Current directory: $(pwd)"
    exit 1
fi

print_header "ProROLA iOS Repository Setup"

# Get target directory from user
read -p "Enter the path to your ProROLA-iOS repository: " TARGET_DIR

# Validate target directory
if [ ! -d "$TARGET_DIR" ]; then
    print_error "Target directory does not exist: $TARGET_DIR"
    exit 1
fi

if [ ! -d "$TARGET_DIR/.git" ]; then
    print_error "Target directory is not a git repository: $TARGET_DIR"
    exit 1
fi

SOURCE_DIR="$(pwd)"
print_status "Source directory: $SOURCE_DIR"
print_status "Target directory: $TARGET_DIR"

# Confirm before proceeding
echo
print_warning "This will completely clean the target repository and copy optimized files."
read -p "Are you sure you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    print_status "Operation cancelled."
    exit 0
fi

print_header "Step 1: Cleaning Target Repository"

cd "$TARGET_DIR"

# Create backup branch if there are uncommitted changes
if ! git diff-index --quiet HEAD --; then
    print_warning "Uncommitted changes detected. Creating backup branch..."
    BACKUP_BRANCH="backup-$(date +%Y%m%d-%H%M%S)"
    git checkout -b "$BACKUP_BRANCH"
    git add -A
    git commit -m "Backup before iOS optimization"
    git checkout main 2>/dev/null || git checkout master
    print_success "Backup created in branch: $BACKUP_BRANCH"
fi

# Clean all files except .git
print_status "Removing all files from target repository..."
find . -mindepth 1 -maxdepth 1 ! -name '.git' -exec rm -rf {} +
print_success "Target repository cleaned"

print_header "Step 2: Copying Essential Files"

cd "$SOURCE_DIR"

# Copy root configuration files
ESSENTIAL_FILES=(
    "App.tsx"
    "app.json"
    "eas.json"
    "metro.config.js"
    "tsconfig.json"
    "expo-env.d.ts"
    "firebase.json"
    "GoogleService-Info.plist"
    "i18n.ts"
)

print_status "Copying root configuration files..."
for file in "${ESSENTIAL_FILES[@]}"; do
    if [ -f "$file" ]; then
        cp "$file" "$TARGET_DIR/"
        print_success "✓ $file"
    else
        print_warning "✗ $file (not found)"
    fi
done

# Copy source directories
ESSENTIAL_DIRS=(
    "app"
    "components"
    "constants"
    "contexts"
    "hooks"
    "services"
    "types"
    "utils"
    "config"
    "assets"
)

print_status "Copying source directories..."
for dir in "${ESSENTIAL_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        cp -r "$dir" "$TARGET_DIR/"
        print_success "✓ $dir/"
    else
        print_warning "✗ $dir/ (not found)"
    fi
done

print_header "Step 3: Creating Optimized Configuration Files"

# Copy optimized configuration files
cp "ios-optimized-configs/package.json" "$TARGET_DIR/"
cp "ios-optimized-configs/.gitignore" "$TARGET_DIR/"
cp "ios-optimized-configs/.easignore" "$TARGET_DIR/"
cp "ios-optimized-configs/README.md" "$TARGET_DIR/"

# Copy ci_scripts directory
cp -r "ios-optimized-configs/ci_scripts" "$TARGET_DIR/"

# Make scripts executable
chmod +x "$TARGET_DIR/ci_scripts/"*.sh

print_success "Configuration files created"

print_header "Step 4: Git Operations"

cd "$TARGET_DIR"

# Add all files to git
git add -A

# Check if there are changes to commit
if git diff-index --quiet --cached HEAD --; then
    print_warning "No changes to commit"
else
    # Commit changes
    git commit -m "iOS optimization: Clean repository with essential files only

- Removed web admin components, documentation, and Android files
- Added Xcode Cloud integration scripts
- Optimized for iOS builds only
- Repository size reduced by ~80-90%"

    print_success "Changes committed to git"
fi

print_header "Step 5: Validation"

# Validate essential files exist
VALIDATION_PASSED=true

print_status "Validating repository structure..."

# Check essential files
for file in "${ESSENTIAL_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✓ $file"
    else
        echo "✗ $file"
        VALIDATION_PASSED=false
    fi
done

# Check essential directories
for dir in "${ESSENTIAL_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        echo "✓ $dir/"
    else
        echo "✗ $dir/"
        VALIDATION_PASSED=false
    fi
done

# Check Xcode Cloud scripts
if [ -f "ci_scripts/ci_post_clone.sh" ] && [ -x "ci_scripts/ci_post_clone.sh" ]; then
    echo "✓ ci_scripts/ci_post_clone.sh (executable)"
else
    echo "✗ ci_scripts/ci_post_clone.sh"
    VALIDATION_PASSED=false
fi

print_header "Setup Complete!"

if [ "$VALIDATION_PASSED" = true ]; then
    print_success "✅ Repository setup completed successfully!"
else
    print_warning "⚠️  Setup completed with some missing files"
fi

echo
print_status "Next steps:"
echo "1. Push changes to GitHub: git push origin main"
echo "2. Set up Xcode Cloud integration"
echo "3. Configure signing certificates and provisioning profiles"
echo "4. Test build: npx expo prebuild --platform ios"
echo
print_status "Repository location: $TARGET_DIR"
