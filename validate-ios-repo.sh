#!/bin/bash

# Validation script for ProROLA iOS repository
# Ensures all essential files are present and repository is optimized

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_file() {
    if [ -f "$1" ]; then
        echo -e "${GREEN}✓${NC} $1"
        return 0
    else
        echo -e "${RED}✗${NC} $1"
        return 1
    fi
}

check_dir() {
    if [ -d "$1" ]; then
        echo -e "${GREEN}✓${NC} $1/"
        return 0
    else
        echo -e "${RED}✗${NC} $1/"
        return 1
    fi
}

check_executable() {
    if [ -f "$1" ] && [ -x "$1" ]; then
        echo -e "${GREEN}✓${NC} $1 (executable)"
        return 0
    else
        echo -e "${RED}✗${NC} $1 (not executable or missing)"
        return 1
    fi
}

print_header "ProROLA iOS Repository Validation"

# Check if we're in a git repository
if [ ! -d ".git" ]; then
    print_error "Not in a git repository"
    exit 1
fi

VALIDATION_SCORE=0
TOTAL_CHECKS=0

print_header "Essential Configuration Files"

# Essential root files
ESSENTIAL_FILES=(
    "package.json"
    "app.json"
    "eas.json"
    "metro.config.js"
    "tsconfig.json"
    "App.tsx"
    "GoogleService-Info.plist"
    "firebase.json"
    "i18n.ts"
    ".gitignore"
    ".easignore"
    "README.md"
)

for file in "${ESSENTIAL_FILES[@]}"; do
    if check_file "$file"; then
        ((VALIDATION_SCORE++))
    fi
    ((TOTAL_CHECKS++))
done

print_header "Essential Source Directories"

# Essential directories
ESSENTIAL_DIRS=(
    "app"
    "components"
    "constants"
    "contexts"
    "hooks"
    "services"
    "types"
    "utils"
    "config"
    "assets"
    "ci_scripts"
)

for dir in "${ESSENTIAL_DIRS[@]}"; do
    if check_dir "$dir"; then
        ((VALIDATION_SCORE++))
    fi
    ((TOTAL_CHECKS++))
done

print_header "Xcode Cloud Integration"

# Xcode Cloud scripts
XCODE_SCRIPTS=(
    "ci_scripts/ci_post_clone.sh"
    "ci_scripts/ci_pre_xcodebuild.sh"
    "ci_scripts/ci_post_xcodebuild.sh"
)

for script in "${XCODE_SCRIPTS[@]}"; do
    if check_executable "$script"; then
        ((VALIDATION_SCORE++))
    fi
    ((TOTAL_CHECKS++))
done

print_header "Excluded Files Check"

# Files that should NOT be present (optimization check)
EXCLUDED_ITEMS=(
    "webadmin"
    "gestores"
    "tecnicos"
    "stats"
    "privacy"
    "app-updates"
    "email"
    "google-services.json"
    "android"
    "build"
    "dist"
    ".expo"
)

EXCLUSION_SCORE=0
EXCLUSION_TOTAL=0

print_status "Checking that excluded files are not present..."

for item in "${EXCLUDED_ITEMS[@]}"; do
    if [ ! -e "$item" ]; then
        echo -e "${GREEN}✓${NC} $item (correctly excluded)"
        ((EXCLUSION_SCORE++))
    else
        echo -e "${YELLOW}!${NC} $item (should be excluded)"
    fi
    ((EXCLUSION_TOTAL++))
done

print_header "Package.json Validation"

# Check package.json content
if [ -f "package.json" ]; then
    # Check if it's the iOS-optimized version
    if grep -q '"name": "prorola-ios"' package.json; then
        print_success "Package.json is iOS-optimized"
        ((VALIDATION_SCORE++))
    else
        print_warning "Package.json may not be iOS-optimized"
    fi
    ((TOTAL_CHECKS++))
    
    # Check for essential dependencies
    ESSENTIAL_DEPS=(
        "expo"
        "react-native"
        "@react-native-firebase/app"
        "react-native-maps"
        "expo-router"
    )
    
    print_status "Checking essential dependencies..."
    for dep in "${ESSENTIAL_DEPS[@]}"; do
        if grep -q "\"$dep\"" package.json; then
            echo -e "${GREEN}✓${NC} $dep"
        else
            echo -e "${RED}✗${NC} $dep"
        fi
    done
fi

print_header "Repository Size Analysis"

# Calculate repository size
if command -v du >/dev/null 2>&1; then
    REPO_SIZE=$(du -sh . 2>/dev/null | cut -f1)
    print_status "Repository size: $REPO_SIZE"
    
    # Check if size is reasonable (should be under 200MB for optimized repo)
    SIZE_MB=$(du -sm . 2>/dev/null | cut -f1)
    if [ "$SIZE_MB" -lt 200 ]; then
        print_success "Repository size is optimized (< 200MB)"
    elif [ "$SIZE_MB" -lt 500 ]; then
        print_warning "Repository size is moderate ($SIZE_MB MB)"
    else
        print_warning "Repository size is large ($SIZE_MB MB) - consider further optimization"
    fi
fi

print_header "Git Status Check"

# Check git status
if git status --porcelain | grep -q .; then
    print_warning "Repository has uncommitted changes"
    git status --short
else
    print_success "Repository is clean (no uncommitted changes)"
fi

print_header "Validation Results"

# Calculate percentages
VALIDATION_PERCENT=$((VALIDATION_SCORE * 100 / TOTAL_CHECKS))
EXCLUSION_PERCENT=$((EXCLUSION_SCORE * 100 / EXCLUSION_TOTAL))

echo "Essential files: $VALIDATION_SCORE/$TOTAL_CHECKS ($VALIDATION_PERCENT%)"
echo "Exclusion check: $EXCLUSION_SCORE/$EXCLUSION_TOTAL ($EXCLUSION_PERCENT%)"

if [ $VALIDATION_PERCENT -ge 90 ] && [ $EXCLUSION_PERCENT -ge 80 ]; then
    print_success "🎉 Repository is well-optimized for iOS builds!"
    echo
    print_status "Ready for Xcode Cloud integration:"
    echo "1. Push to GitHub: git push origin main"
    echo "2. Connect to Xcode Cloud"
    echo "3. Configure build workflow"
    echo "4. Set up signing certificates"
elif [ $VALIDATION_PERCENT -ge 75 ]; then
    print_warning "⚠️  Repository is mostly ready but has some issues"
    echo "Please address the missing files/directories above"
else
    print_error "❌ Repository needs significant work before it's ready"
    echo "Many essential files are missing"
fi

echo
print_status "Validation completed."
