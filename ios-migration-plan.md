# iOS Repository Migration Plan

## Essential Files to Copy

### Root Configuration Files
- `package.json` (optimized version)
- `app.json` (iOS-focused configuration)
- `eas.json`
- `metro.config.js`
- `tsconfig.json`
- `expo-env.d.ts`
- `firebase.json`
- `GoogleService-Info.plist`
- `i18n.ts`
- `App.tsx`

### Source Code Directories
- `app/` (entire directory - Expo Router)
- `components/` (entire directory)
- `constants/` (entire directory)
- `contexts/` (entire directory)
- `hooks/` (entire directory)
- `services/` (entire directory)
- `types/` (entire directory)
- `utils/` (entire directory)
- `config/` (entire directory)

### Assets
- `assets/` (entire directory)

### Configuration Files to Create
- `.gitignore` (iOS-optimized)
- `.easignore` (iOS-optimized)
- `.npmrc`
- `README.md` (basic iOS build instructions)

### Xcode Cloud Integration
- `ci_scripts/` (directory with build scripts)
  - `ci_post_clone.sh`
  - `ci_pre_xcodebuild.sh` (optional)
  - `ci_post_xcodebuild.sh` (optional)

## Files to EXCLUDE
- `webadmin/`
- `gestores/`
- `tecnicos/`
- `stats/`
- `privacy/`
- `app-updates/`
- `email/`
- `android/`
- `google-services.json`
- `*.jks`
- `*.apk`
- `*.aab`
- All `.md` files except README.md
- `scripts/` (cleanup scripts)
- `keytool/`
- `build/`
- `dist/`
- `.expo/`
- `node_modules/`

## Repository Size Reduction
- Original: ~500MB+
- Optimized: ~50-100MB
- Reduction: ~80-90%

## Migration Steps
1. Clean existing ProROLA-iOS repository
2. Copy essential files using migration script
3. Create iOS-optimized configuration files
4. Set up Xcode Cloud integration files
5. Test build process
6. Validate repository structure
