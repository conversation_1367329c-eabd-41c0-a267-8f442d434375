# Google Maps Loading Fixes

## Issues Identified
1. **Multiple Google Maps API Loads**: The page was loading Google Maps API twice (static script + dynamic script)
2. **initMap Function Not Accessible**: The callback function wasn't globally accessible when Google Maps tried to call it
3. **CORS Errors**: Related to multiple API loads causing conflicts

## Fixes Applied

### 1. Removed Duplicate Script Tag
- **Removed**: Static `<script async defer src="...maps.googleapis.com..."></script>` tag
- **Kept**: Dynamic loading logic for better control

### 2. Made initMap Globally Accessible
```javascript
// Make initMap globally accessible for the callback
window.initMap = function() {
    console.log('Google Maps API loaded, initializing...');
    if (!window.mapsInitialized) {
        window.mapsInitialized = true;
        initializeMap();
    }
};
```

### 3. Added Initialization Protection
- **Global Flag**: `window.mapsInitialized` prevents multiple initializations
- **Duplicate Check**: Prevents re-initialization if already done

### 4. Improved Error Handling
```javascript
script.onerror = function() {
    console.error('Failed to load Google Maps API');
    Swal.fire({
        title: 'Erro ao carregar mapas',
        text: 'Não foi possível carregar o Google Maps. Verifique a sua ligação à internet.',
        icon: 'error'
    });
};
```

### 5. Better Loading Logic
- **DOM Ready Check**: Loads maps when DOM is ready
- **Existing API Check**: Detects if Google Maps is already loaded
- **Robust Loading**: Handles both scenarios (fresh load vs already loaded)

### 6. Added Debugging
- Console logs to track initialization process
- Better error identification

## Expected Result
- ✅ No more "multiple includes" warnings
- ✅ No more "initMap is not a function" errors
- ✅ No more CORS errors
- ✅ Clean map initialization
- ✅ Proper error handling if maps fail to load

## Testing
The page should now load without console errors and display the Google Map properly. 