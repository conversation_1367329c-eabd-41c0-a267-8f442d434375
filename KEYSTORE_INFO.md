# 🔐 ProROLA App Keystore Information

## Critical Production Signing Key

**⚠️ NEVER DELETE THIS FILE OR THE KEYSTORE - Required for Google Play Store uploads**

### Keystore Details
- **File Location**: `@dtales__rola.jks` (in project root)
- **Alias**: `a71772ebd4483772e88e20a51a8e49ab`
- **Store Password**: `b90b5b58cff5dcd461d7445022f69017`
- **Key Password**: `2f36b28b4b048991b9539a860b07a94f`

### Certificate Fingerprint
- **SHA1**: `5C:A4:F0:91:36:A4:EB:2F:CB:9A:EE:4F:10:EC:A9:56:2D:4B:41:B8`
- **SHA256**: `A4:D5:51:7A:92:79:CC:7D:70:80:02:C9:CC:95:EE:CB:CF:40:87:7E:D7:FC:E1:4C:19:52:6D:29:FC:4F:D3:1E`

### Google Play Console
This keystore matches the expected certificate fingerprint in Google Play Console.
**Any builds MUST use this exact keystore or uploads will be rejected.**

### Gradle Configuration
The keystore is configured in `android/gradle.properties`:
```
MYAPP_RELEASE_STORE_FILE=../../@dtales__rola.jks
MYAPP_RELEASE_KEY_ALIAS=a71772ebd4483772e88e20a51a8e49ab
MYAPP_RELEASE_STORE_PASSWORD=b90b5b58cff5dcd461d7445022f69017
MYAPP_RELEASE_KEY_PASSWORD=2f36b28b4b048991b9539a860b07a94f
```

### Safety Notes
- ✅ Keystore is in project root (safe from android folder deletions)
- ✅ Keystore is in .gitignore (not committed to git)
- ✅ Backup this file to secure cloud storage
- ⚠️ Never share passwords publicly
- ⚠️ Keep multiple backups of the keystore file

### Build Commands
```bash
cd android
./gradlew clean
./gradlew bundleRelease
```

Generated AAB will be in: `android/app/build/outputs/bundle/release/app-release.aab` 