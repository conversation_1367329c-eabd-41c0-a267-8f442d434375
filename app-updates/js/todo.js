$(document).ready(function() {
    // Function to load and organize tasks
    function loadTasks(callback) {
        // Add a small delay to ensure server has processed changes
        setTimeout(function() {
            $.ajax({
                url: 'server/loadTasks.php',
                type: 'GET',
                timeout: 10000, // 10 seconds timeout
                cache: false, // Prevent caching
                success: function(data) {
                    // First, load all tasks into a temporary container
                    const $tempContainer = $('<div>').html(data);
                    
                    // Separate completed and non-completed tasks
                    const completedTasks = $tempContainer.find('.list-group-item[data-completed="1"]');
                    const nonCompletedTasks = $tempContainer.find('.list-group-item:not([data-completed="1"])');
                    
                    // Clear both lists
                    $('#taskList').empty();
                    $('#completedTasksList').empty();
                    
                    // Add non-completed tasks to main list
                    $('#taskList').append(nonCompletedTasks);
                    
                    // Add completed tasks to completed list
                    if (completedTasks.length > 0) {
                        $('#completedTasksList').append('<h5 class="mt-4 mb-3 text-muted">Atualizações Concluídas</h5>');
                        $('#completedTasksList').append(completedTasks);
                    }
                    
                    // Update UI based on authentication status
                    updateUIForAuthentication();
                    
                    // Update statistics
                    updateStatistics();
                    
                    if (typeof callback === 'function') callback();
                },
                error: function(xhr, status, error) {
                    console.error("Error loading tasks:", status, error);
                    let errorMessage = "Erro ao carregar as tarefas.";
                    if (xhr.status === 500) {
                        errorMessage = "Erro interno do servidor. Por favor, verifique o log de erros.";
                    } else if (xhr.status === 404) {
                        errorMessage = "Arquivo não encontrado. Verifique se o caminho está correto.";
                    } else if (status === 'timeout') {
                        errorMessage = "Tempo limite excedido. Tente novamente mais tarde.";
                    }
                    $('#taskList').html(`<div class="alert alert-danger">${errorMessage}</div>`);
                }
            });
        }, 300); // 300ms delay
    }

    // Load tasks on page load
    loadTasks();

    // Authentication functions
    function setCookie(name, value, days) {
        const expires = new Date();
        expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
        document.cookie = name + '=' + value + ';expires=' + expires.toUTCString() + ';path=/';
    }

    function getCookie(name) {
        const nameEQ = name + "=";
        const ca = document.cookie.split(';');
        for(let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) === ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) === 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }

    function isAuthenticated() {
        return getCookie('admin_auth') === 'authenticated';
    }

    function authenticate() {
        const password = prompt("Por favor, introduza a password para aceder às funcionalidades de administração:");
        
        if (password === "albufeira") {
            setCookie('admin_auth', 'authenticated', 7); // 7 days
            updateUIForAuthentication();
            return true;
        } else if (password !== null) {
            showAlert('Password incorreta!', 'danger');
        }
        return false;
    }

    function updateUIForAuthentication() {
        if (isAuthenticated()) {
            $('#addTask').show();
            $('.task-edit-btn').show();
        } else {
            $('#addTask').hide();
            $('.task-edit-btn').hide();
        }
    }

    function logout() {
        // Remove authentication cookie
        setCookie('admin_auth', '', -1); // Set expiry to past date to delete cookie
        updateUIForAuthentication();
        showAlert('Sessão de administração terminada', 'info');
        $('#settingsModal').modal('hide');
    }

    // Check authentication on page load and update UI
    updateUIForAuthentication();

    // Define these variables in the global scope so they can be accessed by functions outside document.ready
    window.isEditing = false;
    window.selectedLabel = '';
    window.selectedPlatform = 'Web';
    window.selectedFiles = []; // For new task images
    window.editSelectedFiles = []; // For edit task images
    window.newTodoItems = []; // For new task todo items
    window.editTodoItems = []; // For edit task todo items
    const taskList = document.getElementById('taskList');

    // Initialize platform buttons - set default to 'Web'
    $('.platform-btn').removeClass('active');
    $('.platform-btn[data-platform="Web"]').addClass('active');
    
    // Initialize add task modal platform buttons
    $('#addTaskModal .platform-btn').removeClass('active');
    $('#addTaskModal .platform-btn[data-platform="Web"]').addClass('active');

    // Load the site title from the server
    loadSiteTitle();

    // Image Modal Functionality
    const imageModal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');
    const closeBtn = document.querySelector('.image-modal-close');

    // Close the modal when clicking the close button
    if (closeBtn) {
        closeBtn.onclick = function() {
            imageModal.style.display = "none";
        }
    }

    // Close the modal when clicking outside the image
    if (imageModal) {
        imageModal.onclick = function(event) {
            if (event.target === imageModal) {
                imageModal.style.display = "none";
            }
        }
    }

    // Handle file input change for new task
    $('#newTaskImageUpload').on('change', function(e) {
        handleFileSelect(e, 'new');
    });

    // Handle file input change for edit task
    $('#editTaskImageUpload').on('change', function(e) {
        handleFileSelect(e, 'edit');
    });

    // Handle clipboard paste for new task modal
    $('#addTaskModal').on('paste', function(e) {
        handleClipboardPaste(e, 'new');
    });

    // Handle clipboard paste for edit task modal
    $('#taskEditModal').on('paste', function(e) {
        handleClipboardPaste(e, 'edit');
    });

    // Todo list functionality
    $('#newAddTodoBtn').click(function() {
        const todoText = $('#newTodoItem').val().trim();
        if (todoText) {
            addTodoItem(todoText, 'new');
            $('#newTodoItem').val('');
        }
    });

    $('#editAddTodoBtn').click(function() {
        const todoText = $('#editNewTodoItem').val().trim();
        if (todoText) {
            addTodoItem(todoText, 'edit');
            $('#editNewTodoItem').val('');
        }
    });

    // Handle Enter key in todo inputs
    $('#newTodoItem').keypress(function(e) {
        if (e.which === 13) {
            $('#newAddTodoBtn').click();
        }
    });

    $('#editNewTodoItem').keypress(function(e) {
        if (e.which === 13) {
            $('#editAddTodoBtn').click();
        }
    });

    // Handle progress slider changes
    $(document).on('input', '#taskProgress', function() {
        const value = $(this).val();
        $('#progressValue').text(value + '%');
    });

    // Add new task - Show modal directly
    $('#addTask').click(function() {
        // Check authentication first
        if (!isAuthenticated()) {
            if (authenticate()) {
                // Continue with showing modal after successful authentication
            } else {
                return; // Stop if authentication failed
            }
        }
        
        // Reset form
        $('#addTaskForm')[0].reset();

        // Reset task type to default
        $('#newTaskType').val('Atualização');

        // Reset platform selection to Web
        $('#addTaskModal .platform-btn').removeClass('active');
        $('#addTaskModal .platform-btn[data-platform="Web"]').addClass('active');
        window.selectedPlatforms = ['Web'];
        
        // Reset image previews
        $('#newImagePreviewContainer').empty();
        window.selectedFiles = [];

        // Reset todo items
        window.newTodoItems = [];
        renderTodoItems(window.newTodoItems, '#newTodoItems');

        // Show modal
        $('#addTaskModal').modal('show');
    });
    
    // Settings button handler
    $('#settingsBtn').click(function() {
        // Check if already authenticated
        if (isAuthenticated()) {
            // Load current title
            $('#siteTitle').val($('.title').text());
            
            // Show settings modal
            $('#settingsModal').modal('show');
            return;
        }
        
        // Prompt for password
        const password = prompt("Por favor, introduza a password para aceder às definições:");
        
        // Check if password is correct
        if (password === "albufeira") {
            // Set authentication cookie
            setCookie('admin_auth', 'authenticated', 7); // 7 days
            updateUIForAuthentication();
            
            // Load current title
            $('#siteTitle').val($('.title').text());
            
            // Show settings modal
            $('#settingsModal').modal('show');
        } else if (password !== null) { // Only show error if user didn't click Cancel
            showAlert('Password incorreta!', 'danger');
        }
    });
    
    // Logout button handler
    $('#logoutBtn').click(function() {
        if (confirm('Tem a certeza que deseja terminar a sessão de administração?')) {
            logout();
        }
    });
    
    // Reset tasks button handler
    $('#resetTasksBtn').click(function() {
        if (confirm('Tem a certeza que deseja redefinir todas as tarefas? Esta ação não pode ser desfeita.')) {
            $.ajax({
                url: 'server/resetTasks.php',
                type: 'POST',
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        loadTasks();
                        showAlert('Todas as tarefas foram removidas com sucesso', 'success');
                        $('#settingsModal').modal('hide');
                    } else {
                        showAlert(response.message || 'Falha ao redefinir tarefas', 'danger');
                    }
                },
                error: function() {
                    showAlert('Erro ao ligar ao servidor', 'danger');
                }
            });
        }
    });
    
    // Save settings handler
    $('#saveSettings').click(function() {
        const newTitle = $('#siteTitle').val().trim();
        
        if (newTitle) {
            // Save the title to the server
            $.ajax({
                url: 'server/saveTitle.php',
                type: 'POST',
                data: { title: newTitle },
                dataType: 'json',
                success: function(response) {
                    if (response.status === 'success') {
                        // Update the title in the DOM
                        $('.title').text(newTitle);
                        
                        // Update the page title
                        document.title = newTitle;
                        $('#pageTitle').text(newTitle);
                        
                        showAlert('Definições guardadas com sucesso', 'success');
                        $('#settingsModal').modal('hide');
                    } else {
                        showAlert(response.message || 'Falha ao guardar definições', 'danger');
                    }
                },
                error: function() {
                    showAlert('Erro ao ligar ao servidor', 'danger');
                }
            });
        } else {
            showAlert('O título não pode estar vazio', 'danger');
        }
    });
    
    // Confirm add task
    $(document).on('click', '#confirmAddTask', function() {
        const taskText = $('#newTaskText').val().trim();
        const taskUser = $('#newTaskUser').val().trim();
        const taskType = $('#newTaskType').val() || 'Atualização';
        const selectedPlatforms = window.selectedPlatforms || ['Web'];
        const platformString = selectedPlatforms.join(', ');
        
        if (!taskText) {
            showAlert('O texto da tarefa não pode estar vazio', 'danger');
            return;
        }
        
        if (!taskUser) {
            showAlert('O campo "Solicitado por" não pode estar vazio', 'danger');
            return;
        }
        
        // Always set status to "Aguardando Desenvolvimento"
        let formattedTaskText = taskText;
        
        // Disable the button to prevent multiple submissions
        $(this).prop('disabled', true);
        
        // Create FormData object for file uploads
        const formData = new FormData();
        formData.append('taskText', formattedTaskText);
        formData.append('taskUser', taskUser);
        formData.append('taskType', taskType);
        formData.append('todoList', JSON.stringify(window.newTodoItems || []));
        formData.append('platform', platformString);
        
        // Add selected files to FormData
        if (window.selectedFiles.length > 0) {
            for (let i = 0; i < window.selectedFiles.length; i++) {
                formData.append('images[]', window.selectedFiles[i]);
            }
        }
        
        $.ajax({
            url: 'server/saveTasks.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                if (response.success || response.status === 'success') {
                    $('#addTaskModal').modal('hide');
                    loadTasks();
                    showAlert('Tarefa adicionada com sucesso', 'success');
                } else {
                    showAlert(response.message || 'Falha ao adicionar tarefa', 'danger');
                }
                $('#confirmAddTask').prop('disabled', false);
            },
            error: function() {
                showAlert('Erro ao ligar ao servidor', 'danger');
                $('#confirmAddTask').prop('disabled', false);
            }
        });
    });

    // Platform button selection in modals - allow multiple selection
    $(document).on('click', '.platform-btn', function() {
        $(this).toggleClass('active');
        
        // Update selectedPlatforms array
        const modal = $(this).closest('.modal');
        const activePlatforms = modal.find('.platform-btn.active').map(function() {
            return $(this).data('platform');
        }).get();
        
        // Store in global variable for easy access
        if (modal.attr('id') === 'addTaskModal') {
            window.selectedPlatforms = activePlatforms;
        } else if (modal.attr('id') === 'taskEditModal') {
            window.editSelectedPlatforms = activePlatforms;
        }
        
        // Ensure at least one platform is selected
        if (activePlatforms.length === 0) {
            $(this).addClass('active');
            if (modal.attr('id') === 'addTaskModal') {
                window.selectedPlatforms = [$(this).data('platform')];
            } else if (modal.attr('id') === 'taskEditModal') {
                window.editSelectedPlatforms = [$(this).data('platform')];
            }
        }
    });

    // Label button selection
    $(document).on('click', '.label-btn', function(e) {
        e.preventDefault();
        $(this).closest('.modal-body').find('.label-btn').removeClass('active');
        $(this).addClass('active');
        window.selectedLabel = $(this).data('label');
    });

    // Enhanced Task Edit Modal handler
    $(document).on('show.bs.modal', '#taskEditModal', function(event) {
        // Check authentication first
        if (!isAuthenticated()) {
            event.preventDefault(); // Prevent modal from opening
            if (authenticate()) {
                // Retry opening the modal after successful authentication
                $(event.relatedTarget).trigger('click');
            }
            return;
        }
        
        const button = $(event.relatedTarget);
        const taskId = button.data('task-id');
        const taskText = button.data('task-text');
        const taskUser = button.data('task-user') || '';
        const taskType = button.data('task-type') || 'Atualização';
        const todoList = String(button.data('todo-list') || '');
        const platform = button.data('platform') || 'Web';
        const label = button.data('label') || '';
        const completed = button.data('completed') === '1';
        const progress = button.data('progress') || 0;
        
        const modal = $(this);
        
        // Reset form
        modal.find('#taskEditForm')[0].reset();
        
        // Extract the actual task text without label prefix and completion date
        let displayText = taskText;
        
        // Remove label prefix if present
        if (label && displayText.startsWith(label + ' - ')) {
            displayText = displayText.substring((label + ' - ').length);
        }
        
        // Remove completion date if present
        displayText = displayText.replace(/\s*\(Concluído em: .*?\)/, '');
        
        modal.find('#editTaskText').val(displayText.trim());
        modal.find('#editTaskUser').val(taskUser);
        modal.find('#editTaskType').val(taskType);
        
        // Reset all platform buttons first
        modal.find('.platform-btn').removeClass('active');
        
        // Handle multiple platforms (comma-separated)
        const platforms = platform.split(',').map(p => p.trim());
        platforms.forEach(p => {
            modal.find(`.platform-btn[data-platform="${p}"]`).addClass('active');
        });
        window.editSelectedPlatforms = platforms;
        
        // Set label
        modal.find('.label-btn').removeClass('active');
        if (label) {
            modal.find(`.label-btn[data-label="${label}"]`).addClass('active');
        }
        window.selectedLabel = label;
        
        // Set completed checkbox
        modal.find('#taskCompleted').prop('checked', completed);
        
        // Set progress slider
        modal.find('#taskProgress').val(progress);
        modal.find('#progressValue').text(progress + '%');
        
        // Reset image previews
        $('#editImagePreviewContainer').empty();
        window.editSelectedFiles = [];

        // Load todo items
        loadTodoItems(todoList, 'edit');
        
        // Load existing images for this task
        loadTaskImages(taskId);
        
        // Load comments for this task
        loadTaskComments(taskId);
        
        // Store task ID in the modal and globally for comments
        modal.data('taskId', taskId);
        currentTaskId = taskId;
    });

    // Save task changes
    $(document).on('click', '#saveTaskChanges', function() {
        const modal = $('#taskEditModal');
        const taskId = modal.data('taskId');
        const taskText = modal.find('#editTaskText').val().trim();
        const taskUser = modal.find('#editTaskUser').val().trim();
        const taskType = modal.find('#editTaskType').val() || 'Atualização';
        const selectedPlatforms = window.editSelectedPlatforms || ['Web'];
        const platformString = selectedPlatforms.join(', ');
        const selectedLabel = modal.find('.label-btn.active').data('label') || '';
        const completed = modal.find('#taskCompleted').prop('checked') ? '1' : '0';
        const progress = modal.find('#taskProgress').val() || 0;
        
        if (!taskText) {
            showAlert('O texto da tarefa não pode estar vazio', 'danger');
            return;
        }
        
        if (!taskUser) {
            showAlert('O campo "Solicitado por" não pode estar vazio', 'danger');
            return;
        }
        
        // Disable the save button to prevent multiple submissions
        $(this).prop('disabled', true);
        
        // Create FormData object for file uploads
        const formData = new FormData();
        formData.append('taskId', taskId);
        formData.append('taskText', taskText);
        formData.append('taskUser', taskUser);
        formData.append('taskType', taskType);
        formData.append('todoList', JSON.stringify(window.editTodoItems || []));
        formData.append('taskLabel', selectedLabel);
        formData.append('platform', platformString);
        formData.append('completed', completed);
        formData.append('progress', progress);
        formData.append('forceComplete', completed);
        
        // Add selected files to FormData
        if (window.editSelectedFiles.length > 0) {
            for (let i = 0; i < window.editSelectedFiles.length; i++) {
                formData.append('images[]', window.editSelectedFiles[i]);
            }
        }
        
        $.ajax({
            url: 'server/updateTask.php',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    modal.modal('hide');
                    loadTasks();
                    showAlert('Tarefa atualizada com sucesso', 'success');
                } else {
                    showAlert(response.message || 'Falha ao atualizar tarefa', 'danger');
                }
                $('#saveTaskChanges').prop('disabled', false);
            },
            error: function() {
                showAlert('Erro ao ligar ao servidor', 'danger');
                $('#saveTaskChanges').prop('disabled', false);
            }
        });
    });

    // Delete task button in edit modal
    $(document).on('click', '#deleteTask', function() {
        const taskId = $('#taskEditModal').data('taskId');
        const taskText = $('#editTaskText').val().trim();
        
        // Hide the edit modal and show the delete confirmation modal
        $('#taskEditModal').modal('hide');
        showDeleteConfirmation(taskId, taskText);
    });

    // Confirm delete task
    $(document).on('click', '#confirmDeleteTask', function() {
        const taskId = $('#deleteConfirmModal').data('taskId');
        
        // Disable the button to prevent multiple submissions
        $(this).prop('disabled', true);
        
        $.ajax({
            url: 'server/removeTask.php',
            type: 'POST',
            dataType: 'json',
            data: { taskId: taskId },
            success: function(response) {
                if (response.status === 'success') {
                    $('#deleteConfirmModal').modal('hide');
                    loadTasks();
                    showAlert('Tarefa removida com sucesso', 'success');
                } else {
                    showAlert(response.message || 'Falha ao remover tarefa', 'danger');
                }
                $('#confirmDeleteTask').prop('disabled', false);
            },
            error: function() {
                showAlert('Erro ao ligar ao servidor', 'danger');
                $('#confirmDeleteTask').prop('disabled', false);
            }
        });
    });

    // Task completion handlers
    let taskToComplete = null;

    // Store the task ID when the complete button is clicked
    $(document).on('click', '#completeTask', function() {
        const taskId = $('#taskEditModal').data('taskId');
        taskToComplete = taskId;
        
        // Hide the edit modal first
        $('#taskEditModal').modal('hide');
        
        // Show the completion confirmation modal after a short delay
        setTimeout(() => {
            $('#completeConfirmModal').modal({
                backdrop: 'static',
                keyboard: false,
                show: true
            });
        }, 150);
    });

    // Handle the confirmation of task completion
    $(document).on('click', '#confirmCompleteTask', function() {
        if (!taskToComplete) return;

        // Disable the button to prevent multiple clicks
        $(this).prop('disabled', true);

        // Get the task element
        const taskElement = $(`.list-group-item[data-task-id="${taskToComplete}"]`);
        const taskText = taskElement.find('.task-text').data('original-text');
        
        // First, save the completion status to the server
        $.ajax({
            url: 'server/updateTask.php',
            type: 'POST',
            data: {
                taskId: taskToComplete,
                taskText: taskText,
                completed: '1',
                forceComplete: '1'
            },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    // After successful save, reload the tasks to get the updated state
                    loadTasks();
                    $('#completeConfirmModal').modal('hide');
                    showAlert('Tarefa marcada como concluída com sucesso', 'success');
                } else {
                    showAlert(response.message || 'Falha ao marcar tarefa como concluída', 'danger');
                }
            },
            error: function() {
                showAlert('Erro ao ligar ao servidor', 'danger');
            },
            complete: function() {
                taskToComplete = null;
                $('#confirmCompleteTask').prop('disabled', false);
            }
        });
    });

    // Reset taskToComplete when the completion confirmation modal is hidden
    $('#completeConfirmModal').on('hidden.bs.modal', function() {
        taskToComplete = null;
    });

    // Reset todo items when modals are closed
    $('#addTaskModal').on('hidden.bs.modal', function() {
        window.newTodoItems = [];
        renderTodoItems(window.newTodoItems, '#newTodoItems');
    });

    $('#taskEditModal').on('hidden.bs.modal', function() {
        window.editTodoItems = [];
        renderTodoItems(window.editTodoItems, '#editTodoItems');
    });
});

// Function to handle clipboard paste events
function handleClipboardPaste(event, type) {
    const clipboardData = event.originalEvent.clipboardData;
    if (!clipboardData || !clipboardData.items) return;
    
    const items = clipboardData.items;
    let imageFound = false;
    
    // Check if we've already reached the maximum number of images
    const currentFiles = type === 'new' ? window.selectedFiles : window.editSelectedFiles;
    if (currentFiles.length >= 6) {
        showAlert('Máximo de 6 imagens permitido', 'warning');
        return;
    }
    
    for (let i = 0; i < items.length; i++) {
        if (items[i].type.indexOf('image') !== -1) {
            imageFound = true;
            const file = items[i].getAsFile();
            
            // Generate a unique name for the pasted image
            const timestamp = new Date().getTime();
            const pastedFileName = `pasted_image_${timestamp}.png`;
            
            // Create a File object with a proper name
            const renamedFile = new File([file], pastedFileName, { type: file.type });
            
            // Add to the appropriate files array
            if (type === 'new') {
                window.selectedFiles.push(renamedFile);
                addImagePreview(renamedFile, $('#newImagePreviewContainer'), 'new');
            } else {
                window.editSelectedFiles.push(renamedFile);
                addImagePreview(renamedFile, $('#editImagePreviewContainer'), 'edit');
            }
            
            showAlert('Imagem colada com sucesso', 'success');
            break; // Only process the first image found
        }
    }
    
    if (imageFound) {
        event.preventDefault(); // Prevent the default paste behavior if we found an image
    }
}

// Function to add image preview
function addImagePreview(file, container, type) {
    const reader = new FileReader();
    
    reader.onload = function(e) {
        // Create preview container
        const previewDiv = $('<div class="preview-image-container"></div>');
        
        // Create image element
        const img = $('<img class="preview-image">').attr('src', e.target.result);
        
        // Create remove button
        const removeBtn = $('<button class="remove-preview-btn"><i class="fas fa-times"></i></button>');
        removeBtn.on('click', function() {
            const index = Array.from(container.find('.preview-image-container')).indexOf(previewDiv[0]);
            if (type === 'new') {
                window.selectedFiles.splice(index, 1);
            } else {
                window.editSelectedFiles.splice(index, 1);
            }
            previewDiv.remove();
        });
        
        // Add elements to preview container
        previewDiv.append(img, removeBtn);
        container.append(previewDiv);
    };
    
    // Read the image file as a data URL
    reader.readAsDataURL(file);
}

// Function to handle file selection
function handleFileSelect(event, type) {
    const files = event.target.files;
    const previewContainer = type === 'new' ? $('#newImagePreviewContainer') : $('#editImagePreviewContainer');
    const currentFiles = type === 'new' ? window.selectedFiles : window.editSelectedFiles;
    
    if (files.length > 0) {
        // Check if adding these files would exceed the limit
        if (currentFiles.length + files.length > 6) {
            showAlert('Máximo de 6 imagens permitido', 'warning');
            // Only process files up to the limit
            const remainingSlots = 6 - currentFiles.length;
            if (remainingSlots <= 0) {
                return;
            }
            
            // Process only the number of files that would fit within the limit
            for (let i = 0; i < remainingSlots; i++) {
                if (i >= files.length) break;
                
                const file = files[i];
                
                // Only process image files
                if (!file.type.match('image.*')) {
                    continue;
                }
                
                // Add to selected files array
                if (type === 'new') {
                    window.selectedFiles.push(file);
                } else {
                    window.editSelectedFiles.push(file);
                }
                
                addImagePreview(file, previewContainer, type);
            }
        } else {
            // Process all files if we're within the limit
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                
                // Only process image files
                if (!file.type.match('image.*')) {
                    continue;
                }
                
                // Add to selected files array
                if (type === 'new') {
                    window.selectedFiles.push(file);
                } else {
                    window.editSelectedFiles.push(file);
                }
                
                addImagePreview(file, previewContainer, type);
            }
        }
    }
}

// Function to load task images
function loadTaskImages(taskId) {
    $.ajax({
        url: 'server/getTaskImages.php',
        type: 'GET',
        data: { taskId: taskId },
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success' && response.images) {
                const imagesContainer = $('#editTaskImages');
                imagesContainer.empty();
                
                if (response.images.length > 0) {
                    response.images.forEach(function(image) {
                        const imageDiv = $('<div class="task-image-container"></div>');
                        const img = $('<img class="task-image">').attr('src', image.path);
                        img.on('click', function() {
                            openImageModal(image.path);
                        });
                        
                        const deleteBtn = $('<button class="delete-image-btn"><i class="fas fa-times"></i></button>');
                        deleteBtn.on('click', function() {
                            deleteTaskImage(taskId, image.path);
                        });
                        
                        imageDiv.append(img, deleteBtn);
                        imagesContainer.append(imageDiv);
                    });
                } else {
                    imagesContainer.html('<p class="text-muted">Nenhuma imagem anexada a esta tarefa.</p>');
                }
            }
        },
        error: function() {
            $('#editTaskImages').html('<p class="text-danger">Erro ao carregar imagens.</p>');
        }
    });
}

// Function to open image modal
function openImageModal(imageSrc) {
    const modal = document.getElementById('imageModal');
    const modalImg = document.getElementById('modalImage');
    
    modal.style.display = "block";
    modalImg.src = imageSrc;
}

// Function to delete task image
function deleteTaskImage(taskId, imagePath) {
    // Get references to the elements before showing the confirm dialog
    const $deleteBtn = $(event.target).closest('.delete-image-btn');
    const $imageContainer = $deleteBtn.closest('.task-image-container');
    
    if (confirm('Tem a certeza que deseja excluir esta imagem?')) {
        // Add loading state
        $imageContainer.css('opacity', '0.5');
        $deleteBtn.prop('disabled', true);
        
        // Perform the deletion
        $.ajax({
            url: 'server/deleteImage.php',
            type: 'POST',
            data: {
                taskId: taskId,
                imagePath: imagePath
            },
            dataType: 'json',
            success: function(response) {
                if (response.status === 'success') {
                    // Remove just this image from the DOM
                    $imageContainer.fadeOut(300, function() {
                        $(this).remove();
                        
                        // If no images left, show the "no images" message
                        if ($('#editTaskImages .task-image-container').length === 0) {
                            $('#editTaskImages').html('<p class="text-muted">Nenhuma imagem anexada a esta tarefa.</p>');
                        }
                    });
                    
                    showAlert('Imagem excluída com sucesso', 'success');
                } else {
                    // Restore the image container if there was an error
                    $imageContainer.css('opacity', '1');
                    $deleteBtn.prop('disabled', false);
                    showAlert(response.message || 'Falha ao excluir imagem', 'danger');
                }
            },
            error: function() {
                // Restore the image container if there was an error
                $imageContainer.css('opacity', '1');
                $deleteBtn.prop('disabled', false);
                showAlert('Erro ao ligar ao servidor', 'danger');
            }
        });
    }
}

function showDeleteConfirmation(taskId, taskText) {
    $('#deleteConfirmModal').data('taskId', taskId);
    $('#deleteConfirmModal .modal-body').html(`Tem a certeza que deseja apagar:<br><strong>${taskText}</strong>?`);
    $('#deleteConfirmModal').modal('show');
}

// Add the showAlert function
function showAlert(message, type) {
    const alertDiv = $('<div>').addClass(`alert alert-${type} alert-dismissible fade show`).attr('role', 'alert');
    alertDiv.html(`
        ${message}
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    `);
    
    $('#alertContainer').append(alertDiv);
    
    // Auto-dismiss after 3 seconds
    setTimeout(function() {
        alertDiv.alert('close');
    }, 3000);
}

// Add the loadSiteTitle function
function loadSiteTitle() {
    $.ajax({
        url: 'server/getTitle.php',
        type: 'GET',
        dataType: 'json',
        success: function(response) {
            if (response.status === 'success' && response.title) {
                // Update the title in the DOM
                $('.title').text(response.title);
                
                // Update the page title
                document.title = response.title;
                $('#pageTitle').text(response.title);
            }
        },
        error: function() {
            console.error('Failed to load site title');
        }
    });
}

// Comments functionality
function loadTaskComments(taskId) {
    console.log('Loading comments for task ID:', taskId);
    $.ajax({
        url: 'server/getComments.php',
        type: 'GET',
        data: { taskId: taskId },
        dataType: 'json',
        success: function(response) {
            console.log('Comments response:', response);
            if (response.status === 'success') {
                displayComments(response.comments);
            } else {
                console.error('Failed to load comments:', response.message);
                displayComments([]);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error loading comments:', status, error);
            console.error('Response text:', xhr.responseText);
            displayComments([]);
        }
    });
}

function displayComments(comments) {
    const commentsList = $('#taskCommentsList');
    commentsList.empty();
    
    if (comments.length === 0) {
        commentsList.html(`
            <div class="comments-empty">
                <i class="fas fa-comments"></i>
                <div>Nenhum comentário ainda</div>
            </div>
        `);
    } else {
        comments.forEach(function(comment) {
            const commentHtml = `
                <div class="comment-item" data-comment-id="${comment.id}">
                    <div class="comment-header">
                        <div class="comment-author">
                            <i class="fas fa-user"></i>
                            ${escapeHtml(comment.author)}
                        </div>
                        <div class="comment-actions">
                            <div class="comment-timestamp">
                                ${comment.formattedTimestamp}
                            </div>
                            <div class="comment-buttons">
                                <button class="btn btn-sm btn-outline-primary edit-comment-btn" title="Editar comentário">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger delete-comment-btn" title="Apagar comentário">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="comment-content">
                        <div class="comment-text">
                            ${escapeHtml(comment.comment)}
                        </div>
                        <div class="comment-edit-form" style="display: none;">
                            <div class="form-row">
                                <div class="col-md-4">
                                    <input type="text" class="form-control edit-comment-author" value="${escapeHtml(comment.author)}" placeholder="Nome">
                                </div>
                                <div class="col-md-6">
                                    <input type="text" class="form-control edit-comment-text" value="${escapeHtml(comment.comment)}" placeholder="Comentário">
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-success btn-sm save-comment-btn">
                                        <i class="fas fa-check"></i>
                                    </button>
                                    <button class="btn btn-secondary btn-sm cancel-edit-btn">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            commentsList.append(commentHtml);
        });
    }
}

function addTaskComment(taskId, comment, author) {
    if (!comment.trim() || !author.trim()) {
        showAlert('Por favor, preencha o nome e o comentário', 'warning');
        return;
    }
    
    console.log('Adding comment for task ID:', taskId, 'Author:', author, 'Comment:', comment);
    
    const addButton = $('#addComment');
    const originalText = addButton.html();
    
    // Show loading state
    addButton.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i>');
    
    $.ajax({
        url: 'server/addComment.php',
        type: 'POST',
        data: {
            taskId: taskId,
            comment: comment,
            author: author
        },
        dataType: 'json',
        success: function(response) {
            console.log('Add comment response:', response);
            if (response.status === 'success') {
                // Clear the form
                $('#commentText').val('');
                
                // Reload comments
                loadTaskComments(taskId);
                
                // Refresh the task list to update comment previews
                loadTasks();
                
                showAlert('Comentário adicionado com sucesso', 'success');
            } else {
                showAlert(response.message || 'Falha ao adicionar comentário', 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error adding comment:', status, error);
            console.error('Response text:', xhr.responseText);
            showAlert('Erro ao ligar ao servidor', 'danger');
        },
        complete: function() {
            // Restore button state
            addButton.prop('disabled', false).html(originalText);
        }
    });
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Function to update statistics cards
function updateStatistics() {
    // Exclude canceled tasks from all counts
    const allTasks = $('#taskList .list-group-item:not([data-label="Cancelado"]), #completedTasksList .list-group-item:not([data-label="Cancelado"])');
    const completedTasks = $('#completedTasksList .list-group-item[data-completed="1"]:not([data-label="Cancelado"])');
    const inDevelopmentTasks = $('#taskList .list-group-item[data-label="Em Desenvolvimento"]:not([data-label="Cancelado"])');
    
    // Count tasks with 100% progress as implemented, even if they're still "Em Desenvolvimento"
    // But exclude canceled tasks
    let tasksAt100Percent = 0;
    $('#taskList .list-group-item[data-label="Em Desenvolvimento"]:not([data-label="Cancelado"])').each(function() {
        const progressText = $(this).find('.task-progress-text').text();
        const progress = parseInt(progressText.replace('%', '')) || 0;
        if (progress === 100) {
            tasksAt100Percent++;
        }
    });
    
    // Count waiting for response tasks separately (they're not actively in development)
    const waitingResponseTasks = $('#taskList .list-group-item[data-label="Aguardando Resposta"]:not([data-label="Cancelado"])');
    
    const totalCount = allTasks.length;
    const completedCount = completedTasks.length + tasksAt100Percent; // Include 100% development tasks
    const inDevelopmentCount = inDevelopmentTasks.length - tasksAt100Percent; // Exclude 100% tasks from development count
    const progressPercentage = totalCount > 0 ? Math.round((completedCount / totalCount) * 100) : 0;
    
    // Update the statistics cards
    $('#totalTasks').text(totalCount);
    $('#completedTasks').text(completedCount);
    $('#inDevelopmentTasks').text(inDevelopmentCount);
    $('#progressPercentage').text(progressPercentage + '%');
}

// Store current task ID for comments
let currentTaskId = null;

// Add event handlers for comments
$(document).on('click', '#addComment', function() {
    if (currentTaskId) {
        const comment = $('#commentText').val();
        const author = $('#commentAuthor').val();
        addTaskComment(currentTaskId, comment, author);
    }
});

// Handle Enter key in comment inputs
$(document).on('keypress', '#commentText, #commentAuthor', function(e) {
    if (e.which === 13) { // Enter key
        $('#addComment').click();
    }
});

// Edit comment functionality
$(document).on('click', '.edit-comment-btn', function() {
    const commentItem = $(this).closest('.comment-item');
    const commentText = commentItem.find('.comment-text');
    const editForm = commentItem.find('.comment-edit-form');
    
    // Hide text and show edit form
    commentText.hide();
    editForm.show();
    
    // Focus on the comment text input
    editForm.find('.edit-comment-text').focus();
});

// Cancel edit
$(document).on('click', '.cancel-edit-btn', function() {
    const commentItem = $(this).closest('.comment-item');
    const commentText = commentItem.find('.comment-text');
    const editForm = commentItem.find('.comment-edit-form');
    
    // Show text and hide edit form
    commentText.show();
    editForm.hide();
});

// Save edited comment
$(document).on('click', '.save-comment-btn', function() {
    const commentItem = $(this).closest('.comment-item');
    const commentId = commentItem.data('comment-id');
    const newAuthor = commentItem.find('.edit-comment-author').val().trim();
    const newComment = commentItem.find('.edit-comment-text').val().trim();
    
    if (!newAuthor || !newComment) {
        showAlert('Por favor, preencha o nome e o comentário', 'warning');
        return;
    }
    
    console.log('Editing comment:', commentId, 'Author:', newAuthor, 'Comment:', newComment);
    
    const saveBtn = $(this);
    const originalText = saveBtn.html();
    
    // Show loading state
    saveBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i>');
    
    $.ajax({
        url: 'server/editComment.php',
        type: 'POST',
        data: {
            commentId: commentId,
            comment: newComment,
            author: newAuthor
        },
        dataType: 'json',
        success: function(response) {
            console.log('Edit comment response:', response);
                         if (response.status === 'success') {
                 // Reload comments to show updated data
                 loadTaskComments(currentTaskId);
                 
                 // Refresh the task list to update comment previews
                 loadTasks();
                 
                 showAlert('Comentário atualizado com sucesso', 'success');
             } else {
                showAlert(response.message || 'Falha ao atualizar comentário', 'danger');
            }
        },
        error: function(xhr, status, error) {
            console.error('Error editing comment:', status, error);
            console.error('Response text:', xhr.responseText);
            showAlert('Erro ao ligar ao servidor', 'danger');
        },
        complete: function() {
            // Restore button state
            saveBtn.prop('disabled', false).html(originalText);
        }
    });
});

// Delete comment functionality
$(document).on('click', '.delete-comment-btn', function() {
    const commentItem = $(this).closest('.comment-item');
    const commentId = commentItem.data('comment-id');
    
    if (!confirm('Tem a certeza que deseja apagar este comentário?')) {
        return;
    }
    
    console.log('Deleting comment:', commentId);
    
    const deleteBtn = $(this);
    const originalText = deleteBtn.html();
    
    // Show loading state
    deleteBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i>');
    
    $.ajax({
        url: 'server/deleteComment.php',
        type: 'POST',
        data: {
            commentId: commentId
        },
        dataType: 'json',
        success: function(response) {
            console.log('Delete comment response:', response);
            if (response.status === 'success') {
                                 // Remove the comment from DOM with animation
                 commentItem.fadeOut(300, function() {
                     $(this).remove();
                     
                     // Check if no comments left
                     if ($('#taskCommentsList .comment-item').length === 0) {
                         loadTaskComments(currentTaskId); // Reload to show empty state
                     }
                 });
                 
                 // Refresh the task list to update comment previews
                 loadTasks();
                 
                 showAlert('Comentário apagado com sucesso', 'success');
            } else {
                showAlert(response.message || 'Falha ao apagar comentário', 'danger');
                deleteBtn.prop('disabled', false).html(originalText);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error deleting comment:', status, error);
            console.error('Response text:', xhr.responseText);
            showAlert('Erro ao ligar ao servidor', 'danger');
            deleteBtn.prop('disabled', false).html(originalText);
        }
    });
});

// Handle Enter key in edit comment inputs
$(document).on('keypress', '.edit-comment-text, .edit-comment-author', function(e) {
    if (e.which === 13) { // Enter key
        $(this).closest('.comment-edit-form').find('.save-comment-btn').click();
    }
});

// Todo list helper functions
function addTodoItem(text, type) {
    const todoId = 'todo_' + Date.now();
    const todoItem = {
        id: todoId,
        text: text,
        completed: false
    };

    if (type === 'new') {
        window.newTodoItems.push(todoItem);
        renderTodoItems(window.newTodoItems, '#newTodoItems');
    } else {
        window.editTodoItems.push(todoItem);
        renderTodoItems(window.editTodoItems, '#editTodoItems');
    }
}

function renderTodoItems(todoItems, containerId) {
    const container = $(containerId);
    container.empty();

    if (todoItems.length === 0) {
        container.html('<div class="text-muted">Nenhuma tarefa adicionada</div>');
        return;
    }

    todoItems.forEach(function(item, index) {
        const todoHtml = `
            <div class="todo-item" data-todo-id="${item.id}">
                <div class="todo-content">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input todo-checkbox" id="todo_${item.id}" ${item.completed ? 'checked' : ''}>
                        <label class="custom-control-label" for="todo_${item.id}">
                            <span class="todo-text ${item.completed ? 'completed' : ''}">${escapeHtml(item.text)}</span>
                        </label>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-todo-btn" title="Remover tarefa">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(todoHtml);
    });
}

function loadTodoItems(todoListJson, type) {
    let todoItems = [];

    // Ensure todoListJson is a string and not empty
    if (todoListJson && typeof todoListJson === 'string' && todoListJson.trim() !== '') {
        try {
            todoItems = JSON.parse(todoListJson);
            // Ensure todoItems is an array
            if (!Array.isArray(todoItems)) {
                todoItems = [];
            }
        } catch (e) {
            console.error('Error parsing todo list:', e);
            todoItems = [];
        }
    }

    if (type === 'new') {
        window.newTodoItems = todoItems;
        renderTodoItems(window.newTodoItems, '#newTodoItems');
    } else {
        window.editTodoItems = todoItems;
        renderTodoItems(window.editTodoItems, '#editTodoItems');
    }
}

// Todo item event handlers
$(document).on('change', '.todo-checkbox', function() {
    const todoId = $(this).closest('.todo-item').data('todo-id');
    const isChecked = $(this).is(':checked');
    const todoText = $(this).siblings('label').find('.todo-text');

    // Update the visual state
    if (isChecked) {
        todoText.addClass('completed');
    } else {
        todoText.removeClass('completed');
    }

    // Update the data
    const containerId = $(this).closest('.todo-items').attr('id');
    const todoItems = containerId === 'newTodoItems' ? window.newTodoItems : window.editTodoItems;

    const todoItem = todoItems.find(item => item.id === todoId);
    if (todoItem) {
        todoItem.completed = isChecked;
    }
});

$(document).on('click', '.remove-todo-btn', function() {
    const todoItem = $(this).closest('.todo-item');
    const todoId = todoItem.data('todo-id');
    const containerId = $(this).closest('.todo-items').attr('id');

    // Remove from data
    if (containerId === 'newTodoItems') {
        window.newTodoItems = window.newTodoItems.filter(item => item.id !== todoId);
        renderTodoItems(window.newTodoItems, '#newTodoItems');
    } else {
        window.editTodoItems = window.editTodoItems.filter(item => item.id !== todoId);
        renderTodoItems(window.editTodoItems, '#editTodoItems');
    }
});



