<!DOCTYPE html>
<html lang="pt">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="pageTitle">CoolFM - Updates</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container-fluid p-2 p-md-4">
        <div class="row">
            <div class="col-12 col-md-10 col-lg-8 mx-auto">
                <div class="card card-body">
                    <div class="header-container">
                        <h3>
                            <span class="title">CoolFM - Updates</span>
                        </h3>
                        <div class="header-buttons">
                            <button id="addTask" class="btn btn-primary"><i class="fas fa-plus-circle mr-2"></i>Nova Atualização</button>
                            <button id="settingsBtn" class="btn btn-sm btn-outline-secondary settings-btn">
                                <i class="fas fa-cog"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Statistics Cards -->
                    <div class="stats-container mb-4">
                        <div class="row">
                            <div class="col-6 col-md-3">
                                <div class="stat-card">
                                    <div class="stat-number" id="totalTasks">0</div>
                                    <div class="stat-label">TOTAL DE ATUALIZAÇÕES</div>
                                </div>
                            </div>
                            <div class="col-6 col-md-3">
                                <div class="stat-card">
                                    <div class="stat-number" id="completedTasks">0</div>
                                    <div class="stat-label">IMPLEMENTADAS</div>
                                </div>
                            </div>
                            <div class="col-6 col-md-3">
                                <div class="stat-card">
                                    <div class="stat-number" id="inDevelopmentTasks">0</div>
                                    <div class="stat-label">EM DESENVOLVIMENTO</div>
                                </div>
                            </div>
                            <div class="col-6 col-md-3">
                                <div class="stat-card">
                                    <div class="stat-number" id="progressPercentage">0%</div>
                                    <div class="stat-label">PROGRESSO GLOBAL</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <ul id="taskList" class="list-group"></ul>
                    <ul id="completedTasksList" class="list-group"></ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Task Edit Modal -->
    <div class="modal fade" id="taskEditModal" tabindex="-1" role="dialog" aria-labelledby="taskEditModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="taskEditModalLabel">Editar Atualização</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="taskEditForm" enctype="multipart/form-data">
                        <div class="form-group">
                            <label for="editTaskUser">Solicitado por:</label>
                            <div class="row">
                                <div class="col-md-8">
                                    <input type="text" class="form-control" id="editTaskUser" required>
                                </div>
                                <div class="col-md-4">
                                    <select class="form-control" id="editTaskType" required>
                                        <option value="Atualização">Atualização</option>
                                        <option value="Bug">Bug</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="editTaskText">Texto da Atualização:</label>
                            <input type="text" class="form-control" id="editTaskText" required>
                        </div>

                        <div class="form-group">
                            <label><i class="fas fa-list-ul mr-2"></i>Lista de Tarefas:</label>
                            <div class="todo-list-container">
                                <div class="todo-items" id="editTodoItems">
                                    <!-- Todo items will be loaded here dynamically -->
                                </div>
                                <div class="add-todo-form mt-2">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="editNewTodoItem" placeholder="Adicionar nova tarefa...">
                                        <div class="input-group-append">
                                            <button type="button" class="btn btn-primary" id="editAddTodoBtn">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="taskProgress">Progresso da Tarefa:</label>
                            <div class="progress-slider-container">
                                <input type="range" class="form-control-range" id="taskProgress" min="0" max="100" value="0">
                                <div class="progress-slider-labels">
                                    <span>0%</span>
                                    <span id="progressValue">0%</span>
                                    <span>100%</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="taskCompleted">
                                <label class="custom-control-label" for="taskCompleted">Marcar como concluída</label>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>Plataforma:</label>
                            <div class="platform-buttons">
                                <button type="button" class="btn platform-btn web" data-platform="Web">
                                    <i class="fas fa-globe"></i><span>&nbsp;Web</span>
                                </button>
                                <button type="button" class="btn platform-btn android" data-platform="Android">
                                    <i class="fab fa-android"></i><span>&nbsp;Android</span>
                                </button>
                                <button type="button" class="btn platform-btn ios" data-platform="iOS">
                                    <i class="fab fa-apple"></i><span>&nbsp;iOS</span>
                                </button>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>Status:</label>
                            <div class="label-buttons">
                                <a href="#" class="badge badge-success label-btn" data-label="Em Desenvolvimento">Em Desenvolvimento</a>
                                <a href="#" class="badge badge-warning label-btn" data-label="Aguardando Resposta">Aguardando Resposta</a>
                                <a href="#" class="badge badge-info label-btn" data-label="Próximo Update">Próximo Update</a>
                                <a href="#" class="badge badge-secondary label-btn" data-label="Não Prioritário">Não Prioritário</a>
                                <a href="#" class="badge badge-danger label-btn" data-label="Cancelado">Cancelado</a>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>Imagens:</label>
                            <div class="task-images-container mb-3" id="editTaskImages">
                                <!-- Images will be loaded here dynamically -->
                            </div>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="editTaskImageUpload" accept="image/*" multiple>
                                <label class="custom-file-label" for="editTaskImageUpload">Escolher imagens...</label>
                            </div>
                            <div class="clipboard-paste-hint">
                                <i class="fas fa-info-circle"></i> Também pode colar imagens diretamente da área de transferência (Ctrl+V). Máximo de 6 imagens.
                            </div>
                            <div class="image-preview-container mt-3" id="editImagePreviewContainer">
                                <!-- New image previews will appear here -->
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label><i class="fas fa-comments mr-2"></i>Comentários:</label>
                            <div class="comments-section">
                                <div class="comments-list" id="taskCommentsList">
                                    <!-- Comments will be loaded here dynamically -->
                                </div>
                                <div class="add-comment-form mt-3">
                                    <div class="form-row">
                                        <div class="col-md-4">
                                            <input type="text" class="form-control" id="commentAuthor" placeholder="Seu nome" required>
                                        </div>
                                        <div class="col-md-6">
                                            <input type="text" class="form-control" id="commentText" placeholder="Adicionar comentário..." required>
                                        </div>
                                        <div class="col-md-2">
                                            <button type="button" class="btn btn-primary btn-block" id="addComment">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <div class="modal-footer-left">
                        <button type="button" class="btn btn-danger" id="deleteTask">Apagar</button>
                        <button type="button" class="btn btn-success" id="completeTask" data-toggle="modal" data-target="#completeConfirmModal">
                            <i class="fas fa-check-circle mr-1"></i>Concluída
                        </button>
                    </div>
                    <div class="modal-footer-right">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                        <button type="button" class="btn btn-primary" id="saveTaskChanges">Salvar Alterações</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteConfirmModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirmar Exclusão</h5>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body">
                    Tem a certeza que deseja apagar esta atualização?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteTask">Apagar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Complete Task Confirmation Modal -->
    <div class="modal fade" id="completeConfirmModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Confirmar Conclusão</h5>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>
                <div class="modal-body">
                    Tem a certeza que deseja marcar esta atualização como concluída?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-success" id="confirmCompleteTask">
                        <i class="fas fa-check-circle mr-1"></i>Concluir
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Task Modal -->
    <div class="modal fade" id="addTaskModal" tabindex="-1" role="dialog" aria-labelledby="addTaskModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addTaskModalLabel">Nova Atualização</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="addTaskForm" enctype="multipart/form-data">
                        <div class="form-group">
                            <label for="newTaskUser">Solicitado por:</label>
                            <div class="row">
                                <div class="col-md-8">
                                    <input type="text" class="form-control" id="newTaskUser" required>
                                </div>
                                <div class="col-md-4">
                                    <select class="form-control" id="newTaskType" required>
                                        <option value="Atualização" selected>Atualização</option>
                                        <option value="Bug">Bug</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="newTaskText">Texto da Atualização:</label>
                            <input type="text" class="form-control" id="newTaskText" required>
                        </div>

                        <div class="form-group">
                            <label><i class="fas fa-list-ul mr-2"></i>Lista de Tarefas:</label>
                            <div class="todo-list-container">
                                <div class="todo-items" id="newTodoItems">
                                    <!-- Todo items will be added here dynamically -->
                                </div>
                                <div class="add-todo-form mt-2">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="newTodoItem" placeholder="Adicionar nova tarefa...">
                                        <div class="input-group-append">
                                            <button type="button" class="btn btn-primary" id="newAddTodoBtn">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>Plataforma:</label>
                            <div class="platform-buttons">
                                <button type="button" class="btn platform-btn web active" data-platform="Web">
                                    <i class="fas fa-globe"></i><span>&nbsp;Web</span>
                                </button>
                                <button type="button" class="btn platform-btn android" data-platform="Android">
                                    <i class="fab fa-android"></i><span>&nbsp;Android</span>
                                </button>
                                <button type="button" class="btn platform-btn ios" data-platform="iOS">
                                    <i class="fab fa-apple"></i><span>&nbsp;iOS</span>
                                </button>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label>Imagens:</label>
                            <div class="custom-file">
                                <input type="file" class="custom-file-input" id="newTaskImageUpload" accept="image/*" multiple>
                                <label class="custom-file-label" for="newTaskImageUpload">Escolher imagens...</label>
                            </div>
                            <div class="clipboard-paste-hint">
                                <i class="fas fa-info-circle"></i> Também pode colar imagens diretamente da área de transferência (Ctrl+V). Máximo de 6 imagens.
                            </div>
                            <div class="image-preview-container mt-3" id="newImagePreviewContainer">
                                <!-- Image previews will appear here -->
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" id="confirmAddTask">Adicionar</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
    <!-- Font Awesome CSS (more reliable than JS version) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <script src="js/todo.js"></script>
    
    <!-- Image Modal -->
    <div id="imageModal" class="image-modal">
        <span class="image-modal-close">&times;</span>
        <img class="image-modal-content" id="modalImage">
    </div>
    
    <!-- Alert Container -->
    <div id="alertContainer" class="alert-container"></div>
    
    <!-- Settings Modal -->
    <div class="modal fade" id="settingsModal" tabindex="-1" role="dialog" aria-labelledby="settingsModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="settingsModalLabel">Definições</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="settingsForm">
                        <div class="form-group">
                            <label for="siteTitle">Título:</label>
                            <input type="text" class="form-control" id="siteTitle" value="CoolFM - Updates">
                        </div>
                    </form>
                    <div class="admin-zone mt-4">
                        <h6 class="text-info">Administração</h6>
                        <hr>
                        <button id="logoutBtn" class="btn btn-warning mb-3">
                            <i class="fas fa-sign-out-alt mr-2"></i>Terminar Sessão de Administração
                        </button>
                    </div>
                    
                    <div class="danger-zone mt-4">
                        <h6 class="text-danger">Zona de Perigo</h6>
                        <hr>
                        <p class="text-muted">Atenção: Esta ação não pode ser desfeita.</p>
                        <button id="resetTasksBtn" class="btn btn-danger">Apagar Todas as Atualizações</button>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancelar</button>
                    <button type="button" class="btn btn-primary" id="saveSettings">Guardar</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Footer -->
    <footer class="footer">
        <div class="container text-center">
            <span class="text-muted">Tasks Manager v1.2 - Malha &copy; <script>document.write(new Date().getFullYear())</script></span>
        </div>
    </footer>
</body>
</html>
