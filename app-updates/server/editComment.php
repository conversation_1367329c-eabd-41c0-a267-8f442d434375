<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');

header('Content-Type: application/json');

if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
    exit;
}

// Get POST data
$commentId = $_POST['commentId'] ?? '';
$newComment = $_POST['comment'] ?? '';
$author = $_POST['author'] ?? '';

// Validate input
if (empty($commentId) || empty($newComment) || empty($author)) {
    echo json_encode(['status' => 'error', 'message' => 'Comment ID, comment text, and author are required']);
    exit;
}

// Sanitize input
$commentId = trim($commentId);
$newComment = trim($newComment);
$author = trim($author);

// Comments file path
$commentsFile = __DIR__ . '/comments/comments.txt';

// Check if comments file exists
if (!file_exists($commentsFile)) {
    echo json_encode(['status' => 'error', 'message' => 'Comments file not found']);
    exit;
}

try {
    // Read all comments
    $fp = fopen($commentsFile, 'r');
    if (!$fp) {
        throw new Exception("Failed to open comments file for reading");
    }

    if (!flock($fp, LOCK_SH)) {
        fclose($fp);
        throw new Exception("Failed to acquire shared lock on comments file");
    }

    $contents = '';
    while (!feof($fp)) {
        $contents .= fread($fp, 8192);
    }

    flock($fp, LOCK_UN);
    fclose($fp);

    // Parse comments
    $commentsArray = explode(PHP_EOL, $contents);
    $commentsArray = array_filter($commentsArray, 'trim');
    
    $updated = false;
    $updatedComments = [];
    
    foreach ($commentsArray as $commentLine) {
        $parts = explode('|', $commentLine, 5);
        
        if (count($parts) < 5) {
            $updatedComments[] = $commentLine; // Keep invalid lines as-is
            continue;
        }
        
        $currentCommentId = $parts[0];
        $taskId = $parts[1];
        $originalAuthor = $parts[2];
        $timestamp = $parts[3];
        $originalComment = $parts[4];
        
        if ($currentCommentId === $commentId) {
            // Update this comment
            $newTimestamp = date('Y-m-d H:i:s');
            $updatedLine = $commentId . '|' . $taskId . '|' . $author . '|' . $newTimestamp . '|' . $newComment;
            $updatedComments[] = $updatedLine;
            $updated = true;
        } else {
            $updatedComments[] = $commentLine;
        }
    }
    
    if (!$updated) {
        echo json_encode(['status' => 'error', 'message' => 'Comment not found']);
        exit;
    }
    
    // Write updated comments back to file
    $fp = fopen($commentsFile, 'w');
    if (!$fp) {
        throw new Exception("Failed to open comments file for writing");
    }
    
    if (!flock($fp, LOCK_EX)) {
        fclose($fp);
        throw new Exception("Failed to acquire exclusive lock on comments file");
    }
    
    $newContents = implode(PHP_EOL, $updatedComments) . PHP_EOL;
    if (fwrite($fp, $newContents) === false) {
        flock($fp, LOCK_UN);
        fclose($fp);
        throw new Exception("Failed to write updated comments to file");
    }
    
    flock($fp, LOCK_UN);
    fclose($fp);
    
    error_log("Comment edited successfully: CommentID=$commentId, Author=$author");
    
    echo json_encode([
        'status' => 'success', 
        'message' => 'Comment updated successfully',
        'commentId' => $commentId
    ]);
    
} catch (Exception $e) {
    error_log("Error editing comment: " . $e->getMessage());
    echo json_encode(['status' => 'error', 'message' => 'Failed to edit comment: ' . $e->getMessage()]);
} 