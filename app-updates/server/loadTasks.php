<?php
// Enable error reporting for debugging (comment out in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Log errors to a file
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');

// Function to load comments for a specific task
function loadTaskComments($taskId) {
    $commentsFile = __DIR__ . '/comments/comments.txt';
    
    if (!file_exists($commentsFile)) {
        return [];
    }
    
    try {
        $fp = fopen($commentsFile, 'r');
        if (!$fp) {
            return [];
        }

        if (!flock($fp, LOCK_SH)) {
            fclose($fp);
            return [];
        }

        $contents = '';
        while (!feof($fp)) {
            $contents .= fread($fp, 8192);
        }

        flock($fp, LOCK_UN);
        fclose($fp);

        $commentsArray = explode(PHP_EOL, $contents);
        $commentsArray = array_filter($commentsArray, 'trim');
        
        $taskComments = [];
        
        foreach ($commentsArray as $commentLine) {
            $parts = explode('|', $commentLine, 5);
            
            if (count($parts) < 5) {
                continue;
            }
            
            $commentId = $parts[0];
            $commentTaskId = $parts[1];
            $author = $parts[2];
            $timestamp = $parts[3];
            $comment = $parts[4];
            
            if ($commentTaskId === $taskId) {
                $formattedTimestamp = '';
                try {
                    $date = new DateTime($timestamp);
                    $formattedTimestamp = $date->format('d-m H:i');
                } catch (Exception $e) {
                    $formattedTimestamp = $timestamp;
                }
                
                $taskComments[] = [
                    'id' => $commentId,
                    'author' => $author,
                    'timestamp' => $timestamp,
                    'formattedTimestamp' => $formattedTimestamp,
                    'comment' => $comment
                ];
            }
        }
        
        // Sort comments by timestamp (newest first)
        usort($taskComments, function($a, $b) {
            return strtotime($b['timestamp']) - strtotime($a['timestamp']);
        });
        
        return $taskComments;
        
    } catch (Exception $e) {
        return [];
    }
}

// Use absolute path for the tasks file
$filePath = __DIR__ . '/tasks.txt';

// Check if file exists and is readable
if(file_exists($filePath) && is_readable($filePath)) {
    try {
        // Use file locking to prevent race conditions
        $fp = fopen($filePath, 'r');
        if (!$fp) {
            throw new Exception("Failed to open file for reading");
        }

        if (!flock($fp, LOCK_SH)) { // Shared lock for reading
            fclose($fp);
            throw new Exception("Failed to acquire shared lock on file");
        }

        // Read the file contents
        $contents = '';
        while (!feof($fp)) {
            $contents .= fread($fp, 8192);
        }

        // Release the lock and close the file
        flock($fp, LOCK_UN);
        fclose($fp);

        $tasksArray = explode(PHP_EOL, $contents);
        $tasksArray = array_filter($tasksArray, 'trim'); // Remove empty lines

        // Debug log the tasks
        error_log("Tasks loaded from file: " . print_r($tasksArray, true));

        // First, separate completed and non-completed tasks
        $completedTasks = [];
        $nonCompletedTasks = [];

        foreach ($tasksArray as $task) {
            $parts = explode("|", $task);
            // Check if we have the expected number of parts (5 with platform, 4 without)
            if (count($parts) < 4) {
                error_log("Invalid task format: $task");
                continue;
            }
            
            // Handle both old format (4 parts) and new format (5 parts with platform)
            $taskId = $parts[0];
            $dateAdded = $parts[1];
            $taskText = $parts[2];
            $completed = $parts[3];
            $platform = isset($parts[4]) ? $parts[4] : 'Web';
            
            // If platform is "Nenhuma", set it to "Web" as default
            if ($platform === 'Nenhuma') {
                $platform = 'Web';
            }
            
            // Ensure completed is treated as a string for comparison
            $completed = trim($completed);
            
            error_log("Processing task: ID=$taskId, Text=$taskText, Completed=$completed, Platform=$platform");
            
            // Reconstruct the task with platform if it was missing
            if (count($parts) < 5) {
                $task = "{$taskId}|{$dateAdded}|{$taskText}|{$completed}|{$platform}";
            }
            
            if ($completed === '1') {
                // Extract completion date from task text
                $completionDate = '';
                if (preg_match('/\(Concluído em: ([\d-]+)\)/', $taskText, $matches)) {
                    $completionDate = $matches[1];
                    error_log("Found completion date: $completionDate for task: $taskId");
                } else {
                    error_log("No completion date found for completed task: $taskId");
                }
                
                // Store both task and completion date
                $completedTasks[] = [
                    'task' => $task,
                    'completionDate' => $completionDate
                ];
            } else {
                $nonCompletedTasks[] = $task;
            }
        }

        error_log("Completed tasks count: " . count($completedTasks));
        error_log("Non-completed tasks count: " . count($nonCompletedTasks));

        // Sort completed tasks by completion date (newest first)
        usort($completedTasks, function($a, $b) {
            // If dates can't be parsed, use a default date
            $dateA = !empty($a['completionDate']) ? 
                DateTime::createFromFormat('d-m-Y', $a['completionDate']) : 
                new DateTime('1970-01-01');
            
            $dateB = !empty($b['completionDate']) ? 
                DateTime::createFromFormat('d-m-Y', $b['completionDate']) : 
                new DateTime('1970-01-01');
            
            if ($dateA === false) $dateA = new DateTime('1970-01-01');
            if ($dateB === false) $dateB = new DateTime('1970-01-01');
            
            return $dateB <=> $dateA; // Reverse order (newest first)
        });

        // Extract just the tasks after sorting
        $completedTasks = array_map(function($item) {
            return $item['task'];
        }, $completedTasks);

        // Sort non-completed tasks by type and maintain order for development tasks
        $devTasksByOrder = [];
        $nonDevTasks = [];
        
        foreach ($nonCompletedTasks as $task) {
            $parts = explode("|", $task);
            if (strpos($parts[2], 'Em Desenvolvimento - ') === 0) {
                $devTasksByOrder[] = $task;
            } else {
                $nonDevTasks[] = $task;
            }
        }

        // Sort development tasks by progress percentage (highest first)
        usort($devTasksByOrder, function($a, $b) {
            $partsA = explode("|", $a);
            $partsB = explode("|", $b);
            
            // Get progress values (default to 0 if not set)
            $progressA = isset($partsA[7]) ? intval($partsA[7]) : 0;
            $progressB = isset($partsB[7]) ? intval($partsB[7]) : 0;
            
            // Sort by progress percentage (highest first)
            return $progressB - $progressA; // Descending order
        });

        // Sort other non-completed tasks by progress percentage (highest first), then by priority
        usort($nonDevTasks, function($a, $b) {
            $partsA = explode("|", $a);
            $partsB = explode("|", $b);
            
            list(, , $textA) = $partsA;
            list(, , $textB) = $partsB;
            
            // Get progress values (default to 0 if not set)
            $progressA = isset($partsA[7]) ? intval($partsA[7]) : 0;
            $progressB = isset($partsB[7]) ? intval($partsB[7]) : 0;
            
            // First sort by progress percentage (highest first)
            if ($progressA !== $progressB) {
                return $progressB - $progressA; // Descending order
            }
            
            // If progress is the same, sort by priority
            $getPriority = function($text) {
                if (strpos($text, 'Em Desenvolvimento - ') === 0) return 1;
                if (strpos($text, 'Aguardando Resposta - ') === 0) return 2;
                if (strpos($text, 'Aguardando Desenvolvimento') === 0) return 3;
                if (strpos($text, 'Próximo Update - ') === 0) return 4;
                if (strpos($text, 'Não Prioritário - ') === 0) return 5;
                if (strpos($text, 'Cancelado - ') === 0) return 6;
                return 3;
            };
            
            return $getPriority($textA) - $getPriority($textB);
        });

        // Merge all tasks back together
        $tasksArray = array_merge($devTasksByOrder, $nonDevTasks, $completedTasks);
        error_log("Final merged tasks count: " . count($tasksArray));

        // Output tasks
        foreach($tasksArray as $task) {
            $parts = explode("|", $task);
            $taskId = $parts[0];
            $dateAdded = $parts[1];
            $taskText = $parts[2];
            $completed = $parts[3];
            $platform = isset($parts[4]) ? $parts[4] : 'Web';
            // Get user name if available
            $userName = isset($parts[6]) ? $parts[6] : '';
            // Get progress if available
            $progress = isset($parts[7]) ? intval($parts[7]) : 0;
            // Get task type if available
            $taskType = isset($parts[8]) ? $parts[8] : 'Atualização';
            // Get todo list if available - ensure it's properly formatted
            $todoList = isset($parts[9]) ? $parts[9] : '';
            // If todoList is empty or just whitespace, set it to empty string
            if (trim($todoList) === '') {
                $todoList = '';
            }
            
            // If platform is "Nenhuma", set it to "Web" as default
            if ($platform === 'Nenhuma') {
                $platform = 'Web';
            }
            
            // Format the date to show only the date part (without time)
            $formattedDate = $dateAdded;
            if (strpos($dateAdded, ' ') !== false) {
                $dateParts = explode(' ', $dateAdded);
                $formattedDate = $dateParts[0];
            }
            
            // Convert YYYY-MM-DD format to DD-MM-YYYY if needed
            if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $formattedDate)) {
                $date = DateTime::createFromFormat('Y-m-d', $formattedDate);
                if ($date) {
                    $formattedDate = $date->format('d-m-Y');
                }
            }
            
            // Ensure completed is treated as a string for comparison
            $completed = trim($completed);
            
            // Split label and text
            $label = '';
            $taskText = $taskText;
            if(strpos($taskText, ' - ') !== false && $completed === '0') {
                list($label, $taskText) = explode(' - ', $taskText, 2);
            }

            $taskClass = ($completed === '1') ? 'completed-task' : 'not-completed-task';
            
            // Add canceled-task class for tasks with "Cancelado" label
            if ($label === 'Cancelado') {
                $taskClass .= ' canceled-task';
            }
            // Add waiting-response-task class for tasks with "Aguardando Resposta" label
            if ($label === 'Aguardando Resposta') {
                $taskClass .= ' waiting-response-task';
            }

            // Replace match expression with if-else for PHP compatibility
            $badgeClass = 'badge-secondary'; // Default value
            if ($label === 'Em Desenvolvimento') {
                $badgeClass = 'badge-success';
            } elseif ($label === 'Aguardando Resposta') {
                $badgeClass = 'badge-warning';
            } elseif ($label === 'Próximo Update') {
                $badgeClass = 'badge-info';
            } elseif ($label === 'Não Prioritário') {
                $badgeClass = 'badge-secondary';
            } elseif ($label === 'Cancelado') {
                $badgeClass = 'badge-danger';
            }

            // Update label handling - move ALL status badges to the right side
            $labelHtml = '';
            $rightLabelHtml = '';
            if ($completed === '0') {
                // All status labels go to the right side
                $rightLabelHtml = $label ?
                    "<span class='badge $badgeClass'>$label</span>" :
                    "<span class='badge badge-default'>Aguardando Desenvolvimento</span>";
            }

            // Handle multiple platforms (comma-separated)
            $platforms = array_map('trim', explode(',', $platform));
            $platformBadges = '';
            
            foreach ($platforms as $singlePlatform) {
                $iconPrefix = 'fas';
                $platformClass = 'web'; // Default to web
                $platformIcon = 'fa-globe';
                $platformText = $singlePlatform;
                
                switch($singlePlatform) {
                    case 'Web':
                        $platformClass = 'web';
                        $platformIcon = 'fa-globe';
                        break;
                    case 'Android':
                        $iconPrefix = 'fab';
                        $platformClass = 'android';
                        $platformIcon = 'fa-android';
                        break;
                    case 'iOS':
                        $iconPrefix = 'fab';
                        $platformClass = 'ios';
                        $platformIcon = 'fa-apple';
                        break;
                    default:
                        // Default to Web for any other value
                        $singlePlatform = 'Web';
                        $platformClass = 'web';
                        $platformIcon = 'fa-globe';
                        break;
                }
                
                $platformBadges .= '<span class="platform-badge ' . $platformClass . '"><i class="' . $iconPrefix . ' ' . $platformIcon . '"></i>&nbsp;<span>' . $platformText . '</span></span> ';
            }
            
            $platformBadge = trim($platformBadges);

            // Extract completion date if it exists
            $completionDate = '';
            if ($completed === '1' && preg_match('/\(Concluído em: ([\d-]+)\)/', $taskText, $matches)) {
                $completionDate = $matches[1];
                $taskText = preg_replace('/\s*\(Concluído em: .*?\)/', '', $taskText);
            }

            // Process images if they exist
            $imagesHtml = '';
            if (isset($parts[5]) && !empty($parts[5])) {
                $images = json_decode($parts[5], true);
                if ($images && is_array($images)) {
                    $imagesHtml = '<div class="task-images">';
                    foreach ($images as $image) {
                        if (isset($image['path']) && !empty($image['path'])) {
                            $imagePath = htmlspecialchars($image['path']);
                            $imagesHtml .= '
                                <div class="task-image-container">
                                    <img src="' . $imagePath . '" class="task-image" onclick="openImageModal(this.src)">
                                </div>';
                        }
                    }
                    $imagesHtml .= '</div>';
                }
            }

            // Load and format comments for this task
            $comments = loadTaskComments($taskId);
            $commentsHtml = '';
            if (!empty($comments)) {
                $commentsHtml = '<div class="task-comments-preview">';
                foreach ($comments as $comment) {
                    $escapedAuthor = htmlspecialchars($comment['author']);
                    $escapedComment = htmlspecialchars($comment['comment']);
                    $commentsHtml .= '
                        <div class="comment-preview-item">
                            <i class="fas fa-comment comment-icon"></i>
                            <div class="comment-preview-content">
                                <span class="comment-preview-author"><i class="fas fa-user"></i> ' . $escapedAuthor . '</span>
                                <div class="comment-preview-text">' . $escapedComment . '</div>
                            </div>
                        </div>';
                }
                $commentsHtml .= '</div>';
            }

            // Output the task HTML
            echo "<li class='list-group-item $taskClass' data-task-id='$taskId' data-completed='$completed' data-label='$label' data-platform='$platform' data-task-user='$userName'>
                <div class='task-row'>
                    <div class='task-left'>
                        <div class='task-meta'>
                            <div class='task-meta-left'>
                                <span class='badge-date'>{$formattedDate}</span>
                                " . ($userName ? "<span class='badge-user' title='Solicitado por: $userName'><i class='fas fa-user'></i> $userName</span>" : "") . "
                                " . ($taskType ? "<span class='badge-task-type " . ($taskType === 'Bug' ? 'badge-bug' : 'badge-update') . "' title='Tipo: $taskType'><i class='fas " . ($taskType === 'Bug' ? 'fa-bug' : 'fa-sync-alt') . "'></i> $taskType</span>" : "") . "
                                $platformBadge
                            </div>
                            " . ($rightLabelHtml ? "<div class='task-meta-right'><span class='task-label' data-label='$label'>$rightLabelHtml</span></div>" : "") . "
                        </div>
                        <div class='task-text-wrapper'>
                            <span class='task-text' data-original-text='$taskText'>$taskText</span>
                            " . ($completionDate ? "<span class='completion-date'>(Concluído em: $completionDate)</span>" : "") . "
                        </div>
                        " . ($completed === '0' && $progress > 0 ? "
                        <div class='task-progress-container'>
                            <div class='task-progress-bar'>
                                <div class='task-progress-fill' style='width: {$progress}%'></div>
                            </div>
                            <span class='task-progress-text' style='left: {$progress}%'>{$progress}%</span>
                        </div>" : "") . "
                        $commentsHtml
                        $imagesHtml
                    </div>
                    <div class='task-right'>
                        <button class='btn btn-sm task-edit-btn' data-toggle='modal' data-target='#taskEditModal'
                            data-task-id='$taskId'
                            data-task-text='$taskText'
                            data-platform='$platform'
                            data-label='$label'
                            data-completed='$completed'
                            data-task-user='$userName'
                            data-progress='$progress'
                            data-task-type='$taskType'
                            data-todo-list='$todoList'>
                            <i class='fas fa-edit'></i>
                        </button>
                    </div>
                </div>
            </li>";
        }
    } catch (Exception $e) {
        error_log("Error in loadTasks.php: " . $e->getMessage());
        echo "<div class='alert alert-danger'>Erro ao carregar as tarefas: " . $e->getMessage() . "</div>";
    }
} else {
    error_log("Tasks file not found or not readable: $filePath");
    echo "<div class='alert alert-warning'>Arquivo de tarefas não encontrado ou não pode ser lido.</div>";
}
?>
