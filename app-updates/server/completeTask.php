<?php
header('Content-Type: application/json');
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Set up error logging
$logFile = __DIR__ . '/error.log';
ini_set('error_log', $logFile);

if (!isset($_POST['taskId'])) {
    echo json_encode(['status' => 'error', 'message' => 'ID da tarefa não fornecido']);
    exit;
}

$taskId = $_POST['taskId'];
$tasksFile = __DIR__ . '/tasks.txt';

try {
    // Read all tasks
    $tasks = file_exists($tasksFile) ? file($tasksFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES) : [];
    $found = false;
    $updatedTasks = [];
    $completionDate = date('d-m-Y'); // Use the correct date format

    foreach ($tasks as $task) {
        $parts = explode('|', $task);
        if (count($parts) >= 4) {
            if ($parts[0] === $taskId) {
                // Get all the parts
                $taskId = $parts[0];
                $dateAdded = $parts[1];
                $taskText = $parts[2];
                $platform = isset($parts[4]) ? $parts[4] : 'Web';
                $images = isset($parts[5]) ? $parts[5] : '';
                $user = isset($parts[6]) ? $parts[6] : '';
                
                // Remove any existing completion date
                $taskText = preg_replace('/\s*\(Concluído em: .*?\)/', '', $taskText);
                
                // Add the new completion date
                $taskText .= " (Concluído em: {$completionDate})";
                
                // Create the updated task string
                $updatedTask = "{$taskId}|{$dateAdded}|{$taskText}|1|{$platform}|{$images}|{$user}";
                $updatedTasks[] = $updatedTask;
                $found = true;
            } else {
                $updatedTasks[] = $task;
            }
        }
    }

    if (!$found) {
        echo json_encode(['status' => 'error', 'message' => 'Tarefa não encontrada']);
        exit;
    }

    // Write back to file
    if (file_put_contents($tasksFile, implode("\n", $updatedTasks) . "\n") !== false) {
        echo json_encode(['status' => 'success']);
    } else {
        echo json_encode(['status' => 'error', 'message' => 'Erro ao salvar as alterações']);
    }
} catch (Exception $e) {
    error_log("Error in completeTask.php: " . $e->getMessage());
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
} 