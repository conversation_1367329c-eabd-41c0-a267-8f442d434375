<?php
class BackupManager {
    private $backupDir;
    private $maxBackups = 50; // Maximum number of backups to keep
    private $tasksFile;

    public function __construct($tasksFilePath) {
        $this->tasksFile = $tasksFilePath;
        $this->backupDir = dirname($tasksFilePath);
        $this->ensureBackupDirectory();
    }

    private function ensureBackupDirectory() {
        if (!is_dir($this->backupDir)) {
            if (!mkdir($this->backupDir, 0755, true)) {
                throw new Exception("Failed to create backup directory");
            }
        }
    }

    public function createBackup($operation = '') {
        try {
            // Ensure source file exists
            if (!file_exists($this->tasksFile)) {
                throw new Exception("Source file does not exist");
            }

            // Generate backup filename with timestamp and operation
            $timestamp = date('Y-m-d_H-i-s');
            $operationSlug = $operation ? '_' . preg_replace('/[^a-z0-9-]/', '-', strtolower($operation)) : '';
            $backupFile = $this->backupDir . '/tasks_' . $timestamp . $operationSlug . '.txt';

            // Create the backup
            if (!copy($this->tasksFile, $backupFile)) {
                throw new Exception("Failed to create backup");
            }

            // Rotate old backups
            $this->rotateBackups();

            return true;
        } catch (Exception $e) {
            error_log("Backup failed: " . $e->getMessage());
            return false;
        }
    }

    public function rotateBackups() {
        try {
            // Get all backup files
            $backups = glob($this->backupDir . '/tasks_*.txt');
            
            // Sort by modification time, newest first
            usort($backups, function($a, $b) {
                return filemtime($b) - filemtime($a);
            });

            // Remove old backups if we have more than the maximum
            if (count($backups) > $this->maxBackups) {
                for ($i = $this->maxBackups; $i < count($backups); $i++) {
                    unlink($backups[$i]);
                }
            }
        } catch (Exception $e) {
            error_log("Backup rotation failed: " . $e->getMessage());
        }
    }

    public function restoreFromBackup($backupFile) {
        try {
            // Validate backup file
            if (!file_exists($backupFile)) {
                throw new Exception("Backup file does not exist");
            }

            // Create a backup of current state before restore
            $this->createBackup('before_restore');

            // Restore from backup
            if (!copy($backupFile, $this->tasksFile)) {
                throw new Exception("Failed to restore from backup");
            }

            return true;
        } catch (Exception $e) {
            error_log("Restore failed: " . $e->getMessage());
            return false;
        }
    }

    public function listBackups() {
        try {
            $backups = glob($this->backupDir . '/tasks_*.txt');
            $backupList = [];

            foreach ($backups as $backup) {
                $backupList[] = [
                    'file' => basename($backup),
                    'date' => date('Y-m-d H:i:s', filemtime($backup)),
                    'size' => filesize($backup)
                ];
            }

            // Sort by date, newest first
            usort($backupList, function($a, $b) {
                return strtotime($b['date']) - strtotime($a['date']);
            });

            return $backupList;
        } catch (Exception $e) {
            error_log("Failed to list backups: " . $e->getMessage());
            return [];
        }
    }
}
?> 