<?php
header('Content-Type: application/json');
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Set up error logging
$logFile = __DIR__ . '/error.log';
ini_set('error_log', $logFile);

try {
    // Check if task ID is provided
    if (!isset($_GET['taskId'])) {
        throw new Exception("Task ID is required");
    }
    
    $taskId = trim($_GET['taskId']);
    
    // Get the tasks file path
    $filePath = __DIR__ . '/tasks.txt';
    
    if (!file_exists($filePath)) {
        throw new Exception("Tasks file not found");
    }
    
    if (!is_readable($filePath)) {
        throw new Exception("Tasks file not readable");
    }
    
    // Read the file
    $contents = file_get_contents($filePath);
    if ($contents === false) {
        throw new Exception("Failed to read tasks file");
    }
    
    $tasks = explode(PHP_EOL, $contents);
    $tasks = array_filter($tasks, 'trim'); // Remove empty lines
    
    $images = [];
    $found = false;
    
    foreach ($tasks as $task) {
        $parts = explode("|", $task);
        if (count($parts) < 4) {
            continue;
        }
        
        $currentTaskId = $parts[0];
        
        if ($currentTaskId === $taskId && isset($parts[5]) && !empty($parts[5])) {
            $found = true;
            $taskImages = json_decode($parts[5], true);
            
            if (is_array($taskImages)) {
                $images = $taskImages;
            }
            
            break;
        }
    }
    
    if (!$found) {
        echo json_encode([
            'status' => 'success',
            'message' => 'No images found for this task',
            'images' => []
        ]);
        exit;
    }
    
    echo json_encode([
        'status' => 'success',
        'images' => $images
    ]);
    
} catch (Exception $e) {
    error_log("Error in getTaskImages.php: " . $e->getMessage());
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
?> 