<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');

header('Content-Type: application/json');

if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
    exit;
}

// Get task ID from query parameter
$taskId = $_GET['taskId'] ?? '';

if (empty($taskId)) {
    echo json_encode(['status' => 'error', 'message' => 'Task ID is required']);
    exit;
}

$taskId = trim($taskId);

// Comments file path
$commentsFile = __DIR__ . '/comments/comments.txt';

// Check if comments file exists
if (!file_exists($commentsFile)) {
    echo json_encode(['status' => 'success', 'comments' => []]);
    exit;
}

try {
    // Use file locking to prevent race conditions
    $fp = fopen($commentsFile, 'r');
    if (!$fp) {
        throw new Exception("Failed to open comments file for reading");
    }

    if (!flock($fp, LOCK_SH)) {
        fclose($fp);
        throw new Exception("Failed to acquire shared lock on comments file");
    }

    // Read all comments
    $contents = '';
    while (!feof($fp)) {
        $contents .= fread($fp, 8192);
    }

    // Release lock and close file
    flock($fp, LOCK_UN);
    fclose($fp);

    // Parse comments
    $commentsArray = explode(PHP_EOL, $contents);
    $commentsArray = array_filter($commentsArray, 'trim'); // Remove empty lines
    
    $taskComments = [];
    
    foreach ($commentsArray as $commentLine) {
        $parts = explode('|', $commentLine, 5); // Limit to 5 parts in case comment contains pipes
        
        if (count($parts) < 5) {
            continue; // Skip invalid lines
        }
        
        $commentId = $parts[0];
        $commentTaskId = $parts[1];
        $author = $parts[2];
        $timestamp = $parts[3];
        $comment = $parts[4];
        
        // Only include comments for the requested task
        if ($commentTaskId === $taskId) {
            // Format timestamp for display
            $formattedTimestamp = '';
            try {
                $date = new DateTime($timestamp);
                $formattedTimestamp = $date->format('d-m-Y H:i');
            } catch (Exception $e) {
                $formattedTimestamp = $timestamp; // Fallback to original
            }
            
            $taskComments[] = [
                'id' => $commentId,
                'author' => $author,
                'timestamp' => $timestamp,
                'formattedTimestamp' => $formattedTimestamp,
                'comment' => $comment
            ];
        }
    }
    
    // Sort comments by timestamp (newest first)
    usort($taskComments, function($a, $b) {
        return strtotime($b['timestamp']) - strtotime($a['timestamp']);
    });
    
    echo json_encode(['status' => 'success', 'comments' => $taskComments]);
    
} catch (Exception $e) {
    error_log("Error loading comments: " . $e->getMessage());
    echo json_encode(['status' => 'error', 'message' => 'Failed to load comments: ' . $e->getMessage()]);
} 