<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');

header('Content-Type: application/json');

if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['status' => 'error', 'message' => 'Method not allowed']);
    exit;
}

// Get POST data
$taskId = $_POST['taskId'] ?? '';
$comment = $_POST['comment'] ?? '';
$author = $_POST['author'] ?? '';

// Validate input
if (empty($taskId) || empty($comment) || empty($author)) {
    echo json_encode(['status' => 'error', 'message' => 'Task ID, comment, and author are required']);
    exit;
}

// Sanitize input
$taskId = trim($taskId);
$comment = trim($comment);
$author = trim($author);

// Create comments directory if it doesn't exist
$commentsDir = __DIR__ . '/comments';
if (!is_dir($commentsDir)) {
    if (!mkdir($commentsDir, 0755, true)) {
        echo json_encode(['status' => 'error', 'message' => 'Failed to create comments directory']);
        exit;
    }
}

// Comments file path
$commentsFile = $commentsDir . '/comments.txt';

try {
    // Generate unique comment ID
    $commentId = uniqid();
    
    // Create timestamp
    $timestamp = date('Y-m-d H:i:s');
    
    // Format: commentId|taskId|author|timestamp|comment
    $commentLine = $commentId . '|' . $taskId . '|' . $author . '|' . $timestamp . '|' . $comment . PHP_EOL;
    
    // Use file locking to prevent race conditions
    $fp = fopen($commentsFile, 'a');
    if (!$fp) {
        throw new Exception("Failed to open comments file for writing");
    }
    
    if (!flock($fp, LOCK_EX)) {
        fclose($fp);
        throw new Exception("Failed to acquire exclusive lock on comments file");
    }
    
    // Write the comment
    if (fwrite($fp, $commentLine) === false) {
        flock($fp, LOCK_UN);
        fclose($fp);
        throw new Exception("Failed to write comment to file");
    }
    
    // Release lock and close file
    flock($fp, LOCK_UN);
    fclose($fp);
    
    // Log success
    error_log("Comment added successfully: TaskID=$taskId, Author=$author");
    
    echo json_encode([
        'status' => 'success', 
        'message' => 'Comment added successfully',
        'commentId' => $commentId,
        'timestamp' => $timestamp
    ]);
    
} catch (Exception $e) {
    error_log("Error adding comment: " . $e->getMessage());
    echo json_encode(['status' => 'error', 'message' => 'Failed to add comment: ' . $e->getMessage()]);
} 