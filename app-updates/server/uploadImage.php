<?php
header('Content-Type: application/json');
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Set up error logging
$logFile = __DIR__ . '/error.log';
ini_set('error_log', $logFile);

try {
    // Check if taskId is provided
    if (!isset($_POST['taskId'])) {
        throw new Exception("Task ID is required");
    }
    
    $taskId = trim($_POST['taskId']);
    
    // Create uploads directory if it doesn't exist
    $uploadsDir = __DIR__ . '/../uploads';
    if (!file_exists($uploadsDir)) {
        if (!mkdir($uploadsDir, 0755, true)) {
            throw new Exception("Failed to create uploads directory");
        }
    }
    
    // Create task-specific directory
    $taskDir = $uploadsDir . '/' . $taskId;
    if (!file_exists($taskDir)) {
        if (!mkdir($taskDir, 0755, true)) {
            throw new Exception("Failed to create task directory");
        }
    }
    
    // Check if files were uploaded
    if (!isset($_FILES['images']) || empty($_FILES['images']['name'][0])) {
        throw new Exception("No images uploaded");
    }
    
    $uploadedFiles = [];
    $errors = [];
    
    // Process each uploaded file
    $fileCount = count($_FILES['images']['name']);
    for ($i = 0; $i < $fileCount; $i++) {
        if ($_FILES['images']['error'][$i] === UPLOAD_ERR_OK) {
            $tmpName = $_FILES['images']['tmp_name'][$i];
            $fileName = $_FILES['images']['name'][$i];
            
            // Generate a unique filename
            $fileExtension = pathinfo($fileName, PATHINFO_EXTENSION);
            $newFileName = uniqid() . '.' . $fileExtension;
            $targetPath = $taskDir . '/' . $newFileName;
            
            // Check if it's a valid image
            $imageInfo = getimagesize($tmpName);
            if ($imageInfo === false) {
                $errors[] = "File '$fileName' is not a valid image";
                continue;
            }
            
            // Move the uploaded file
            if (move_uploaded_file($tmpName, $targetPath)) {
                $uploadedFiles[] = [
                    'name' => $fileName,
                    'path' => 'uploads/' . $taskId . '/' . $newFileName,
                    'url' => '../uploads/' . $taskId . '/' . $newFileName
                ];
            } else {
                $errors[] = "Failed to upload file '$fileName'";
            }
        } else {
            $errors[] = "Error uploading file: " . $_FILES['images']['error'][$i];
        }
    }
    
    // Update task data with image information
    if (!empty($uploadedFiles)) {
        $filePath = __DIR__ . '/tasks.txt';
        
        if (!file_exists($filePath)) {
            throw new Exception("Tasks file not found");
        }
        
        if (!is_readable($filePath) || !is_writable($filePath)) {
            throw new Exception("Tasks file not accessible");
        }
        
        // Use file locking to prevent race conditions
        $fp = fopen($filePath, 'c+');
        if (!$fp) {
            throw new Exception("Failed to open tasks file");
        }
        
        if (!flock($fp, LOCK_EX)) {
            fclose($fp);
            throw new Exception("Failed to acquire exclusive lock on file");
        }
        
        // Read the file contents
        $contents = '';
        while (!feof($fp)) {
            $contents .= fread($fp, 8192);
        }
        
        $tasks = explode(PHP_EOL, $contents);
        $tasks = array_filter($tasks, 'trim'); // Remove empty lines
        
        $updatedTasks = [];
        $found = false;
        
        foreach ($tasks as $task) {
            $parts = explode("|", $task);
            if (count($parts) < 4) {
                continue;
            }
            
            $currentTaskId = $parts[0];
            
            if ($currentTaskId === $taskId) {
                // Get existing images if any
                $existingImages = [];
                if (isset($parts[5])) {
                    $existingImages = json_decode($parts[5], true) ?: [];
                }
                
                // Add new images
                $allImages = array_merge($existingImages, $uploadedFiles);
                
                // Update the task with images
                $parts[5] = json_encode($allImages);
                $updatedTask = implode("|", $parts);
                $updatedTasks[] = $updatedTask;
                $found = true;
            } else {
                $updatedTasks[] = $task;
            }
        }
        
        if (!$found) {
            flock($fp, LOCK_UN);
            fclose($fp);
            throw new Exception("Task not found: $taskId");
        }
        
        // Write the updated content back to the file
        ftruncate($fp, 0); // Clear the file
        rewind($fp); // Go to the beginning
        $writeResult = fwrite($fp, implode(PHP_EOL, $updatedTasks) . PHP_EOL);
        
        // Release the lock and close the file
        flock($fp, LOCK_UN);
        fclose($fp);
        
        if ($writeResult === false) {
            throw new Exception("Failed to update task with image information");
        }
    }
    
    // Return response
    echo json_encode([
        'status' => 'success',
        'files' => $uploadedFiles,
        'errors' => $errors
    ]);
    
} catch (Exception $e) {
    error_log("Error in uploadImage.php: " . $e->getMessage());
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
?> 