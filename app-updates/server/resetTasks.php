<?php
// Set content type to JSON
header('Content-Type: application/json');

// Enable error reporting for debugging
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Log errors to a file
ini_set('log_errors', 1);
ini_set('error_log', 'error.log');

// Path to the tasks file
$tasksFile = 'tasks.txt';

// Path to the uploads directory
$uploadsDir = '../uploads';

try {
    // Check if the tasks file exists
    if (file_exists($tasksFile)) {
        // Create a backup of the tasks file
        $backupFile = 'tasks_backup_' . date('Y-m-d_H-i-s') . '.txt';
        copy($tasksFile, $backupFile);
        
        // Empty the tasks file
        file_put_contents($tasksFile, '');
        
        // Delete all uploaded images if the uploads directory exists
        if (is_dir($uploadsDir)) {
            // Function to recursively delete a directory
            function deleteDirectory($dir) {
                if (!is_dir($dir)) {
                    return;
                }
                
                $files = array_diff(scandir($dir), array('.', '..'));
                foreach ($files as $file) {
                    $path = $dir . '/' . $file;
                    if (is_dir($path)) {
                        deleteDirectory($path);
                    } else {
                        unlink($path);
                    }
                }
                
                // Don't remove the main uploads directory, just its contents
                if ($dir !== '../uploads') {
                    rmdir($dir);
                }
            }
            
            deleteDirectory($uploadsDir);
            
            // Recreate the uploads directory
            if (!is_dir($uploadsDir)) {
                mkdir($uploadsDir, 0777, true);
            }
        }
        
        echo json_encode([
            'status' => 'success',
            'message' => 'Todas as tarefas foram removidas com sucesso'
        ]);
    } else {
        // If the tasks file doesn't exist, create an empty one
        file_put_contents($tasksFile, '');
        
        echo json_encode([
            'status' => 'success',
            'message' => 'Ficheiro de tarefas criado com sucesso'
        ]);
    }
} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Erro ao redefinir tarefas: ' . $e->getMessage()
    ]);
} 