<?php
header('Content-Type: application/json');
ini_set('display_errors', 0);
error_reporting(E_ALL);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');

if (isset($_POST['tasks']) && is_array($_POST['tasks'])) {
    $filePath = __DIR__ . '/tasks.txt';
    
    if (!file_exists($filePath)) {
        error_log("File not found: $filePath");
        echo json_encode(['status' => 'error', 'message' => 'File not found']);
        exit;
    }
    
    $tasks = file($filePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    
    if ($tasks === false) {
        error_log("Failed to read file: $filePath");
        echo json_encode(['status' => 'error', 'message' => 'Failed to read file']);
        exit;
    }

    // Log the received task IDs
    error_log("Received task IDs for reordering: " . json_encode($_POST['tasks']));

    // Index the tasks by ID
    $tasksByType = [
        'dev' => [],    // Development tasks
        'other' => []   // All other tasks
    ];

    // First pass: separate tasks
    foreach ($tasks as $task) {
        $parts = explode('|', $task);
        // Check if we have at least the minimum required parts
        if (count($parts) >= 4) {
            $taskId = $parts[0];
            $taskText = $parts[2];
            $completed = trim($parts[3]);
            
            // Check if it's a development task and not completed
            if (strpos($taskText, 'Em Desenvolvimento - ') === 0 && $completed !== '1') {
                $tasksByType['dev'][$taskId] = $task;
                error_log("Found development task: $taskId - $taskText");
            } else {
                $tasksByType['other'][] = $task;
            }
        } else {
            error_log("Invalid task format: $task");
            $tasksByType['other'][] = $task; // Keep invalid tasks
        }
    }

    // Log the development tasks found
    error_log("Development tasks found: " . count($tasksByType['dev']));
    error_log("Development task IDs: " . json_encode(array_keys($tasksByType['dev'])));

    // Create final array with ordered development tasks
    $finalTasks = [];
    
    // Add development tasks in specified order
    foreach ($_POST['tasks'] as $taskId) {
        if (isset($tasksByType['dev'][$taskId])) {
            $finalTasks[] = $tasksByType['dev'][$taskId];
            error_log("Adding task in order: $taskId");
            unset($tasksByType['dev'][$taskId]);
        } else {
            error_log("Task ID not found in development tasks: $taskId");
        }
    }

    // Add remaining development tasks (if any)
    foreach ($tasksByType['dev'] as $taskId => $task) {
        $finalTasks[] = $task;
        error_log("Adding remaining development task: $taskId");
    }

    // Add all other tasks
    $finalTasks = array_merge($finalTasks, $tasksByType['other']);

    // Create a backup before saving
    $backupDir = __DIR__ . '/backup';
    if (!is_dir($backupDir)) {
        mkdir($backupDir, 0755, true);
    }
    $backupFilePath = $backupDir . '/tasks_order_' . date('Y-m-d_H-i-s') . '.txt';
    copy($filePath, $backupFilePath);

    // Log the final task count
    error_log("Final task count: " . count($finalTasks));

    if (file_put_contents($filePath, implode(PHP_EOL, $finalTasks) . PHP_EOL) !== false) {
        echo json_encode(['status' => 'success', 'saved' => true]);
        error_log("Successfully saved reordered tasks");
    } else {
        error_log("Failed to write file: $filePath");
        echo json_encode(['status' => 'error', 'message' => 'Failed to write file']);
    }
} else {
    error_log("Invalid request to updateOrder.php: " . json_encode($_POST));
    echo json_encode(['status' => 'error', 'message' => 'Invalid request']);
}
?>
