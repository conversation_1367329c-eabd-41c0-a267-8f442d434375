<?php
header('Content-Type: application/json');
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Set up error logging
$logFile = __DIR__ . '/error.log';
ini_set('error_log', $logFile);

// Include backup manager
require_once 'backup_manager.php';

try {
    if (!isset($_POST['taskId'])) {
        throw new Exception("Missing required parameter: taskId");
    }

    $taskIdToDelete = trim($_POST['taskId']);
    $filePath = __DIR__ . '/tasks.txt';
    
    if (!file_exists($filePath)) {
        throw new Exception("File not found: {$filePath}");
    }

    if (!is_readable($filePath) || !is_writable($filePath)) {
        throw new Exception("File not accessible: {$filePath}");
    }

    // Create a backup before deleting the task
    $backupManager = new BackupManager($filePath);
    $backupManager->createBackup('before_delete_task_' . $taskIdToDelete);

    // Use file locking to prevent race conditions
    $fp = fopen($filePath, 'c+');
    if (!$fp) {
        throw new Exception("Failed to open file for reading/writing");
    }

    if (!flock($fp, LOCK_EX)) {
        fclose($fp);
        throw new Exception("Failed to acquire exclusive lock on file");
    }

    // Read the file contents
    $contents = '';
    while (!feof($fp)) {
        $contents .= fread($fp, 8192);
    }

    $tasks = explode(PHP_EOL, $contents);
    $tasks = array_filter($tasks, 'trim'); // Remove empty lines

    if (empty($tasks)) {
        flock($fp, LOCK_UN);
        fclose($fp);
        throw new Exception("Failed to read tasks file or file is empty");
    }

    $updatedTasks = [];
    $found = false;

    foreach ($tasks as $task) {
        $parts = explode("|", $task);
        if (count($parts) < 4) {
            // Skip malformed tasks
            continue;
        }

        $taskId = $parts[0];
        
        if ($taskId === $taskIdToDelete) {
            $found = true;
            // Skip this task (don't add to updatedTasks)
        } else {
            // Keep this task
            $updatedTasks[] = $task;
        }
    }

    if (!$found) {
        flock($fp, LOCK_UN);
        fclose($fp);
        throw new Exception("Task not found: {$taskIdToDelete}");
    }

    // Write the updated content back to the file
    ftruncate($fp, 0); // Clear the file
    rewind($fp); // Go to the beginning
    $writeResult = fwrite($fp, implode(PHP_EOL, $updatedTasks) . PHP_EOL);
    
    // Release the lock and close the file
    flock($fp, LOCK_UN);
    fclose($fp);
    
    if ($writeResult === false) {
        throw new Exception("Failed to write to file");
    }

    echo json_encode(['status' => 'success']);
} catch (Exception $e) {
    error_log("Error deleting task: " . $e->getMessage());
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
}
?> 