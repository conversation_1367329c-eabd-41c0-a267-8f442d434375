<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');

header('Content-Type: application/json');

try {
    // Get the task ID and platform from the POST request
    $taskId = isset($_POST['taskId']) ? trim($_POST['taskId']) : '';
    $platform = isset($_POST['platform']) ? trim($_POST['platform']) : 'Web';
    
    // Validate the task ID
    if (empty($taskId)) {
        echo json_encode(['success' => false, 'message' => 'Task ID is required']);
        exit;
    }
    
    // Validate platform - handle multiple platforms separated by commas
    $validPlatforms = ['Web', 'Android', 'iOS'];
    $platformArray = array_map('trim', explode(',', $platform));
    $validatedPlatforms = [];
    
    foreach ($platformArray as $singlePlatform) {
        if (in_array($singlePlatform, $validPlatforms)) {
            $validatedPlatforms[] = $singlePlatform;
        }
    }
    
    // If no valid platforms found, default to Web
    if (empty($validatedPlatforms)) {
        $platform = 'Web';
    } else {
        $platform = implode(', ', $validatedPlatforms);
    }
    
    $filePath = __DIR__ . '/tasks.txt';
    
    // Check if the file exists
    if (!file_exists($filePath)) {
        throw new Exception("Tasks file not found");
    }
    
    // Use file locking to prevent race conditions
    $fp = fopen($filePath, 'r+');
    if (!$fp) {
        throw new Exception("Failed to open file for reading/writing");
    }
    
    if (!flock($fp, LOCK_EX)) {
        fclose($fp);
        throw new Exception("Failed to acquire exclusive lock on file");
    }
    
    // Read the file contents
    $contents = '';
    while (!feof($fp)) {
        $contents .= fread($fp, 8192);
    }
    
    $tasksArray = explode(PHP_EOL, $contents);
    $tasksArray = array_filter($tasksArray, 'trim'); // Remove empty lines
    
    $taskFound = false;
    $updatedTasks = [];
    
    foreach ($tasksArray as $task) {
        $parts = explode('|', $task);
        if (count($parts) < 4) {
            // Skip invalid tasks
            continue;
        }
        
        if ($parts[0] === $taskId) {
            // Found the task to update
            $taskFound = true;
            
            // Update the platform (parts[4])
            if (count($parts) >= 5) {
                $parts[4] = $platform;
            } else {
                // Add platform if it doesn't exist
                $parts[] = $platform;
            }
            
            // Rebuild the task string
            $task = implode('|', $parts);
        }
        
        $updatedTasks[] = $task;
    }
    
    if (!$taskFound) {
        flock($fp, LOCK_UN);
        fclose($fp);
        echo json_encode(['success' => false, 'message' => 'Task not found']);
        exit;
    }
    
    // Truncate the file
    ftruncate($fp, 0);
    rewind($fp);
    
    // Write the updated tasks back to the file
    foreach ($updatedTasks as $task) {
        fwrite($fp, $task . PHP_EOL);
    }
    
    flock($fp, LOCK_UN);
    fclose($fp);
    
    echo json_encode(['success' => true, 'message' => 'Platform updated successfully']);
    
} catch (Exception $e) {
    error_log("Error in updatePlatform.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?> 