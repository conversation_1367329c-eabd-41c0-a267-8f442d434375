<?php
header('Content-Type: application/json');
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Set up error logging
$logFile = __DIR__ . '/error.log';
ini_set('error_log', $logFile);

// Include backup manager
require_once 'backup_manager.php';

try {
    // Validate required parameters
    if (!isset($_POST['text']) || !isset($_POST['date']) || !isset($_POST['time'])) {
        throw new Exception("Missing required parameters: text, date, or time");
    }

    $text = trim($_POST['text']);
    $date = trim($_POST['date']);
    $time = trim($_POST['time']);
    $label = isset($_POST['label']) ? trim($_POST['label']) : '';
    $images = isset($_POST['images']) ? $_POST['images'] : '';

    if (empty($text) || empty($date) || empty($time)) {
        throw new Exception("Required parameters cannot be empty");
    }

    // Format the task text with label if provided
    if (!empty($label)) {
        $text = "[{$label}] {$text}";
    }

    // Generate a unique ID for the task
    $taskId = uniqid();
    
    // Create the task entry
    $taskEntry = "{$taskId}|{$text}|{$date}|{$time}|{$images}";
    
    $filePath = __DIR__ . '/tasks.txt';
    
    // Create a backup before adding a new task
    $backupManager = new BackupManager($filePath);
    $backupManager->createBackup('before_add_task');

    // Use file locking to prevent race conditions
    $fp = fopen($filePath, 'a+');
    if (!$fp) {
        throw new Exception("Failed to open file for writing");
    }

    if (!flock($fp, LOCK_EX)) {
        fclose($fp);
        throw new Exception("Failed to acquire exclusive lock on file");
    }

    // Write the task to the file
    $writeResult = fwrite($fp, $taskEntry . PHP_EOL);
    
    // Release the lock and close the file
    flock($fp, LOCK_UN);
    fclose($fp);
    
    if ($writeResult === false) {
        throw new Exception("Failed to write to file");
    }

    echo json_encode(['status' => 'success', 'taskId' => $taskId]);
} catch (Exception $e) {
    error_log("Error adding task: " . $e->getMessage());
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
}
?> 