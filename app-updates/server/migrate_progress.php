<?php
// Migration script to add progress field to existing tasks
error_reporting(E_ALL);
ini_set('display_errors', 1);

$filePath = __DIR__ . '/tasks.txt';
$backupPath = __DIR__ . '/tasks_backup_' . date('Y-m-d_H-i-s') . '.txt';

if (!file_exists($filePath)) {
    echo "Tasks file not found!\n";
    exit(1);
}

// Create backup
copy($filePath, $backupPath);
echo "Backup created: " . basename($backupPath) . "\n";

// Read existing tasks
$tasks = file($filePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
$updatedTasks = [];

foreach ($tasks as $task) {
    $parts = explode('|', $task);
    
    // If task already has 8 parts (including progress), skip it
    if (count($parts) >= 8) {
        $updatedTasks[] = $task;
        continue;
    }
    
    // Add missing fields to reach 8 parts
    while (count($parts) < 7) {
        $parts[] = '';
    }
    
    // Add progress field (default to 0)
    $parts[7] = '0';
    
    $updatedTasks[] = implode('|', $parts);
}

// Write updated tasks back to file
if (file_put_contents($filePath, implode("\n", $updatedTasks) . "\n")) {
    echo "Successfully updated " . count($updatedTasks) . " tasks with progress field.\n";
} else {
    echo "Failed to update tasks file!\n";
    exit(1);
}

echo "Migration completed successfully!\n";
?> 