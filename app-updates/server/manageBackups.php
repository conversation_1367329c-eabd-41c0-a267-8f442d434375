<?php
header('Content-Type: application/json');
require_once 'backup_manager.php';

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');

try {
    $tasksFile = __DIR__ . '/tasks.txt';
    $backupManager = new BackupManager($tasksFile);
    
    $action = isset($_POST['action']) ? $_POST['action'] : '';
    
    switch ($action) {
        case 'create':
            $operation = isset($_POST['operation']) ? $_POST['operation'] : '';
            $success = $backupManager->createBackup($operation);
            echo json_encode([
                'status' => $success ? 'success' : 'error',
                'message' => $success ? 'Backup created successfully' : 'Failed to create backup'
            ]);
            break;
            
        case 'list':
            $backups = $backupManager->listBackups();
            echo json_encode([
                'status' => 'success',
                'backups' => $backups
            ]);
            break;
            
        case 'restore':
            if (!isset($_POST['backupFile'])) {
                throw new Exception('No backup file specified');
            }
            $backupFile = __DIR__ . '/backup/' . basename($_POST['backupFile']);
            $success = $backupManager->restoreFromBackup($backupFile);
            echo json_encode([
                'status' => $success ? 'success' : 'error',
                'message' => $success ? 'Backup restored successfully' : 'Failed to restore backup'
            ]);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
} catch (Exception $e) {
    error_log("Backup management error: " . $e->getMessage());
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
?> 