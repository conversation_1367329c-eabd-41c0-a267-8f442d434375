<?php
header('Content-Type: application/json');
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');

require_once 'backup_manager.php';

$filePath = __DIR__ . '/tasks.txt';
$taskIdToRemove = isset($_POST['taskId']) ? $_POST['taskId'] : '';

if (empty($taskIdToRemove)) {
    echo json_encode(['status' => 'error', 'message' => 'No task ID provided']);
    exit;
}

try {
    // Create a backup before removing the task
    $backupManager = new BackupManager($filePath);
    $backupManager->createBackup('before_remove_task_' . $taskIdToRemove);

    if(file_exists($filePath)) {
        $tasks = file($filePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $updatedTasks = [];
        $found = false;

        foreach ($tasks as $task) {
            $parts = explode("|", $task);
            if (count($parts) < 4) {
                error_log("Skipping malformed task: {$task}");
                continue;
            }

            $taskId = $parts[0];
            
            if ($taskId !== $taskIdToRemove) {
                // Keep tasks that don't match the ID to remove
                // Ensure all tasks have the platform field
                if (count($parts) < 5) {
                    $dateAdded = $parts[1];
                    $taskText = $parts[2];
                    $completed = $parts[3];
                    $platform = 'Web';
                    $task = "{$taskId}|{$dateAdded}|{$taskText}|{$completed}|{$platform}";
                }
                $updatedTasks[] = $task;
            } else {
                $found = true;
            }
        }

        if (file_put_contents($filePath, implode(PHP_EOL, $updatedTasks) . PHP_EOL) !== false) {
            echo json_encode(['status' => 'success']);
        } else {
            throw new Exception("Failed to save changes when removing task ID: $taskIdToRemove");
        }
    } else {
        throw new Exception("File not found: $filePath");
    }
} catch (Exception $e) {
    error_log($e->getMessage());
    echo json_encode(['status' => 'error', 'message' => $e->getMessage()]);
}
?>

