<?php
// File to store the site title
$titleFile = __DIR__ . '/../data/site_title.txt';

// Default title
$defaultTitle = 'CoolFM - Updates';

// Check if the title file exists
if (file_exists($titleFile)) {
    $title = file_get_contents($titleFile);
    // If the file is empty, use the default title
    if (empty($title)) {
        $title = $defaultTitle;
    }
} else {
    $title = $defaultTitle;
}

// Return the title as JSON
echo json_encode(['status' => 'success', 'title' => $title]); 