<?php
header('Content-Type: application/json');
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Set up error logging
$logFile = __DIR__ . '/error.log';
ini_set('error_log', $logFile);

try {
    if (!isset($_POST['taskId'], $_POST['taskText'])) {
        throw new Exception("Missing required parameters");
    }

    $taskIdToEdit = trim($_POST['taskId']);
    $newTaskText = trim($_POST['taskText']);
    $taskLabel = isset($_POST['taskLabel']) ? trim($_POST['taskLabel']) : '';
    $platform = isset($_POST['platform']) ? trim($_POST['platform']) : 'Web';
    
    // Validate platform - handle multiple platforms separated by commas
    $validPlatforms = ['Web', 'Android', 'iOS'];
    $platformArray = array_map('trim', explode(',', $platform));
    $validatedPlatforms = [];
    
    foreach ($platformArray as $singlePlatform) {
        if (in_array($singlePlatform, $validPlatforms)) {
            $validatedPlatforms[] = $singlePlatform;
        }
    }
    
    // If no valid platforms found, default to Web
    if (empty($validatedPlatforms)) {
        $platform = 'Web';
    } else {
        $platform = implode(', ', $validatedPlatforms);
    }
    
    if (empty($newTaskText)) {
        throw new Exception("Task text cannot be empty");
    }

    $filePath = __DIR__ . '/tasks.txt';
    
    if (!file_exists($filePath)) {
        throw new Exception("File not found: {$filePath}");
    }

    if (!is_readable($filePath) || !is_writable($filePath)) {
        throw new Exception("File not accessible: {$filePath}");
    }

    // Use file locking to prevent race conditions
    $fp = fopen($filePath, 'c+');
    if (!$fp) {
        throw new Exception("Failed to open file for reading/writing");
    }

    if (!flock($fp, LOCK_EX)) {
        fclose($fp);
        throw new Exception("Failed to acquire exclusive lock on file");
    }

    // Read the file contents
    $contents = '';
    while (!feof($fp)) {
        $contents .= fread($fp, 8192);
    }

    $tasks = explode(PHP_EOL, $contents);
    $tasks = array_filter($tasks, 'trim'); // Remove empty lines

    if (empty($tasks)) {
        flock($fp, LOCK_UN);
        fclose($fp);
        throw new Exception("Failed to read tasks file or file is empty");
    }

    // Create a backup of the tasks file
    $backupPath = $filePath . '.bak';
    file_put_contents($backupPath, $contents);

    $updatedTasks = [];
    $found = false;

    foreach ($tasks as $task) {
        $parts = explode("|", $task);
        if (count($parts) < 4) {
            error_log("Skipping malformed task: {$task}");
            continue;
        }

        $taskId = $parts[0];
        $dateAdded = $parts[1];
        $taskText = $parts[2];
        $completed = $parts[3];
        $currentPlatform = isset($parts[4]) ? $parts[4] : 'Web';
        
        if ($taskId === $taskIdToEdit) {
            // Format the task text with label if provided
            $formattedTaskText = $newTaskText;
            if (!empty($taskLabel)) {
                $formattedTaskText = $taskLabel . " - " . $newTaskText;
            }
            
            // Update the task with the new text, keeping the completion status
            $updatedTask = "{$taskId}|{$dateAdded}|{$formattedTaskText}|{$completed}|{$platform}";
            $updatedTasks[] = $updatedTask;
            $found = true;
        } else {
            // Ensure all tasks have the platform field
            if (count($parts) < 5) {
                $task = "{$taskId}|{$dateAdded}|{$taskText}|{$completed}|{$currentPlatform}";
            }
            $updatedTasks[] = $task;
        }
    }

    if (!$found) {
        flock($fp, LOCK_UN);
        fclose($fp);
        throw new Exception("Task not found: {$taskIdToEdit}");
    }

    // Write the updated content back to the file
    ftruncate($fp, 0); // Clear the file
    rewind($fp); // Go to the beginning
    $writeResult = fwrite($fp, implode(PHP_EOL, $updatedTasks) . PHP_EOL);
    
    // Release the lock and close the file
    flock($fp, LOCK_UN);
    fclose($fp);
    
    if ($writeResult === false) {
        throw new Exception("Failed to write to file");
    }

    echo json_encode(['status' => 'success']);

} catch (Exception $e) {
    error_log("Error in editTask.php: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
?>
