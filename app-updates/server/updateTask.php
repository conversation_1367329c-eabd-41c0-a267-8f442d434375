<?php
header('Content-Type: application/json');
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Set up error logging
$logFile = __DIR__ . '/error.log';
ini_set('error_log', $logFile);

try {
    if (!isset($_POST['taskId'], $_POST['completed'])) {
        throw new Exception("Missing required parameters");
    }

    error_log("\n=== Start Task Update ===");
    error_log("Request data: " . print_r($_POST, true));
    
    $taskIdToUpdate = trim($_POST['taskId']);
    
    // Ensure completed is treated as a string '1' or '0'
    $completed = in_array(trim($_POST['completed']), ['1', 'true', 'yes', 'on', true], true) ? '1' : '0';
    
    // Get task text and label
    $taskText = isset($_POST['taskText']) ? trim($_POST['taskText']) : '';
    $taskUser = isset($_POST['taskUser']) ? trim($_POST['taskUser']) : '';
    $taskType = isset($_POST['taskType']) ? trim($_POST['taskType']) : 'Atualização';
    $todoList = isset($_POST['todoList']) ? $_POST['todoList'] : '';
    // Ensure todoList is properly formatted - if empty, set to empty string
    if (trim($todoList) === '' || $todoList === 'null' || $todoList === '[]') {
        $todoList = '';
    }
    $taskLabel = isset($_POST['taskLabel']) ? trim($_POST['taskLabel']) : '';
    
    // Get progress (default to 0 if not provided)
    $progress = isset($_POST['progress']) ? intval($_POST['progress']) : 0;
    // Ensure progress is between 0 and 100
    $progress = max(0, min(100, $progress));

    // Validate task type
    if (!in_array($taskType, ['Atualização', 'Bug'])) {
        $taskType = 'Atualização'; // Default to Atualização if invalid
    }
    
    // Format task text with label if provided
    $formattedTaskText = $taskText;
    if (!empty($taskLabel) && $completed === '0') {
        $formattedTaskText = $taskLabel . ' - ' . $taskText;
    }
    
    // Get platform
    $platform = isset($_POST['platform']) ? trim($_POST['platform']) : 'Web';
    
    // Validate platform - handle multiple platforms separated by commas
    $validPlatforms = ['Web', 'Android', 'iOS'];
    $platformArray = array_map('trim', explode(',', $platform));
    $validatedPlatforms = [];
    
    foreach ($platformArray as $singlePlatform) {
        if (in_array($singlePlatform, $validPlatforms)) {
            $validatedPlatforms[] = $singlePlatform;
        }
    }
    
    // If no valid platforms found, default to Web
    if (empty($validatedPlatforms)) {
        $platform = 'Web';
    } else {
        $platform = implode(', ', $validatedPlatforms);
    }
    
    // Set completion date if task is being marked as completed
    $completionDate = ($completed === '1') ? date('d-m-Y') : '';
    
    error_log("Processing task ID: {$taskIdToUpdate}");
    error_log("Setting completed to: {$completed}");
    error_log("Completion date: {$completionDate}");
    
    // Process uploaded images
    $uploadedImages = [];
    
    if (isset($_FILES['images']) && !empty($_FILES['images']['name'][0])) {
        // Create uploads directory if it doesn't exist
        $uploadsDir = __DIR__ . '/../uploads';
        if (!file_exists($uploadsDir)) {
            if (!mkdir($uploadsDir, 0755, true)) {
                throw new Exception("Failed to create uploads directory");
            }
        }
        
        // Create task-specific directory
        $taskDir = $uploadsDir . '/' . $taskIdToUpdate;
        if (!file_exists($taskDir)) {
            if (!mkdir($taskDir, 0755, true)) {
                throw new Exception("Failed to create task directory");
            }
        }
        
        // Process each uploaded file
        $fileCount = count($_FILES['images']['name']);
        
        for ($i = 0; $i < $fileCount; $i++) {
            if ($_FILES['images']['error'][$i] === UPLOAD_ERR_OK) {
                $tmpName = $_FILES['images']['tmp_name'][$i];
                $fileName = $_FILES['images']['name'][$i];
                $fileType = $_FILES['images']['type'][$i];
                
                // Validate file type
                if (!preg_match('/^image\//', $fileType)) {
                    continue; // Skip non-image files
                }
                
                // Generate a unique filename
                $extension = pathinfo($fileName, PATHINFO_EXTENSION);
                $newFileName = uniqid() . '.' . $extension;
                $targetPath = $taskDir . '/' . $newFileName;
                
                // Move the uploaded file
                if (move_uploaded_file($tmpName, $targetPath)) {
                    $relativePath = 'uploads/' . $taskIdToUpdate . '/' . $newFileName;
                    $uploadedImages[] = [
                        'name' => $fileName,
                        'path' => $relativePath
                    ];
                }
            }
        }
    }
    
    $filePath = __DIR__ . '/tasks.txt';
    
    if (!file_exists($filePath)) {
        throw new Exception("File not found: {$filePath}");
    }

    if (!is_writable($filePath)) {
        throw new Exception("File not writable: {$filePath}");
    }

    // Use file locking to prevent race conditions
    $fp = fopen($filePath, 'c+');
    if (!$fp) {
        throw new Exception("Failed to open file for reading/writing");
    }

    if (!flock($fp, LOCK_EX)) {
        fclose($fp);
        throw new Exception("Failed to acquire exclusive lock on file");
    }

    // Read the file contents
    $contents = '';
    while (!feof($fp)) {
        $contents .= fread($fp, 8192);
    }

    $tasks = explode(PHP_EOL, $contents);
    $tasks = array_filter($tasks, 'trim'); // Remove empty lines

    if (empty($tasks)) {
        flock($fp, LOCK_UN);
        fclose($fp);
        throw new Exception("Failed to read tasks file or file is empty");
    }

    error_log("Current tasks: " . print_r($tasks, true));

    $updatedTasks = [];
    $found = false;

    foreach ($tasks as $task) {
        $parts = explode("|", $task);
        if (count($parts) < 4) {
            error_log("Skipping malformed task: {$task}");
            continue;
        }

        $taskId = $parts[0];
        $dateAdded = $parts[1];
        $taskText = $parts[2];
        $currentCompleted = $parts[3];
        $currentPlatform = isset($parts[4]) ? $parts[4] : 'Web';
        $currentImages = isset($parts[5]) ? $parts[5] : '';
        $currentUser = isset($parts[6]) ? $parts[6] : '';
        $currentProgress = isset($parts[7]) ? intval($parts[7]) : 0;
        $currentTaskType = isset($parts[8]) ? $parts[8] : 'Atualização';
        $currentTodoList = isset($parts[9]) ? $parts[9] : '';
        // Ensure currentTodoList is properly formatted
        if (trim($currentTodoList) === '' || $currentTodoList === 'null') {
            $currentTodoList = '';
        }
        
        // Use the provided user name or keep the existing one
        $userToSave = !empty($taskUser) ? $taskUser : $currentUser;

        // Use the provided progress or keep the existing one
        $progressToSave = isset($_POST['progress']) ? $progress : $currentProgress;

        // Use the provided task type or keep the existing one
        $taskTypeToSave = !empty($taskType) ? $taskType : $currentTaskType;

        // Use the provided todo list or keep the existing one
        $todoListToSave = isset($_POST['todoList']) ? $todoList : $currentTodoList;
        
        // Ensure currentCompleted is treated as a string
        $currentCompleted = trim($currentCompleted);
        
        if ($taskId === $taskIdToUpdate) {
            error_log("\n=== Processing Task Start ===");
            error_log("Current task state: " . print_r([
                'id' => $taskId,
                'dateAdded' => $dateAdded,
                'text' => $taskText,
                'completed' => $currentCompleted,
                'platform' => $currentPlatform,
                'requestedComplete' => $completed,
                'completionDate' => $completionDate,
                'newPlatform' => $platform,
                'user' => $userToSave
            ], true));
            
            // Add completion date to task text if completed
            if ($completed === '1') {
                $formattedTaskText .= " (Concluído em: {$completionDate})";
            }
            
            // Merge existing images with new uploads
            $existingImages = [];
            if (!empty($currentImages)) {
                $existingImages = json_decode($currentImages, true) ?: [];
            }
            
            $allImages = array_merge($existingImages, $uploadedImages);
            $imagesJson = !empty($allImages) ? json_encode($allImages) : '';
            
            $updatedTask = "{$taskId}|{$dateAdded}|{$formattedTaskText}|{$completed}|{$platform}|{$imagesJson}|{$userToSave}|{$progressToSave}|{$taskTypeToSave}|{$todoListToSave}";
            error_log("Final task state: {$updatedTask}");
            error_log("=== Processing Task End ===\n");
            $updatedTasks[] = $updatedTask;
            $found = true;
        } else {
            // Ensure all tasks have all fields including taskType and todoList
            if (count($parts) < 10) {
                // Reconstruct task with all fields
                $task = "{$taskId}|{$dateAdded}|{$taskText}|{$currentCompleted}|{$currentPlatform}|{$currentImages}|{$currentUser}|{$currentProgress}|{$currentTaskType}|{$currentTodoList}";
            }
            $updatedTasks[] = $task;
        }
    }

    if (!$found) {
        flock($fp, LOCK_UN);
        fclose($fp);
        throw new Exception("Task not found: {$taskIdToUpdate}");
    }

    // Write the updated content back to the file
    error_log("Writing updated tasks to file...");
    ftruncate($fp, 0); // Clear the file
    rewind($fp); // Go to the beginning
    $writeResult = fwrite($fp, implode(PHP_EOL, $updatedTasks) . PHP_EOL);
    
    // Release the lock and close the file
    flock($fp, LOCK_UN);
    fclose($fp);
    
    if ($writeResult === false) {
        throw new Exception("Failed to write to file");
    }

    error_log("=== Task Update Completed Successfully ===\n");
    echo json_encode([
        'status' => 'success',
        'images' => $uploadedImages
    ]);

} catch (Exception $e) {
    error_log("Error in updateTask.php: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    http_response_code(200); // Change to 200 to ensure response is processed
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
?>
