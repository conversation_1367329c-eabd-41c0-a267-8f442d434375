<?php
// Prevent direct access
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header('HTTP/1.1 403 Forbidden');
    exit('Access forbidden');
}

// Get the title from the POST data
$title = isset($_POST['title']) ? trim($_POST['title']) : '';

// Validate the title
if (empty($title)) {
    echo json_encode(['status' => 'error', 'message' => 'Title cannot be empty']);
    exit;
}

// Save the title to a file
$titleFile = __DIR__ . '/../data/site_title.txt';

// Make sure the data directory exists
if (!file_exists(__DIR__ . '/../data')) {
    mkdir(__DIR__ . '/../data', 0755, true);
}

// Save the title to the file
if (file_put_contents($titleFile, $title)) {
    echo json_encode(['status' => 'success', 'message' => 'Title saved successfully']);
} else {
    echo json_encode(['status' => 'error', 'message' => 'Failed to save title']);
} 