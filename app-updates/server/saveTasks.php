<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', __DIR__ . '/error.log');

header('Content-Type: application/json');

try {
    // Get the task text from the POST request
    $taskText = isset($_POST['taskText']) ? trim($_POST['taskText']) : '';
    $taskUser = isset($_POST['taskUser']) ? trim($_POST['taskUser']) : '';
    $taskType = isset($_POST['taskType']) ? trim($_POST['taskType']) : 'Atualização';
    $todoList = isset($_POST['todoList']) ? $_POST['todoList'] : '';
    // Ensure todoList is properly formatted - if empty, set to empty string
    if (trim($todoList) === '' || $todoList === 'null' || $todoList === '[]') {
        $todoList = '';
    }
    $platform = isset($_POST['platform']) ? trim($_POST['platform']) : 'Web';
    
    // Validate platform - handle multiple platforms separated by commas
    $validPlatforms = ['Web', 'Android', 'iOS'];
    $platformArray = array_map('trim', explode(',', $platform));
    $validatedPlatforms = [];
    
    foreach ($platformArray as $singlePlatform) {
        if (in_array($singlePlatform, $validPlatforms)) {
            $validatedPlatforms[] = $singlePlatform;
        }
    }
    
    // If no valid platforms found, default to Web
    if (empty($validatedPlatforms)) {
        $platform = 'Web';
    } else {
        $platform = implode(', ', $validatedPlatforms);
    }
    
    // Validate the task text
    if (empty($taskText)) {
        echo json_encode(['success' => false, 'message' => 'O texto da tarefa é obrigatório']);
        exit;
    }
    
    // Validate the user name
    if (empty($taskUser)) {
        echo json_encode(['success' => false, 'message' => 'O campo "Solicitado por" é obrigatório']);
        exit;
    }

    // Validate task type
    if (!in_array($taskType, ['Atualização', 'Bug'])) {
        $taskType = 'Atualização'; // Default to Atualização if invalid
    }
    
    // Generate a unique ID for the task
    $taskId = uniqid();
    
    // Get the current date and time
    $dateAdded = date('d-m-Y');
    
    // Process uploaded images
    $uploadedImages = [];
    
    if (isset($_FILES['images']) && !empty($_FILES['images']['name'][0])) {
        // Create uploads directory if it doesn't exist
        $uploadsDir = __DIR__ . '/../uploads';
        if (!file_exists($uploadsDir)) {
            if (!mkdir($uploadsDir, 0755, true)) {
                throw new Exception("Failed to create uploads directory");
            }
        }
        
        // Create task-specific directory
        $taskDir = $uploadsDir . '/' . $taskId;
        if (!file_exists($taskDir)) {
            if (!mkdir($taskDir, 0755, true)) {
                throw new Exception("Failed to create task directory");
            }
        }
        
        // Process each uploaded file
        $fileCount = count($_FILES['images']['name']);
        
        for ($i = 0; $i < $fileCount; $i++) {
            if ($_FILES['images']['error'][$i] === UPLOAD_ERR_OK) {
                $tmpName = $_FILES['images']['tmp_name'][$i];
                $fileName = $_FILES['images']['name'][$i];
                $fileType = $_FILES['images']['type'][$i];
                
                // Validate file type
                if (!preg_match('/^image\//', $fileType)) {
                    continue; // Skip non-image files
                }
                
                // Generate a unique filename
                $extension = pathinfo($fileName, PATHINFO_EXTENSION);
                $newFileName = uniqid() . '.' . $extension;
                $targetPath = $taskDir . '/' . $newFileName;
                
                // Move the uploaded file
                if (move_uploaded_file($tmpName, $targetPath)) {
                    $relativePath = 'uploads/' . $taskId . '/' . $newFileName;
                    $uploadedImages[] = [
                        'name' => $fileName,
                        'path' => $relativePath
                    ];
                }
            }
        }
    }
    
    // Add images JSON to task data if there are any
    $imagesJson = !empty($uploadedImages) ? json_encode($uploadedImages) : '';
    
    // Default progress to 0 for new tasks
    $progress = 0;
    
    // Format the task data - adding taskType and todoList to the data structure
    $taskData = "{$taskId}|{$dateAdded}|{$taskText}|0|{$platform}|{$imagesJson}|{$taskUser}|{$progress}|{$taskType}|{$todoList}" . PHP_EOL;
    
    $filePath = __DIR__ . '/tasks.txt';
    
    // Use file locking to prevent race conditions
    $fp = fopen($filePath, 'a+');
    if (!$fp) {
        throw new Exception("Failed to open file for writing");
    }
    
    if (!flock($fp, LOCK_EX)) {
        fclose($fp);
        throw new Exception("Failed to acquire exclusive lock on file");
    }
    
    if (fwrite($fp, $taskData) === false) {
        flock($fp, LOCK_UN);
        fclose($fp);
        error_log("Failed to save task: {$taskData}");
        throw new Exception("Failed to save task");
    }
    
    flock($fp, LOCK_UN);
    fclose($fp);
    
    echo json_encode([
        'success' => true, 
        'status' => 'success',
        'images' => $uploadedImages
    ]);
    
} catch (Exception $e) {
    error_log("Error in saveTasks.php: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
?>


