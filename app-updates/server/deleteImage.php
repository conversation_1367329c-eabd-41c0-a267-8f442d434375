<?php
header('Content-Type: application/json');
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Set up error logging
$logFile = __DIR__ . '/error.log';
ini_set('error_log', $logFile);

try {
    // Check if required parameters are provided
    if (!isset($_POST['taskId']) || !isset($_POST['imagePath'])) {
        throw new Exception("Task ID and image path are required");
    }
    
    $taskId = trim($_POST['taskId']);
    $imagePath = trim($_POST['imagePath']);
    
    // Validate the image path to prevent directory traversal
    if (strpos($imagePath, '../') !== false || strpos($imagePath, '..\\') !== false) {
        throw new Exception("Invalid image path");
    }
    
    // Get the full path to the image
    $fullImagePath = __DIR__ . '/../' . $imagePath;
    
    // Check if the file exists
    if (!file_exists($fullImagePath)) {
        throw new Exception("Image file not found");
    }
    
    // Delete the file
    if (!unlink($fullImagePath)) {
        throw new Exception("Failed to delete image file");
    }
    
    // Update task data to remove the image reference
    $filePath = __DIR__ . '/tasks.txt';
    
    if (!file_exists($filePath)) {
        throw new Exception("Tasks file not found");
    }
    
    if (!is_readable($filePath) || !is_writable($filePath)) {
        throw new Exception("Tasks file not accessible");
    }
    
    // Use file locking to prevent race conditions
    $fp = fopen($filePath, 'c+');
    if (!$fp) {
        throw new Exception("Failed to open tasks file");
    }
    
    if (!flock($fp, LOCK_EX)) {
        fclose($fp);
        throw new Exception("Failed to acquire exclusive lock on file");
    }
    
    // Read the file contents
    $contents = '';
    while (!feof($fp)) {
        $contents .= fread($fp, 8192);
    }
    
    $tasks = explode(PHP_EOL, $contents);
    $tasks = array_filter($tasks, 'trim'); // Remove empty lines
    
    $updatedTasks = [];
    $found = false;
    
    foreach ($tasks as $task) {
        $parts = explode("|", $task);
        if (count($parts) < 4) {
            continue;
        }
        
        $currentTaskId = $parts[0];
        
        if ($currentTaskId === $taskId && isset($parts[5])) {
            // Get existing images
            $images = json_decode($parts[5], true) ?: [];
            
            // Remove the specified image
            $updatedImages = array_filter($images, function($img) use ($imagePath) {
                return $img['path'] !== $imagePath;
            });
            
            // Update the task with the remaining images
            $parts[5] = !empty($updatedImages) ? json_encode(array_values($updatedImages)) : '';
            $updatedTask = implode("|", $parts);
            $updatedTasks[] = $updatedTask;
            $found = true;
        } else {
            $updatedTasks[] = $task;
        }
    }
    
    if (!$found) {
        flock($fp, LOCK_UN);
        fclose($fp);
        throw new Exception("Task not found or has no images: $taskId");
    }
    
    // Write the updated content back to the file
    ftruncate($fp, 0); // Clear the file
    rewind($fp); // Go to the beginning
    $writeResult = fwrite($fp, implode(PHP_EOL, $updatedTasks) . PHP_EOL);
    
    // Release the lock and close the file
    flock($fp, LOCK_UN);
    fclose($fp);
    
    if ($writeResult === false) {
        throw new Exception("Failed to update task with image information");
    }
    
    // Return success response
    echo json_encode([
        'status' => 'success',
        'message' => 'Image deleted successfully'
    ]);
    
} catch (Exception $e) {
    error_log("Error in deleteImage.php: " . $e->getMessage());
    echo json_encode([
        'status' => 'error',
        'message' => $e->getMessage()
    ]);
}
?> 