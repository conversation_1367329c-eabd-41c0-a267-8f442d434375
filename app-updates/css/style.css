body {
    background-color: #0a0e17;
    color: #e0e0e0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.container-fluid {
    flex: 1 0 auto;
}

.card {
    background: #1f2937;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.title {
    color: #777;
}

.list-group-item {
    background: #2d3748;
    border: 1px solid rgba(255,255,255,0.1);
    margin-bottom: 3rem; /* Increased from 2rem for even better separation */
    color: white;
    cursor: default;  /* Remove pointer cursor */
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem 1.75rem; /* Increased vertical padding significantly */
    border-radius: 12px; /* More rounded corners for card-like feel */
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.25); /* Stronger shadow for better definition */
    transition: all 0.2s ease;
    position: relative;
}

/* Add a more prominent separator between tasks */
.list-group-item:not(:first-child)::before {
    content: '';
    position: absolute;
    top: -1.5rem;
    left: 2rem;
    right: 2rem;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.08), transparent);
    border-radius: 1px;
}

/* Add subtle inner border for extra definition */
.list-group-item::after {
    content: '';
    position: absolute;
    top: 1px;
    left: 1px;
    right: 1px;
    bottom: 1px;
    border: 1px solid rgba(255,255,255,0.03);
    border-radius: 11px;
    pointer-events: none;
}

.list-group-item:hover {
    transform: translateY(-3px); /* More pronounced lift */
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3); /* Stronger shadow on hover */
    border-color: rgba(255,255,255,0.2);
    background: #32404f; /* Slightly lighter background on hover */
}

.list-group-item.completed-task,
.list-group-item:not([data-label="Em Desenvolvimento"]) {
    cursor: default;
}

.form-control {
    background: #1a2332;
    border: 1px solid rgba(255,255,255,0.1);
    color: white;
}

.form-control:focus {
    background: #1a2332;
    color: white;
}

/* Task list container styling for better spacing */
#taskList {
    padding: 0; /* Remove extra padding */
    margin-top: 0; /* No extra margin from header */
}

/* Remove default list styling and add better spacing */
.list-group {
    margin-bottom: 0;
    padding-left: 0;
}

/* First task gets minimal spacing - just enough separation */
.list-group-item:first-child {
    margin-top: 1rem; /* Compact but readable spacing */
}

/* Last task gets extra bottom margin */
.list-group-item:last-child {
    margin-bottom: 2rem;
}

.badge {
    font-size: 0.75em;
    padding: 0.35em 0.75em;
    font-weight: normal;
    letter-spacing: 0.01em;
    border-radius: 3px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    min-width: 70px;
    justify-content: center;
}

.badge-success { 
    background: rgba(5, 150, 105, 0.8); 
    color: rgba(255, 255, 255, 0.9);
}
.badge-info { 
    background: rgba(2, 132, 199, 0.8);
    color: rgba(255, 255, 255, 0.9);
}
.badge-secondary { 
    background: rgba(111, 119, 130, 0.8);
    color: rgba(255, 255, 255, 0.9);
}
.badge-warning { 
    background: rgba(251, 191, 36, 0.8);
    color: rgba(0, 0, 0, 0.9);
}
.badge-danger { 
    background: rgba(220, 38, 38, 0.8);
    color: rgba(255, 255, 255, 0.9);
}

/* Update default badge style for "Em Fila" */
.badge-default {
    background: rgba(234, 179, 8, 0.15); /* Subtle yellow background */
    color: rgba(234, 179, 8, 0.9); /* Yellowish text */
    border: 1px solid rgba(234, 179, 8, 0.2); /* Subtle yellow border */
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75em;
    padding: 0.35em 0.75em;
    border-radius: 3px;
    margin-right: 6px;
    min-width: 70px;
}

.badge-date {
    background: #374151;
    color: #9ca3af;
    font-weight: normal;
    font-size: 0.75em;  /* Match other badges */
    padding: 0.35em 0.75em;  /* Match other badges */
    border-radius: 3px;  /* Slightly smaller radius */
    margin-right: 6px;
    border: 1px solid rgba(255,255,255,0.1);
    opacity: 0.8;  /* Make it more subtle */
    height: 24px;  /* Fixed height to match */
    display: inline-flex;
    align-items: center;
    min-width: 70px; /* Match other badges */
}

/* Task type badges */
.badge-task-type {
    font-size: 0.75em;
    padding: 0.35em 0.75em;
    font-weight: normal;
    letter-spacing: 0.01em;
    border-radius: 3px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    min-width: 70px;
    justify-content: center;
    margin-right: 6px;
}

.badge-update {
    background: rgba(34, 197, 94, 0.8);
    color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(34, 197, 94, 0.3);
}

.badge-bug {
    background: rgba(239, 68, 68, 0.8);
    color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(239, 68, 68, 0.3);
}

/* Todo list styling */
.todo-list-container {
    background: #1a2332;
    border: 1px solid rgba(255,255,255,0.1);
    border-radius: 6px;
    padding: 1rem;
    margin-top: 0.5rem;
}

.todo-items {
    max-height: 200px;
    overflow-y: auto;
    margin-bottom: 1rem;
}

.todo-item {
    background: #2d3748;
    border: 1px solid rgba(255,255,255,0.1);
    border-radius: 4px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s ease;
}

.todo-item:hover {
    background: #374151;
    border-color: rgba(255,255,255,0.2);
}

.todo-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.todo-text {
    color: #e0e0e0;
    font-size: 0.9em;
    transition: all 0.2s ease;
}

.todo-text.completed {
    text-decoration: line-through;
    color: #9ca3af;
    opacity: 0.7;
}

.remove-todo-btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1;
    border-color: rgba(239, 68, 68, 0.3);
    color: rgba(239, 68, 68, 0.8);
}

.remove-todo-btn:hover {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.5);
    color: rgba(239, 68, 68, 1);
}

.custom-control-label {
    flex: 1;
    margin-bottom: 0;
    cursor: pointer;
}

.custom-control-input:checked ~ .custom-control-label .todo-text {
    text-decoration: line-through;
    color: #9ca3af;
    opacity: 0.7;
}



.completed-task {
    opacity: 0.7;
    background: #242b38 !important;
}

.completed-task .task-text {
    text-decoration: line-through;
    color: #9ca3af;
}

.completed-task .task-text-wrapper {
    background: rgba(0, 0, 0, 0.2);
}

.completed-task .badge,
.completed-task .badge-date,
.completed-task .badge-user {
    opacity: 0.7;
}

.completion-date {
    color: #6b7280;
    font-size: 0.85em;
    font-style: italic;
    margin-left: 8px;
}

/* Canceled task styles */
.canceled-task {
    opacity: 0.6;
    background: #2a1f1f !important; /* Darker reddish background */
    border: 1px solid rgba(220, 38, 38, 0.3) !important;
}

.canceled-task .task-text {
    color: #9ca3af;
    opacity: 0.8;
}

.canceled-task .task-text-wrapper {
    background: rgba(220, 38, 38, 0.1); /* Subtle red tint */
}

.canceled-task .badge-date,
.canceled-task .badge-user,
.canceled-task .platform-badge {
    opacity: 0.6;
}

.canceled-task:hover {
    opacity: 0.7;
    transform: none; /* Disable hover transform for canceled tasks */
    box-shadow: 0 2px 4px rgba(220, 38, 38, 0.2);
}

/* Hide progress bar for canceled tasks */
.canceled-task .task-progress-container {
    display: none;
}

/* Waiting for response task styles */
.waiting-response-task {
    background: linear-gradient(135deg, #2d3748 0%, #3a2f1a 100%) !important; /* Golden-brown gradient */
    border: 1px solid rgba(251, 191, 36, 0.4) !important;
    position: relative;
    overflow: hidden;
}

.waiting-response-task::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(251, 191, 36, 0.1), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.waiting-response-task .task-text-wrapper {
    background: rgba(251, 191, 36, 0.1); /* Subtle golden tint */
    border-left: 3px solid rgba(251, 191, 36, 0.6); /* Left border indicator */
    padding-left: 1rem;
}

.waiting-response-task .badge-warning {
    box-shadow: 0 0 10px rgba(251, 191, 36, 0.5);
    animation: pulse 1.5s ease-in-out infinite alternate;
}

@keyframes pulse {
    from { box-shadow: 0 0 10px rgba(251, 191, 36, 0.3); }
    to { box-shadow: 0 0 15px rgba(251, 191, 36, 0.7); }
}

.waiting-response-task:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 16px rgba(251, 191, 36, 0.2);
}

#completedTasksList {
    margin-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

#completedTasksList h5 {
    color: #6b7280;
    font-size: 1rem;
    margin-bottom: 1rem;
}

.task-edit-input {
    background: #1a2332;
    color: white;
    border: 1px solid rgba(255,255,255,0.2);
}

.modal-content {
    background: #1f2937;
    color: white;
}

.modal-header {
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.modal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.modal-footer-left,
.modal-footer-right {
    display: flex;
    gap: 8px;
    align-items: center;
}

.btn-sm {
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-sm:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.saveTask, .cancelEdit {
    margin: 0 2px;
    min-width: 80px;
}

.input-group-append {
    margin-left: 8px;
}

.input-group {
    align-items: flex-start;
}

.task-content {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}

.task-text {
    margin-left: 4px;
}

.task-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-width: 0;
    align-items: flex-start;  /* Add this */
    width: 100%;  /* Add this */
    padding-left: 0; /* Remove left padding that was for checkbox */
}

.task-row {
    display: flex;
    align-items: center; /* Changed from flex-start to center */
    justify-content: space-between;
    width: 100%;
    gap: 8px;
    padding: 4px 0;
}

/* Base task layout improvements */
.task-row {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    width: 100%;
    align-items: flex-start;  /* Add this */
}

.task-left {
    display: flex;
    flex: 1;
    min-width: 0;
    gap: 8px;
    align-items: flex-start;
}

.task-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
    width: 100%;  /* Add this */
    justify-content: flex-start;  /* Add this */
}

.task-right {
    display: flex;
    gap: 8px;
    align-items: flex-start;
}

.task-right {
    display: flex;
    gap: 8px;
    margin-left: auto;
    align-items: center; /* Center align buttons */
    align-self: center; /* Center in parent container */
}

.task-right .btn {
    height: 31px; /* Fixed height for buttons */
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Task text container styling */
.task-text-wrapper {
    background: rgba(0, 0, 0, 0.1);
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    width: 100%;
    pointer-events: auto;
    cursor: default;  /* Ensure default cursor on text wrapper */
}

.task-text {
    margin: 0;
    display: inline-block;
    cursor: default;  /* Ensure default cursor on text */
}

.task-text-wrapper {
    text-align: left;  /* Add this */
    width: 100%;  /* Add this */
}

/* Label buttons improvements */
.label-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 8px;
    width: 100%;
}

.label-btn {
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0.35em 0.75em;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 24px;
    font-size: 0.75em;
    border-radius: 3px;
}

/* Edit form improvements */
.task-edit-row {
    display: flex;
    flex-direction: column;
    gap: 15px;
    width: 100%;
}

/* Add task input improvements */
.add-task-group {
    display: flex;
    gap: 8px;
    width: 100%;
    min-height: 38px;
}

.add-task-group .form-control {
    min-width: 0;
    flex: 1;
}

/* Responsive styles */
@media (max-width: 768px) {
    .container-fluid {
        padding: 10px;
    }

    .list-group-item {
        padding: 15px;
        margin-bottom: 1rem;
    }

    .task-content {
        display: flex;
        flex-direction: column;
        gap: 8px;
        width: 100%;
    }

    .task-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
    }

    .task-main {
        display: flex;
        flex-direction: column;
        gap: 8px;
        width: 100%;
    }

    .task-text {
        margin: 0;
        width: 100%;
        font-size: 1rem;
        text-align: left;  /* Add this */
    }

    .ml-auto {
        margin: 10px 0 0 0;
        display: flex;
        gap: 8px;
        width: 100%;
    }

    .ml-auto .btn {
        flex: 1;
        padding: 8px;
        font-size: 0.9rem;
    }

    /* Edit form improvements */
    .input-group {
        display: flex;
        flex-direction: column;
        gap: 12px;
        width: 100%;
    }

    .task-edit-row {
        display: flex;
        flex-direction: column;
        gap: 12px;
        width: 100%;
    }

    .task-edit-input {
        width: 100%;
        padding: 8px;
    }

    .label-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        width: 100%;
    }

    .edit-buttons {
        display: flex;
        gap: 8px;
        width: 100%;
        flex-wrap: wrap;
    }

    .edit-buttons .btn {
        flex: 1;
        padding: 8px;
    }

    .input-group-append {
        display: flex;
        gap: 8px;
        margin: 0;
    }

    .input-group-append .btn {
        flex: 1;
    }

    .label-buttons {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        margin-top: 8px;
        justify-content: flex-start;
    }

    .badge {
        margin: 2px;
    }

    .label-buttons .badge {
        flex: 1;
        text-align: center;
        white-space: nowrap;
        padding: 8px;
    }

    .input-group {
        flex-direction: column;
        width: 100%;
    }

    .input-group-append {
        display: flex;
        gap: 8px;
        margin-top: 8px;
    }

    .input-group-append .btn {
        flex: 1;
    }

    .task-edit-input {
        width: 100%;
    }

    .task-row {
        flex-direction: column;
    }

    .task-right {
        width: 100%;
        padding-top: 0; /* Remove top padding on mobile */
        align-items: center;
        justify-content: flex-end;
    }

    .task-right .btn {
        flex: 1;
    }

    .add-task-group {
        flex-direction: column;
    }

    .add-task-group .form-control,
    .add-task-group .btn {
        width: 100%;
    }

    .label-buttons {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }

    .task-meta {
        justify-content: flex-start;  /* Add this */
    }

    .edit-buttons {
        justify-content: stretch;
    }

    .edit-buttons .btn {
        min-width: 0;  /* Remove min-width on mobile */
        flex: 0 1 auto;  /* Don't force equal widths */
        padding: 8px 20px;  /* Slightly larger padding for touch */
    }

    .task-right {
        padding-top: 8px;
        width: auto;
        margin-left: 0;
    }

    .task-right .btn {
        min-width: 80px;
        max-width: 100px;
    }

    .task-text-wrapper {
        margin-top: 8px;
    }
}

@media (max-width: 576px) {
    .input-group {
        flex-direction: column;
    }

    .input-group-append {
        margin-left: 0;
        margin-top: 10px;
        width: 100%;
    }

    #addTask {
        width: 100%;
    }

    .task-edit-input {
        width: 100%;
    }

    .input-group .btn {
        width: 100%;
        margin: 5px 0;
    }

    .list-group-item {
        padding: 12px;
    }

    .task-content {
        grid-template-columns: 1fr;
        grid-template-areas:
            "text"
            "date"
            "label";
    }

    .badge-date, .task-label {
        text-align: left;
    }

    .ml-auto {
        flex-direction: row;
    }

    .input-group-append {
        flex-direction: row;
    }

    .task-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .badge-date, .badge-user, .platform-badge {
        margin: 0 4px 4px 0;
    }

    .task-meta {
        flex-direction: row;
        flex-wrap: wrap;
        align-items: flex-start;
    }

    .badge-date, .badge-user {
        margin-right: 0;
    }

    .task-right {
        flex-direction: row;
        justify-content: flex-start;
    }

    .task-right .btn {
        flex: 0 1 auto;
        min-width: 70px;
    }

    .task-meta {
        flex-direction: row; /* Change from column to row */
        align-items: center;
        justify-content: flex-start;
    }

    .badge-date, 
    .task-label {
        text-align: left;
        margin-right: 8px;
    }
}

/* ...rest of existing code... */

/* Update button styles */
.task-right .btn-danger {
    background-color: #4b5563;
    border-color: #4b5563;
    transition: all 0.2s ease;
}

.task-right .btn-danger:hover {
    background-color: #dc2626;
    border-color: #dc2626;
}

/* Enhanced desktop styles - now focuses on visual clarity without drag functionality */
@media (min-width: 768px) {
    /* Visual enhancement for development tasks */
    .not-completed-task[data-label="Em Desenvolvimento"] {
        transition: background-color 0.2s ease;
    }
    
    .not-completed-task[data-label="Em Desenvolvimento"]:hover {
        background-color: rgba(255, 255, 255, 0.05);
    }
}

/* ...rest of existing code... */

/* Drag and drop styles - REMOVED: Now using automatic percentage-based sorting */

/* ...existing code... */

.custom-control {
    display: none;
}

.custom-control-label {
    display: none;
}

.custom-control-input,
.btn,
.task-text-wrapper {
    pointer-events: auto;
    cursor: pointer;
}

/* ...rest of existing code... */

/* Add editing state styles */
.list-group-item.editing {
    cursor: default !important;
    pointer-events: auto !important;
}

.editing .task-content {
    pointer-events: auto !important;
}

.editing .task-text-wrapper,
.task-edit-input {
    cursor: text !important;
}

/* ...rest of existing code... */

/* ...existing code... */

.task-edit-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.task-edit-header .custom-control {
    display: flex;
    align-items: center;
    gap: 8px;
}

.task-edit-header .custom-control-input,
.task-edit-header .custom-control-label {
    display: inline-block;
    cursor: pointer;
}

.task-edit-header .custom-control-label {
    color: #9ca3af;
    user-select: none;
}

/* Update checkbox styles to be visible */
.custom-control {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 1rem;
}

.custom-control-input {
    margin-right: 0.5rem;
}

.custom-control-label {
    color: inherit;
    user-select: none;
    cursor: pointer;
}

/* Keep pointer cursor only for interactive elements */
.list-group-item .btn,
.list-group-item .custom-control-input,
.list-group-item .label-btn {
    cursor: pointer;
}

/* ...rest of existing code... */

/* ...existing code... */

.edit-buttons {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-top: 10px;
    flex-wrap: wrap;
    gap: 8px;
}

.edit-buttons-left {
    display: flex;
    gap: 8px;
}

.edit-buttons-right {
    display: flex;
    gap: 8px;
}

/* ...existing code... */

@media (max-width: 768px) {
    /* ...existing code... */
    
    .edit-buttons {
        flex-direction: column;
    }

    .edit-buttons-left,
    .edit-buttons-right {
        justify-content: stretch;
        width: 100%;
    }
}

/* ...existing code... */

.edit-buttons {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-top: 10px;
    gap: 8px;
}

.edit-buttons-left {
    display: flex;
    gap: 8px;
    margin-right: auto; /* Push to the left */
}

.edit-buttons-right {
    display: flex;
    gap: 8px;
    margin-left: auto; /* Push to the right */
}

/* Update responsive styles */
@media (max-width: 768px) {
    .edit-buttons {
        flex-direction: column;
        align-items: stretch;
    }

    .edit-buttons-left,
    .edit-buttons-right {
        margin: 0;
        width: 100%;
    }

    .edit-buttons-left {
        margin-bottom: 8px;
    }

    .edit-buttons-right {
        justify-content: space-between;
    }

    .edit-buttons .btn {
        flex: 1;
    }
}

/* ...existing code... */

/* ...existing code... */

.task-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    gap: 8px;
}

.task-left {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex: 1;
    min-width: 0; /* Allow text container to shrink */
    width: 100%; /* Ensure full width */
}

.task-text-wrapper {
    background: rgba(0, 0, 0, 0.1);
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    width: 100%;
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.task-text {
    flex: 1;
    min-width: 0; /* Allow text to shrink */
    word-wrap: break-word; /* Allow text to wrap */
    white-space: pre-wrap; /* Preserve whitespace and wrapping */
}

.task-right {
    display: flex;
    gap: 8px;
    align-items: center;
    padding-left: 8px;
    flex-shrink: 0; /* Prevent buttons from shrinking */
}

/* Update responsive styles */
@media (max-width: 768px) {
    .task-row {
        flex-wrap: nowrap; /* Keep everything on one line */
    }

    .task-left {
        display: flex;
        flex-direction: column;
        width: 100%;
    }

    .task-text-wrapper {
        width: 100%;
        flex: 1;
    }

    .task-right {
        padding-top: 0;
        white-space: nowrap;
    }

    .task-right .btn {
        padding: 4px 8px; /* Slightly smaller padding for better fit */
        font-size: 0.875rem; /* Slightly smaller font size */
    }
}

/* ...existing code... */

/* Platform badges */
.platform-badge {
    background: #374151;
    color: #9ca3af;
    font-weight: normal;
    font-size: 0.75em;
    padding: 0.35em 0.75em;
    border-radius: 3px;
    margin-right: 6px;
    border: 1px solid rgba(255,255,255,0.1);
    opacity: 0.8;
    height: 24px;
    display: inline-flex;
    align-items: center;
    min-width: 70px;
    justify-content: center;
}

.platform-badge i {
    margin-right: 4px;
    font-size: 0.7rem;
}

.platform-badge.web {
    background-color: #007bff;
    color: white;
}

.platform-badge.android {
    background-color: #3ddc84;
    color: #000;
}

.platform-badge.ios {
    background-color: #000;
    color: white;
}

/* Platform buttons in modals */
.platform-buttons {
    margin: 15px 0;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.platform-btn {
    padding: 8px 15px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s;
    min-width: 120px;
    background-color: transparent;
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #e0e0e0;
}

.platform-btn:hover {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.platform-btn i {
    margin-right: 8px;
    font-size: 1.1rem;
}

/* Platform button active states - match the colors from platform badges */
.platform-btn.web.active {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.platform-btn.android.active {
    background-color: #3ddc84;
    border-color: #3ddc84;
    color: #000;
    box-shadow: 0 0 0 2px rgba(61, 220, 132, 0.25);
}

.platform-btn.ios.active {
    background-color: #000;
    border-color: #000;
    color: white;
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.25);
}

/* Override any conflicting badge styles */
.badge.platform-badge {
    background-color: transparent;
    border: none;
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75em;
    padding: 0.35em 0.75em;
    border-radius: 3px;
}

/* Task item styling */
.task-content {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}

.task-text-wrapper {
    width: 100%;
    margin-bottom: 5px;
}

.task-actions {
    display: flex;
    gap: 5px;
}

.completed-task .task-text {
    text-decoration: line-through;
    color: #6c757d;
}

/* Alert container */
.alert-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 300px;
}

/* Improved task layout */
.task-row {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    width: 100%;
    gap: 12px;
}

.task-left {
    display: flex;
    flex-direction: column;
    gap: 8px;
    flex: 1;
    min-width: 0;
}

.task-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    width: 100%;
}

.task-meta-left {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
    flex: 1;
}

.task-meta-right {
    display: flex;
    align-items: center;
    margin-left: 12px;
}

.task-right {
    display: flex;
    gap: 8px;
    align-items: center;
    align-self: flex-end;
    margin-bottom: 8px;
}

/* Badge date - remove time */
.badge-date {
    background: #374151;
    color: #9ca3af;
    font-weight: normal;
    font-size: 0.75em;
    padding: 0.35em 0.75em;
    border-radius: 3px;
    margin-right: 6px;
    border: 1px solid rgba(255,255,255,0.1);
    opacity: 0.8;
    height: 24px;
    display: inline-flex;
    align-items: center;
    min-width: 70px;
    justify-content: center;
}

/* Task text container styling */
.task-text-wrapper {
    background: rgba(0, 0, 0, 0.1);
    padding: 12px 14px;
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.05);
    width: 100%;
    pointer-events: auto;
    cursor: default;
    margin-bottom: 8px;
    margin-top: 4px;
}

.task-text {
    margin: 0;
    display: inline-block;
    cursor: default;
    word-break: break-word;
}

/* Edit form improvements */
.task-edit-row {
    display: flex;
    flex-direction: column;
    gap: 12px;
    width: 100%;
}

.edit-input-group {
    margin-bottom: 4px;
}

.edit-buttons {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-top: 8px;
    gap: 8px;
}

.edit-buttons-left {
    display: flex;
    gap: 8px;
    margin-right: auto;
}

.edit-buttons-right {
    display: flex;
    gap: 8px;
    margin-left: auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .task-row {
        flex-direction: column;
    }

    .task-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .task-meta-left {
        width: 100%;
    }

    .task-meta-right {
        width: 100%;
        margin-left: 0;
        justify-content: flex-start;
    }

    .task-right {
        align-self: flex-start;
        margin-top: 4px;
        width: 100%;
        justify-content: flex-end;
    }
}

/* Enhanced modal styles */
.modal-lg {
    max-width: 800px;
}

.modal-body .form-group {
    margin-bottom: 1.5rem;
}

.modal-body label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Remove duplicate platform button styles */
/* Label buttons in modal */
.label-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
}

.label-btn {
    cursor: pointer;
    padding: 8px 12px;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    text-decoration: none !important;
}

.label-btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

.label-btn.active {
    box-shadow: 0 0 0 2px rgba(0,0,0,0.2);
    transform: translateY(-1px);
}

/* Task edit button */
.task-edit-btn {
    padding: 6px 12px;
    border-radius: 4px;
    transition: all 0.2s ease;
    background-color: transparent;
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #6c757d;
}

.task-edit-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: #495057;
    transform: translateY(-1px);
}

.task-edit-btn i {
    font-size: 1rem;
}

/* Modal footer with button groups */
.modal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.modal-footer-left,
.modal-footer-right {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* Responsive adjustments for modal footer */
@media (max-width: 576px) {
    .modal-footer {
        flex-direction: column;
        align-items: stretch;
    }

    .modal-footer-left,
    .modal-footer-right {
        flex-direction: column;
        width: 100%;
    }

    .modal-footer .btn {
        width: 100%;
        margin: 4px 0;
    }
}

/* Task image styling */
.task-images-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.task-image-wrapper {
    position: relative;
    width: 150px;
    height: 150px;
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background-color: rgba(0, 0, 0, 0.2);
}

.task-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.2s ease;
}

.task-image:hover {
    opacity: 0.8;
}

.task-image-actions {
    position: absolute;
    top: 5px;
    right: 5px;
    display: flex;
    gap: 5px;
}

.task-image-action {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.task-image-action:hover {
    background-color: rgba(220, 53, 69, 0.8);
    transform: scale(1.1);
}

.image-preview-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.image-preview-item {
    position: relative;
    width: 150px;
    height: 150px;
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background-color: rgba(0, 0, 0, 0.2);
}

.image-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-preview-remove {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.image-preview-remove:hover {
    background-color: rgba(220, 53, 69, 0.8);
    transform: scale(1.1);
}

/* Task images in the task list */
.task-attached-images {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
}

.task-thumbnail {
    width: 80px;
    height: 80px;
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.1);
    cursor: pointer;
    transition: all 0.2s ease;
}

.task-thumbnail:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.task-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Image lightbox */
.image-lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.image-lightbox.active {
    opacity: 1;
    visibility: visible;
}

.lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.lightbox-image {
    max-width: 100%;
    max-height: 90vh;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

.lightbox-close {
    position: absolute;
    top: -40px;
    right: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.lightbox-close:hover {
    background-color: rgba(220, 53, 69, 0.8);
}

.lightbox-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.lightbox-nav:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

.lightbox-prev {
    left: -70px;
}

.lightbox-next {
    right: -70px;
}

/* Custom file input styling */
.custom-file-label {
    background-color: #1a2332;
    color: #e0e0e0;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.custom-file-label::after {
    background-color: #2d3748;
    color: #e0e0e0;
    border-left: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive adjustments for images */
@media (max-width: 768px) {
    .task-image-wrapper,
    .image-preview-item {
        width: 120px;
        height: 120px;
    }
    
    .task-thumbnail {
        width: 60px;
        height: 60px;
    }
    
    .lightbox-nav {
        width: 40px;
        height: 40px;
    }
    
    .lightbox-prev {
        left: -50px;
    }
    
    .lightbox-next {
        right: -50px;
    }
}

/* Task Images Styling */
.task-images {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 5px;
    justify-content: flex-start;
    align-items: flex-start;
    width: 100%;
}

.task-image-container {
    position: relative;
    display: inline-block;
    margin: 5px;
    width: 100px;
    height: 100px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    overflow: hidden;
}

.task-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: pointer;
}

.delete-image-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.task-image-container:hover .delete-image-btn {
    opacity: 1;
}

.delete-image-btn:hover {
    background-color: rgba(220, 53, 69, 0.8);
}

/* Image Modal */
.image-modal {
    display: none;
    position: fixed;
    z-index: 1050;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    overflow: auto;
}

.image-modal-content {
    margin: auto;
    display: block;
    max-width: 90%;
    max-height: 90%;
    margin-top: 50px;
}

.image-modal-close {
    position: absolute;
    top: 15px;
    right: 35px;
    color: #f1f1f1;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
}

/* Image upload in task edit modal */
.image-upload-container {
    margin-top: 15px;
    border: 2px dashed #ddd;
    border-radius: 5px;
    padding: 15px;
    text-align: center;
    background-color: rgba(255, 255, 255, 0.05);
}

.image-upload-container:hover {
    border-color: #aaa;
    background-color: rgba(255, 255, 255, 0.1);
}

.image-upload-label {
    display: block;
    cursor: pointer;
    color: #aaa;
}

.image-upload-label i {
    font-size: 24px;
    margin-bottom: 10px;
}

.image-upload-preview {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
}

.preview-image-container {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: 4px;
    overflow: hidden;
}

.preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.remove-preview-btn {
    position: absolute;
    top: 2px;
    right: 2px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 10px;
}

.remove-preview-btn:hover {
    background-color: rgba(220, 53, 69, 0.8);
}

/* ... existing code ... */

/* Image Upload and Preview Styles */
.image-upload-container {
    margin-top: 15px;
    margin-bottom: 15px;
}

.image-upload-label {
    display: inline-block;
    padding: 8px 15px;
    background-color: #f8f9fa;
    border: 1px solid #ced4da;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.image-upload-label:hover {
    background-color: #e9ecef;
}

.image-upload-input {
    display: none;
}

/* Image Preview Styles */
.image-preview-container {
    margin-top: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.preview-image-container {
    position: relative;
    width: 100px;
    height: 100px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    overflow: hidden;
}

.preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.remove-preview-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 20px;
    height: 20px;
    background-color: rgba(255, 255, 255, 0.7);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
    color: #dc3545;
    padding: 0;
}

.remove-preview-btn:hover {
    background-color: rgba(255, 255, 255, 0.9);
    color: #bd2130;
}

/* Task Images in Task List */
.task-images {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-top: 5px;
}

.task-image-thumbnail {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: 4px;
    cursor: pointer;
    transition: transform 0.2s ease;
}

.task-image-thumbnail:hover {
    transform: scale(1.05);
}

/* Task Images in Edit Modal */
.task-image-container {
    position: relative;
    display: inline-block;
    margin: 5px;
    width: 100px;
    height: 100px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    overflow: hidden;
}

.task-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    cursor: pointer;
}

.delete-image-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 20px;
    height: 20px;
    background-color: rgba(255, 255, 255, 0.7);
    border: none;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
    color: #dc3545;
    padding: 0;
}

.delete-image-btn:hover {
    background-color: rgba(255, 255, 255, 0.9);
    color: #bd2130;
}

/* Image Modal */
.image-modal {
    display: none;
    position: fixed;
    z-index: 1050;
    padding-top: 50px;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.9);
}

.image-modal-content {
    margin: auto;
    display: block;
    max-width: 90%;
    max-height: 80vh;
}

.image-modal-close {
    position: absolute;
    top: 15px;
    right: 35px;
    color: #f1f1f1;
    font-size: 40px;
    font-weight: bold;
    transition: 0.3s;
    cursor: pointer;
}

.image-modal-close:hover,
.image-modal-close:focus {
    color: #bbb;
    text-decoration: none;
    cursor: pointer;
}

/* Clipboard Paste Hint */
.clipboard-paste-hint {
    margin-top: 10px;
    font-size: 0.85rem;
    color: #6c757d;
    font-style: italic;
}

/* Alert Container */
.alert-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 300px;
}

.alert-container .alert {
    margin-bottom: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Footer Styles */
.footer {
    background-color: #1f2937;
    color: #9ca3af;
    padding: 8px 0;
    margin-top: 30px;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
    flex-shrink: 0;
}

.footer .text-muted {
    color: #9ca3af !important;
    font-size: 0.9rem;
}

/* Header container and settings button */
.header-container {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 0;
}

.header-buttons {
    display: flex;
    align-items: center;
    gap: 10px;
}

.header-container h3 {
    margin: 0;
    z-index: 0;
    display: flex;
    align-items: center;
    height: 100%;
}

.title {
    color: #e0e0e0; /* Make the title more visible */
    font-weight: 500;
    display: inline-block;
}

.header-container .btn {
    position: relative;
    z-index: 1;
}

/* Statistics Cards */
.stats-container {
    margin-top: 1rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    padding: 0.8rem 0.5rem;
    text-align: center;
    transition: all 0.3s ease;
    margin-bottom: 0.75rem;
}

.stat-card:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.stat-number {
    font-size: 1.8rem;
    font-weight: bold;
    color: #007bff;
    margin-bottom: 0.25rem;
    line-height: 1;
}

/* Specific colors for different stat types */
#completedTasks {
    color: #28a745 !important;
}

#inDevelopmentTasks {
    color: #ffc107 !important;
}

#progressPercentage {
    color: #17a2b8 !important;
}

.stat-label {
    font-size: 0.65rem;
    color: #9ca3af;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    line-height: 1.1;
}

/* Responsive adjustments for stats */
@media (max-width: 768px) {
    .stat-card {
        padding: 0.6rem 0.4rem;
        margin-bottom: 0.5rem;
    }
    
    .stat-number {
        font-size: 1.5rem;
    }
    
    .stat-label {
        font-size: 0.6rem;
    }
}

.settings-btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.875rem;
    color: #9ca3af;
    background-color: transparent;
    border-color: rgba(255, 255, 255, 0.1);
    transition: all 0.2s ease;
}

.settings-btn:hover {
    color: #e0e0e0;
    background-color: rgba(255, 255, 255, 0.05);
    transform: rotate(30deg);
}

/* Update add task button to be more prominent */
#addTask {
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

#addTask:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Title styling */
.title {
    color: #e0e0e0; /* Make the title more visible */
    font-weight: 500;
}

/* Alert Container */
.alert-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 300px;
}

.alert-container .alert {
    margin-bottom: 10px;
}

/* Responsive header adjustments */
@media (max-width: 768px) {
    .header-container {
        flex-direction: column;
        gap: 15px;
    }
    
    .header-buttons {
        width: 100%;
        justify-content: flex-end;
    }
    
    #addTask {
        font-size: 0.9rem;
        padding: 0.4rem 0.8rem;
    }
}

/* User badge styling */
.badge-user {
    background: #374151;
    color: #9ca3af;
    font-weight: normal;
    font-size: 0.75em;
    padding: 0.35em 0.75em;
    border-radius: 3px;
    margin-right: 6px;
    border: 1px solid rgba(255,255,255,0.1);
    opacity: 0.8;
    height: 24px;
    display: inline-flex;
    align-items: center;
    min-width: 70px;
    justify-content: center;
}

.badge-user i {
    margin-right: 4px;
    font-size: 0.7rem;
}

@media (max-width: 576px) {
    .badge-date,
    .badge-user,
    .platform-badge {
        margin: 0 4px 4px 0;
    }
    
    .task-meta {
        flex-direction: row;
        flex-wrap: wrap;
        align-items: flex-start;
    }
}

/* Make sure task-label badges match other badges */
.task-label .badge {
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75em;
    padding: 0.35em 0.75em;
    border-radius: 3px;
    margin-right: 6px;
}

@media (max-width: 576px) {
    .badge-date,
    .badge-user,
    .platform-badge,
    .task-label .badge {
        margin: 0 4px 4px 0;
    }
    
    .task-meta {
        flex-direction: row;
        flex-wrap: wrap;
        align-items: flex-start;
    }
}

/* Mobile-specific styles to indicate drag and drop is disabled */
@media (max-width: 767px) {
    /* Reset cursor for tasks on mobile */
    .not-completed-task[data-label="Em Desenvolvimento"] {
        cursor: default;
    }
}

/* Completion section styling */
.completion-section {
    background: rgba(25, 135, 84, 0.1);
    border: 1px solid rgba(25, 135, 84, 0.2);
    border-radius: 6px;
    padding: 15px;
}

.completion-section label {
    color: #198754;
    font-weight: 500;
}

.completion-section .custom-checkbox .custom-control-input:checked ~ .custom-control-label::before {
    background-color: #198754;
    border-color: #198754;
}

.completion-section .custom-control-label {
    font-size: 1.1em;
    cursor: pointer;
}

.completion-section .custom-control-label:hover {
    color: #198754;
}

/* Modal stacking improvements */
.modal {
    background: none !important;
}

.modal-backdrop {
    opacity: 0.5;
}

/* When multiple modals are open */
.modal-backdrop + .modal-backdrop {
    opacity: 0.8;
}

/* Ensure proper z-index stacking for multiple modals */
.modal {
    z-index: 1050 !important;
}

.modal-backdrop {
    z-index: 1040 !important;
}

.modal.show {
    z-index: 1055 !important;
}

.modal.show .modal-dialog {
    z-index: 1056 !important;
}

/* Special handling for completion confirmation modal */
#completeConfirmModal {
    z-index: 1060 !important;
}

#completeConfirmModal .modal-dialog {
    z-index: 1061 !important;
}

#completeConfirmModal .modal-backdrop {
    z-index: 1059 !important;
}

/* Comments Section Styles */
.comments-section {
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    background: rgba(26, 35, 50, 0.5);
    padding: 1rem;
    margin-top: 0.5rem;
}

.comments-list {
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 1rem;
}

.comment-item {
    background: rgba(45, 55, 72, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 0.75rem;
    transition: all 0.2s ease;
}

.comment-item:hover {
    background: rgba(45, 55, 72, 1);
    border-color: rgba(255, 255, 255, 0.2);
}

.comment-item:last-child {
    margin-bottom: 0;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.85em;
}

.comment-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.comment-buttons {
    display: flex;
    gap: 0.25rem;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.comment-item:hover .comment-buttons {
    opacity: 1;
}

.comment-buttons .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 3px;
}

.comment-content {
    margin-top: 0.5rem;
}

.comment-edit-form {
    margin-top: 0.5rem;
    padding: 0.75rem;
    background: rgba(26, 35, 50, 0.8);
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.comment-edit-form .form-control {
    background: rgba(45, 55, 72, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #e5e7eb;
    font-size: 0.85rem;
}

.comment-edit-form .form-control:focus {
    background: rgba(45, 55, 72, 1);
    border-color: #60a5fa;
    box-shadow: 0 0 0 0.2rem rgba(96, 165, 250, 0.25);
}

.comment-edit-form .btn {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    margin-right: 0.25rem;
}

/* Comment indicators on task list */
.comment-indicator {
    display: inline-flex;
    align-items: center;
    background: rgba(96, 165, 250, 0.2);
    color: #60a5fa;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    margin-right: 0.5rem;
    border: 1px solid rgba(96, 165, 250, 0.3);
    cursor: help;
    transition: all 0.2s ease;
}

.comment-indicator:hover {
    background: rgba(96, 165, 250, 0.3);
    border-color: rgba(96, 165, 250, 0.5);
    transform: scale(1.05);
}

.comment-indicator i {
    margin-right: 0.25rem;
    font-size: 0.7rem;
}

.comment-count {
    font-weight: 600;
    min-width: 1rem;
    text-align: center;
}

.comment-author {
    color: #60a5fa;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.comment-author i {
    margin-right: 0.5rem;
    color: #93c5fd;
}

.comment-timestamp {
    color: #9ca3af;
    font-size: 0.8em;
}

.comment-text {
    color: #e5e7eb;
    line-height: 1.4;
    word-wrap: break-word;
}

.add-comment-form {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 1rem;
}

.add-comment-form .form-control {
    background: rgba(26, 35, 50, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #e5e7eb;
}

.add-comment-form .form-control:focus {
    background: rgba(26, 35, 50, 1);
    border-color: #60a5fa;
    box-shadow: 0 0 0 0.2rem rgba(96, 165, 250, 0.25);
}

.add-comment-form .form-control::placeholder {
    color: #9ca3af;
}

.comments-empty {
    text-align: center;
    color: #6b7280;
    font-style: italic;
    padding: 2rem 1rem;
    border: 2px dashed rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    margin-bottom: 1rem;
}

.comments-empty i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    opacity: 0.5;
}

/* Responsive adjustments for comments */
@media (max-width: 768px) {
    .add-comment-form .form-row {
        margin: 0;
    }
    
    .add-comment-form .col-md-4,
    .add-comment-form .col-md-6,
    .add-comment-form .col-md-2 {
        padding: 0 0.25rem;
        margin-bottom: 0.5rem;
    }
    
    .add-comment-form .col-md-2 {
        flex: 0 0 100%;
        max-width: 100%;
    }
    
    .comment-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
    
    .comments-list {
        max-height: 200px;
    }
}

/* Task comment preview styles */
.task-comments-preview {
    margin-bottom: 8px;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(37, 99, 235, 0.05));
    border-radius: 6px;
    border: 1px solid rgba(59, 130, 246, 0.2);
    padding: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.comment-preview-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    margin-bottom: 8px;
    padding: 6px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    border-left: 3px solid #3b82f6;
}

.comment-preview-item:last-child {
    margin-bottom: 0;
}

.comment-icon {
    color: #3b82f6;
    font-size: 0.8rem;
    margin-top: 2px;
    flex-shrink: 0;
}

.comment-preview-content {
    flex: 1;
    min-width: 0;
}

.comment-preview-author {
    font-size: 0.7rem;
    color: #60a5fa;
    font-weight: 600;
    margin-bottom: 2px;
    display: block;
}

.comment-preview-author i {
    font-size: 0.65rem;
    margin-right: 4px;
    opacity: 0.8;
}

.comment-preview-text {
    font-size: 0.75rem;
    color: #e2e8f0;
    line-height: 1.3;
    margin: 0;
    word-break: break-word;
    max-height: 2.6em;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    font-style: italic;
}

/* Responsive adjustments for comment previews */
@media (max-width: 768px) {
    .task-comments-preview {
        padding: 8px;
    }
    
    .comment-preview-item {
        padding: 4px;
        gap: 6px;
    }
    
    .comment-icon {
        font-size: 0.7rem;
    }
    
    .comment-preview-author {
        font-size: 0.65rem;
    }
    
    .comment-preview-author i {
        font-size: 0.6rem;
    }
    
    .comment-preview-text {
        font-size: 0.7rem;
        -webkit-line-clamp: 1;
        max-height: 1.3em;
    }
}

/* Progress Bar Styles */
.task-progress-container {
    display: flex !important;
    align-items: center;
    gap: 8px;
    margin-top: 8px;
    margin-bottom: 8px;
    width: 100%;
    position: relative;
}

.task-progress-bar {
    flex: 1;
    height: 6px;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 3px;
    overflow: hidden;
    position: relative;
    border: 1px solid rgba(0, 0, 0, 0.4);
    min-width: 100px;
}

.task-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #059669, #10b981);
    border-radius: 3px;
    transition: width 0.3s ease;
    position: relative;
    box-shadow: 0 1px 3px rgba(5, 150, 105, 0.4);
}

.task-progress-text {
    font-size: 0.7rem;
    color: #10b981;
    font-weight: 600;
    position: absolute;
    top: -20px;
    background: rgba(0, 0, 0, 0.9);
    padding: 2px 5px;
    border-radius: 3px;
    white-space: nowrap;
    z-index: 10;
    border: 1px solid rgba(16, 185, 129, 0.3);
    transform: translateX(-50%);
    pointer-events: none;
}

.completed-task .task-progress-container {
    display: none;
}

/* Progress Slider Styles */
.progress-slider-container {
    margin-top: 8px;
}

.form-control-range {
    width: 100%;
    height: 6px;
    background: transparent;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
    border-radius: 3px;
    background: rgba(255, 255, 255, 0.1);
    margin-bottom: 8px;
}

.form-control-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    background: #059669;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.form-control-range::-webkit-slider-thumb:hover {
    background: #10b981;
    transform: scale(1.1);
}

.form-control-range::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: #059669;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.form-control-range::-moz-range-thumb:hover {
    background: #10b981;
    transform: scale(1.1);
}

.form-control-range::-ms-thumb {
    width: 18px;
    height: 18px;
    background: #059669;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.form-control-range::-webkit-slider-track {
    background: linear-gradient(90deg, #059669 0%, #059669 var(--progress, 0%), rgba(255, 255, 255, 0.1) var(--progress, 0%), rgba(255, 255, 255, 0.1) 100%);
    height: 6px;
    border-radius: 3px;
}

.form-control-range::-moz-range-track {
    background: rgba(255, 255, 255, 0.1);
    height: 6px;
    border-radius: 3px;
    border: none;
}

.form-control-range::-moz-range-progress {
    background: #059669;
    height: 6px;
    border-radius: 3px;
}

.progress-slider-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.75rem;
    color: #9ca3af;
}

.progress-slider-labels span:nth-child(2) {
    font-weight: 600;
    color: #059669;
}

@media (max-width: 768px) {
    .task-progress-container {
        gap: 6px;
        margin-top: 6px;
    }
    
    .task-progress-bar {
        height: 4px;
    }
    
    .task-progress-text {
        font-size: 0.65rem;
        top: -18px;
        padding: 1px 4px;
    }
    
    .form-control-range::-webkit-slider-thumb {
        width: 16px;
        height: 16px;
    }
    
    .form-control-range::-moz-range-thumb {
        width: 16px;
        height: 16px;
    }
}
