# GPS Trajectory and Image Processing Fixes

## Issues Identified

### 1. GPS Trajectory Fluctuations
**Problem**: GPS accuracy degrades when phone screen turns off, causing trajectory fluctuations when screen wakes up.

**Root Causes**:
- Screen timeout causes GPS to enter power-saving mode
- GPS signal weakens when screen is off
- Poor accuracy readings immediately after screen wake
- No filtering for post-wake GPS re-acquisition period

### 2. Image Processing Delays
**Problem**: Adding contacts with images causes delays during "OK" button press due to immediate image processing/upload.

**Root Causes**:
- Images were being processed/uploaded immediately during contact creation
- Network operations blocking UI responsiveness
- No deferred upload system for offline scenarios

## Solutions Implemented

### 1. GPS Trajectory Fixes

#### A. Screen Timeout Prevention
```typescript
// Added expo-keep-awake to prevent screen timeout during monitoring
import { activateKeepAwakeAsync, deactivateKeepAwake } from 'expo-keep-awake';

useEffect(() => {
  const enableKeepAwake = async () => {
    try {
      await activateKeepAwakeAsync();
      console.log('📱 Screen timeout prevention activated');
    } catch (error) {
      console.error('❌ Failed to activate keep awake:', error);
    }
  };

  enableKeepAwake();

  return () => {
    deactivateKeepAwake();
    console.log('📱 Screen timeout prevention deactivated');
  };
}, []);
```

#### B. App State Monitoring
```typescript
// Monitor app state changes to detect screen wake/sleep
useEffect(() => {
  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (appState.match(/inactive|background/) && nextAppState === 'active') {
      // Screen woke up - GPS might need time to re-acquire accuracy
      screenWakeTime.current = Date.now();
      setIsScreenAwake(true);
    } else if (nextAppState.match(/inactive|background/)) {
      // Screen went to sleep
      setIsScreenAwake(false);
    }
    setAppState(nextAppState);
  };

  const subscription = AppState.addEventListener('change', handleAppStateChange);
  return () => subscription?.remove();
}, [appState]);
```

#### C. Enhanced GPS Filtering
```typescript
// Check if we're in post-wake GPS re-acquisition period
const timeSinceWake = currentTime - screenWakeTime.current;
const isPostWakeAcquisition = timeSinceWake < 30000; // 30 seconds after wake

// Apply stricter filtering after screen wake to prevent fluctuations
if (isPostWakeAcquisition) {
  adjustedFilterConfig = {
    ...adjustedFilterConfig,
    maxAccuracy: accuracy && accuracy > 30 ? 80 : 50, // Allow poor accuracy briefly after wake
    maxJumpDistance: 100, // Smaller jump threshold after wake
    minTimeInterval: 3000, // Require longer intervals after wake
  };
}
```

#### D. Improved Location Tracking
- Increased GPS update frequency: `timeInterval: 2000ms` (from 3000ms)
- Reduced distance threshold: `distanceInterval: 3m` (from 5m)
- Better accuracy tracking and classification

### 2. Image Processing Optimization

#### A. Deferred Image Upload
**Before**: Images were processed/uploaded immediately during contact creation
```typescript
// OLD: Immediate processing causing delays
if (netInfo.isConnected && images && images.length > 0) {
  // Upload images immediately - CAUSES DELAYS
  for (let i = 0; i < images.length; i++) {
    // ... upload logic blocking UI
  }
}
```

**After**: Images are saved locally and processed in background
```typescript
// NEW: Fast save with deferred processing
if (images && images.length > 0) {
  console.log(`📸 Fast save: Storing ${images.length} local image paths for background sync...`);
  imagesToSave = [...images]; // Save local paths for later processing
}
```

#### B. Background Sync Enhancement
- Added retry logic with exponential backoff for image uploads
- Improved error handling and logging
- Better progress feedback for users

#### C. UI Improvements
- Added sync indicators on image previews (clock icon)
- Informational message: "Fotos serão enviadas em segundo plano quando houver internet"
- Removed loading states that were blocking user interaction

## Benefits

### GPS Trajectory Improvements
1. **Consistent Accuracy**: Screen stays awake during monitoring, maintaining GPS accuracy
2. **Reduced Fluctuations**: Post-wake filtering prevents erratic GPS readings
3. **Better Path Quality**: More frequent updates with improved filtering
4. **Automatic Recovery**: System adapts to poor accuracy periods automatically

### Image Processing Improvements  
1. **Instant Response**: Contact creation is immediate, no waiting for image processing
2. **Better UX**: Users can continue monitoring without delays
3. **Reliable Upload**: Background sync with retry logic ensures images are eventually uploaded
4. **Clear Feedback**: Users understand when images will be processed

## Technical Details

### Files Modified
- `components/ActiveMonitoring.tsx` - GPS improvements and screen wake handling
- `components/GestorActiveMonitoring.tsx` - Same GPS improvements for gestor users
- `components/ContactDetailsModal.tsx` - Deferred image processing and UI improvements
- `app/contact-details.tsx` - Removed immediate image upload
- `services/monitoringSyncService.ts` - Enhanced background sync with retry logic

### Dependencies Added
- `expo-keep-awake` - Prevents screen timeout during monitoring

### Configuration Changes
- GPS update intervals optimized for better accuracy
- Filtering thresholds adjusted for post-wake scenarios
- Background sync improved with retry mechanisms

## Testing Recommendations

### GPS Trajectory Testing
1. Start monitoring session
2. Let screen turn off (or manually turn off)
3. Wait 10-30 seconds
4. Turn screen back on
5. Verify trajectory doesn't show large jumps or fluctuations
6. Check console logs for post-wake filtering messages

### Image Processing Testing
1. Start monitoring session
2. Add contact with multiple images
3. Press "OK" button
4. Verify immediate response (no delays)
5. Check that images show sync indicators
6. Verify background sync occurs when internet available

## Future Enhancements

1. **GPS Accuracy Visualization**: Add real-time GPS accuracy indicator in UI
2. **Sync Progress**: Show progress bar for background image uploads
3. **Smart Filtering**: Machine learning-based GPS spike detection
4. **Battery Optimization**: Adaptive GPS settings based on battery level 